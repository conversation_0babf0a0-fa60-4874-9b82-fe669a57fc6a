#!/usr/bin/env python3
"""
Coordinate Transformation Validation Test

This script validates the bidirectional coordinate transformations used in the
nodule detection pipeline by:
1. Starting with a DICOM pixel coordinate
2. Converting to world coordinates using DICOM parameters
3. Converting to NIfTI voxel coordinates using affine matrix
4. Converting back to validate round-trip accuracy

Uses real parameters from the test series data.
"""

import numpy as np
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_coordinate_transformation():
    """
    Test bidirectional coordinate transformation using real series parameters.
    """
    logger.info("=" * 80)
    logger.info("COORDINATE TRANSFORMATION VALIDATION TEST")
    logger.info("=" * 80)
    
    # Real parameters from test series 20131104_78091c4ef799c7360673
    # From the debug output we collected
    
    # DICOM parameters (from slice metadata)
    pixel_spacing = [0.69140625, 0.69140625]  # [row_spacing, col_spacing] = [Y, X] in mm
    image_position_patient = [-192.6063, -0.**********, 23.0]  # [X, Y, Z] in LPS coordinates (mm)
    slice_location = 23.0  # Z position in mm
    
    # NIfTI affine matrix (from transformation record)
    # This is the corrected simple affine with positive diagonal for RAS orientation
    affine_matrix = np.array([
        [0.69140625, 0.0, 0.0, 192.6063],         # X: spacing=0.691, origin=192.6063
        [0.0, 0.69140625, 0.0, 0.**********],     # Y: spacing=0.691, origin=0.055
        [0.0, 0.0, 3.0, -1.0],                    # Z: spacing=3.0, origin=-1.0
        [0.0, 0.0, 0.0, 1.0]                      # Homogeneous coordinates
    ])
    
    # Volume parameters
    volume_shape = [117, 512, 512]  # [Z, Y, X] - depth, height, width
    resampled_spacing = [0.7, 0.7, 1.25]  # [X, Y, Z] target spacing
    
    logger.info("PARAMETERS:")
    logger.info(f"  DICOM pixel spacing [Y,X]: {pixel_spacing} mm")
    logger.info(f"  DICOM ImagePositionPatient [X,Y,Z]: {image_position_patient} mm (LPS)")
    logger.info(f"  Slice location: {slice_location} mm")
    logger.info(f"  Volume shape [Z,Y,X]: {volume_shape}")
    logger.info(f"  Resampled spacing [X,Y,Z]: {resampled_spacing} mm")
    logger.info(f"  NIfTI affine matrix:\n{affine_matrix}")
    
    # Test points: Choose representative DICOM pixel coordinates
    test_points_dicom = [
        [256, 256],  # Center of 512x512 image
        [100, 150],  # Arbitrary point
        [400, 300],  # Another arbitrary point
        [0, 0],      # Top-left corner
        [511, 511],  # Bottom-right corner
    ]
    
    logger.info("\n" + "=" * 80)
    logger.info("FORWARD AND REVERSE TRANSFORMATION TESTS")
    logger.info("=" * 80)
    
    for i, (pixel_x, pixel_y) in enumerate(test_points_dicom):
        logger.info(f"\nTEST POINT {i+1}: DICOM pixel coordinates ({pixel_x}, {pixel_y})")
        logger.info("-" * 60)
        
        # =======================================================================
        # STEP 1: DICOM pixel coordinates → DICOM world coordinates (LPS)
        # =======================================================================
        logger.info("STEP 1: DICOM pixel → DICOM world (LPS)")
        
        # Convert DICOM pixel to world coordinates
        # CRITICAL: pixel_spacing = [row_spacing, col_spacing] = [Y_spacing, X_spacing]
        # X direction uses column spacing (index 1), Y direction uses row spacing (index 0)
        world_x_lps = image_position_patient[0] + pixel_x * pixel_spacing[1]  # X uses column spacing
        world_y_lps = image_position_patient[1] + pixel_y * pixel_spacing[0]  # Y uses row spacing  
        world_z_lps = slice_location  # Z is the slice location
        
        dicom_world_lps = np.array([world_x_lps, world_y_lps, world_z_lps])
        
        logger.info(f"  DICOM pixel: ({pixel_x}, {pixel_y}) pixels")
        logger.info(f"  → DICOM world (LPS): ({world_x_lps:.3f}, {world_y_lps:.3f}, {world_z_lps:.3f}) mm")
        
        # =======================================================================
        # STEP 2: DICOM world (LPS) → NIfTI world (RAS)
        # =======================================================================
        logger.info("STEP 2: DICOM world (LPS) → NIfTI world (RAS)")
        
        # Convert LPS to RAS coordinate system
        # LPS (Left-Posterior-Superior) → RAS (Right-Anterior-Superior)
        # RAS = [-LPS_X, -LPS_Y, LPS_Z]
        world_x_ras = -world_x_lps  # Left → Right (flip X)
        world_y_ras = -world_y_lps  # Posterior → Anterior (flip Y)
        world_z_ras = world_z_lps   # Superior unchanged
        
        nifti_world_ras = np.array([world_x_ras, world_y_ras, world_z_ras])
        
        logger.info(f"  DICOM world (LPS): ({world_x_lps:.3f}, {world_y_lps:.3f}, {world_z_lps:.3f}) mm")
        logger.info(f"  → NIfTI world (RAS): ({world_x_ras:.3f}, {world_y_ras:.3f}, {world_z_ras:.3f}) mm")
        
        # =======================================================================
        # STEP 3: NIfTI world (RAS) → NIfTI voxel coordinates
        # =======================================================================
        logger.info("STEP 3: NIfTI world (RAS) → NIfTI voxel")
        
        # Convert world coordinates to voxel coordinates using inverse affine
        # Add homogeneous coordinate
        world_homogeneous = np.array([world_x_ras, world_y_ras, world_z_ras, 1.0])
        
        # Apply inverse affine transformation
        affine_inverse = np.linalg.inv(affine_matrix)
        voxel_homogeneous = affine_inverse @ world_homogeneous
        voxel_coords = voxel_homogeneous[:3]
        
        voxel_x, voxel_y, voxel_z = voxel_coords
        
        logger.info(f"  NIfTI world (RAS): ({world_x_ras:.3f}, {world_y_ras:.3f}, {world_z_ras:.3f}) mm")
        logger.info(f"  → NIfTI voxel: ({voxel_x:.3f}, {voxel_y:.3f}, {voxel_z:.3f})")
        
        # Validate voxel coordinates are within volume bounds
        x_valid = 0 <= voxel_x <= volume_shape[2]  # X within width
        y_valid = 0 <= voxel_y <= volume_shape[1]  # Y within height  
        z_valid = 0 <= voxel_z <= volume_shape[0]  # Z within depth
        
        logger.info(f"  Bounds check: X={x_valid} [0,{volume_shape[2]}], Y={y_valid} [0,{volume_shape[1]}], Z={z_valid} [0,{volume_shape[0]}]")
        
        # =======================================================================
        # STEP 4: REVERSE TRANSFORMATION - NIfTI voxel → NIfTI world (RAS)
        # =======================================================================
        logger.info("STEP 4: REVERSE - NIfTI voxel → NIfTI world (RAS)")
        
        # Convert voxel back to world coordinates using forward affine
        voxel_homogeneous_reverse = np.array([voxel_x, voxel_y, voxel_z, 1.0])
        world_homogeneous_reverse = affine_matrix @ voxel_homogeneous_reverse
        world_coords_reverse = world_homogeneous_reverse[:3]
        
        world_x_ras_reverse, world_y_ras_reverse, world_z_ras_reverse = world_coords_reverse
        
        logger.info(f"  NIfTI voxel: ({voxel_x:.3f}, {voxel_y:.3f}, {voxel_z:.3f})")
        logger.info(f"  → NIfTI world (RAS): ({world_x_ras_reverse:.3f}, {world_y_ras_reverse:.3f}, {world_z_ras_reverse:.3f}) mm")
        
        # =======================================================================
        # STEP 5: REVERSE TRANSFORMATION - NIfTI world (RAS) → DICOM world (LPS)
        # =======================================================================
        logger.info("STEP 5: REVERSE - NIfTI world (RAS) → DICOM world (LPS)")
        
        # Convert RAS back to LPS coordinate system
        # RAS (Right-Anterior-Superior) → LPS (Left-Posterior-Superior)  
        # LPS = [-RAS_X, -RAS_Y, RAS_Z]
        world_x_lps_reverse = -world_x_ras_reverse  # Right → Left (flip X)
        world_y_lps_reverse = -world_y_ras_reverse  # Anterior → Posterior (flip Y)
        world_z_lps_reverse = world_z_ras_reverse   # Superior unchanged
        
        logger.info(f"  NIfTI world (RAS): ({world_x_ras_reverse:.3f}, {world_y_ras_reverse:.3f}, {world_z_ras_reverse:.3f}) mm")
        logger.info(f"  → DICOM world (LPS): ({world_x_lps_reverse:.3f}, {world_y_lps_reverse:.3f}, {world_z_lps_reverse:.3f}) mm")
        
        # =======================================================================
        # STEP 6: REVERSE TRANSFORMATION - DICOM world (LPS) → DICOM pixel
        # =======================================================================
        logger.info("STEP 6: REVERSE - DICOM world (LPS) → DICOM pixel")
        
        # Convert world coordinates back to pixel coordinates
        pixel_x_reverse = (world_x_lps_reverse - image_position_patient[0]) / pixel_spacing[1]  # X uses column spacing
        pixel_y_reverse = (world_y_lps_reverse - image_position_patient[1]) / pixel_spacing[0]  # Y uses row spacing
        
        logger.info(f"  DICOM world (LPS): ({world_x_lps_reverse:.3f}, {world_y_lps_reverse:.3f}, {world_z_lps_reverse:.3f}) mm")
        logger.info(f"  → DICOM pixel: ({pixel_x_reverse:.3f}, {pixel_y_reverse:.3f}) pixels")
        
        # =======================================================================
        # STEP 7: VALIDATION - Compare original and round-trip results
        # =======================================================================
        logger.info("STEP 7: ROUND-TRIP VALIDATION")
        
        # Calculate differences
        pixel_x_diff = abs(pixel_x - pixel_x_reverse)
        pixel_y_diff = abs(pixel_y - pixel_y_reverse)
        world_x_diff = abs(world_x_lps - world_x_lps_reverse)
        world_y_diff = abs(world_y_lps - world_y_lps_reverse)
        world_z_diff = abs(world_z_lps - world_z_lps_reverse)
        
        logger.info(f"  Original DICOM pixel: ({pixel_x}, {pixel_y})")
        logger.info(f"  Round-trip DICOM pixel: ({pixel_x_reverse:.3f}, {pixel_y_reverse:.3f})")
        logger.info(f"  Pixel differences: ΔX={pixel_x_diff:.6f}, ΔY={pixel_y_diff:.6f}")
        
        logger.info(f"  Original DICOM world: ({world_x_lps:.3f}, {world_y_lps:.3f}, {world_z_lps:.3f})")
        logger.info(f"  Round-trip DICOM world: ({world_x_lps_reverse:.3f}, {world_y_lps_reverse:.3f}, {world_z_lps_reverse:.3f})")
        logger.info(f"  World differences: ΔX={world_x_diff:.6f}, ΔY={world_y_diff:.6f}, ΔZ={world_z_diff:.6f}")
        
        # Determine success (tolerance: 1e-6 for numerical precision)
        tolerance = 1e-6
        pixel_success = pixel_x_diff < tolerance and pixel_y_diff < tolerance
        world_success = world_x_diff < tolerance and world_y_diff < tolerance and world_z_diff < tolerance
        
        if pixel_success and world_success:
            logger.info("  ✅ ROUND-TRIP VALIDATION: SUCCESS (within numerical tolerance)")
        else:
            logger.info("  ❌ ROUND-TRIP VALIDATION: FAILED (exceeds tolerance)")
            if not pixel_success:
                logger.info(f"     Pixel coordinate mismatch: {max(pixel_x_diff, pixel_y_diff):.8f} > {tolerance}")
            if not world_success:
                logger.info(f"     World coordinate mismatch: {max(world_x_diff, world_y_diff, world_z_diff):.8f} > {tolerance}")

    logger.info("\n" + "=" * 80)
    logger.info("TRANSFORMATION MATRIX VALIDATION")
    logger.info("=" * 80)
    
    # Validate affine matrix properties
    logger.info("Affine matrix analysis:")
    determinant = np.linalg.det(affine_matrix[:3, :3])
    logger.info(f"  Determinant: {determinant:.6f} (should be positive for proper orientation)")
    
    # Check if matrix is invertible
    try:
        affine_inverse = np.linalg.inv(affine_matrix)
        logger.info("  ✅ Matrix is invertible")
        
        # Verify inverse by multiplying with original
        identity_check = affine_matrix @ affine_inverse
        identity_diff = np.max(np.abs(identity_check - np.eye(4)))
        if identity_diff < 1e-10:
            logger.info(f"  ✅ Inverse verification: SUCCESS (max diff: {identity_diff:.2e})")
        else:
            logger.info(f"  ❌ Inverse verification: FAILED (max diff: {identity_diff:.2e})")
            
    except np.linalg.LinAlgError:
        logger.info("  ❌ Matrix is not invertible")
    
    # Extract spacing from affine matrix
    spacing_x = np.linalg.norm(affine_matrix[:3, 0])
    spacing_y = np.linalg.norm(affine_matrix[:3, 1]) 
    spacing_z = np.linalg.norm(affine_matrix[:3, 2])
    
    logger.info(f"  Extracted spacing from matrix: X={spacing_x:.6f}, Y={spacing_y:.6f}, Z={spacing_z:.6f}")
    logger.info(f"  Expected spacing (DICOM): X={pixel_spacing[1]:.6f}, Y={pixel_spacing[0]:.6f}, Z=3.0")
    
    logger.info("\n" + "=" * 80)
    logger.info("COORDINATE TRANSFORMATION VALIDATION COMPLETE")
    logger.info("=" * 80)

if __name__ == "__main__":
    test_coordinate_transformation()