#!/usr/bin/env python3
"""
CT Lung Nodule Detection Script using Duke AI-Lung-Health-Benchmarking Models

This script processes DICOM CT scans, converts them to the format required by the
MONAI RetinaNet detection model from the ai-lung-health-benchmarking submodule,
performs nodule detection, and outputs results to CSV format.

"""

import argparse
import logging
import sys
import time
import warnings
from pathlib import Path

warnings.filterwarnings("ignore")

# MONAI imports
import nibabel as nib
import numpy as np
import pandas as pd
import pydicom
import torch
from monai.apps.detection.networks.retinanet_detector import RetinaNetDetector
from monai.apps.detection.networks.retinanet_network import (
    RetinaNet,
    resnet_fpn_feature_extractor,
)
from monai.apps.detection.utils.anchor_utils import AnchorGeneratorWithAnchorShape
from monai.networks.nets import resnet
from monai.transforms import (
    Compose,
    EnsureChannelFirst,
    EnsureType,
    LoadImage,
    ScaleIntensityRange,
    Spacing,
    ToTensor,
)
from tqdm import tqdm

# Add ai-lung-health-benchmarking to path for imports
sys.path.append(
    str(Path(__file__).parent.parent.parent / "ai-lung-health-benchmarking")
)

# Import clarion utilities for data paths
from clarion.utils import DATA_DIR


def setup_logging(output_dir: Path) -> logging.Logger:
    """Set up logging configuration."""
    log_file = output_dir / f"nodule_detection_{time.strftime('%Y%m%d_%H%M%S')}.log"

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[logging.FileHandler(log_file), logging.StreamHandler()],
    )

    logger = logging.getLogger(__name__)
    logger.info("Starting lung nodule detection pipeline")
    logger.info(f"Log file: {log_file}")

    return logger


class DICOMProcessor:
    """Handles DICOM file processing and conversion to NIfTI format."""

    def __init__(self, logger: logging.Logger):
        self.logger = logger

    def iterate_series_from_csv(self, csv_path: Path):
        """
        Generator that yields DICOM series information one at a time from pre-filtered CSV file.
        This avoids loading all series metadata upfront, allowing immediate processing.

        Yields:
            Tuple of (series_uid, series_data_dict) for each useful series
        """
        try:
            # Load the CSV file
            df = pd.read_csv(csv_path)
            self.logger.info(f"Loaded CSV with {len(df)} rows")

            # Filter for useful series only
            useful_series = df[df["useful_series"] == 1].copy()
            self.logger.info(f"Found {len(useful_series)} useful series to process")

            for _, row in useful_series.iterrows():
                try:
                    # Construct series directory path
                    series_dir = (
                        DATA_DIR
                        / "raw"
                        / "lung_ct_scans"
                        / row["study_folder"]
                        / row["series_folder"]
                    )

                    if not series_dir.exists():
                        self.logger.warning(f"Series directory not found: {series_dir}")
                        continue

                    # Find all DICOM files in the series directory
                    dicom_files = list(series_dir.glob("*.dcm"))

                    if not dicom_files:
                        self.logger.warning(f"No DICOM files found in: {series_dir}")
                        continue

                    # Sort files by instance number
                    files_with_instance = []
                    for dicom_file in dicom_files:
                        try:
                            ds = pydicom.dcmread(
                                str(dicom_file), stop_before_pixels=True
                            )
                            instance_num = ds.get("InstanceNumber", 0)
                            files_with_instance.append((instance_num, dicom_file))
                        except Exception as e:
                            self.logger.warning(
                                f"Failed to read DICOM file {dicom_file}: {e}"
                            )

                    # Sort by instance number and extract file paths
                    files_with_instance.sort(key=lambda x: x[0])
                    sorted_files = [f[1] for f in files_with_instance]

                    # Use series_uid as key, or generate one if missing
                    series_uid = row.get(
                        "series_uid",
                        f"series_{row['study_folder']}_{row['series_folder']}",
                    )

                    series_data = {
                        "files": sorted_files,
                        "study_folder": row["study_folder"],
                        "series_folder": row["series_folder"],
                        "csv_row": row.to_dict(),  # Store the original CSV row data
                    }

                    self.logger.info(
                        f"Processing series {series_uid} with {len(sorted_files)} files"
                    )

                    yield series_uid, series_data

                except Exception as e:
                    self.logger.error(f"Failed to process series from CSV row: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"Failed to load CSV file {csv_path}: {e}")
            return

    def find_dicom_series_from_csv(self, csv_path: Path) -> dict[str, dict]:
        """
        Load DICOM series information from pre-filtered CSV file.
        DEPRECATED: Use iterate_series_from_csv() for better performance.

        Returns:
            Dict mapping series_uid to series info including file paths
        """
        try:
            # Load the CSV file
            df = pd.read_csv(csv_path)
            self.logger.info(f"Loaded CSV with {len(df)} rows")

            # Filter for useful series only
            useful_series = df[df["useful_series"] == 1].copy()
            self.logger.info(f"Found {len(useful_series)} useful series")

            series_info = {}

            for _, row in useful_series.iterrows():
                try:
                    # Construct series directory path
                    series_dir = (
                        DATA_DIR
                        / "raw"
                        / "lung_ct_scans"
                        / row["study_folder"]
                        / row["series_folder"]
                    )

                    if not series_dir.exists():
                        self.logger.warning(f"Series directory not found: {series_dir}")
                        continue

                    # Find all DICOM files in the series directory
                    dicom_files = list(series_dir.glob("*.dcm"))

                    if not dicom_files:
                        self.logger.warning(f"No DICOM files found in: {series_dir}")
                        continue

                    # Sort files by instance number
                    files_with_instance = []
                    for dicom_file in dicom_files:
                        try:
                            ds = pydicom.dcmread(
                                str(dicom_file), stop_before_pixels=True
                            )
                            instance_num = ds.get("InstanceNumber", 0)
                            files_with_instance.append((instance_num, dicom_file))
                        except Exception as e:
                            self.logger.warning(
                                f"Failed to read DICOM file {dicom_file}: {e}"
                            )

                    # Sort by instance number and extract file paths
                    files_with_instance.sort(key=lambda x: x[0])
                    sorted_files = [f[1] for f in files_with_instance]

                    # Use series_uid as key, or generate one if missing
                    series_uid = row.get(
                        "series_uid",
                        f"series_{row['study_folder']}_{row['series_folder']}",
                    )

                    series_info[series_uid] = {
                        "files": sorted_files,
                        "study_folder": row["study_folder"],
                        "series_folder": row["series_folder"],
                        "csv_row": row.to_dict(),  # Store the original CSV row data
                    }

                    self.logger.info(
                        f"Loaded series {series_uid} with {len(sorted_files)} files"
                    )

                except Exception as e:
                    self.logger.error(f"Failed to process series from CSV row: {e}")
                    continue
            self.logger.info(f"Successfully loaded {len(series_info)} series from CSV")
            return series_info

        except Exception as e:
            self.logger.error(f"Failed to load CSV file {csv_path}: {e}")
            return {}

    def dicom_to_nifti(self, dicom_files: list[Path], output_path: Path) -> dict | None:
        """
        Convert DICOM series to NIfTI format with medical-grade robustness.

        This implementation ensures 100% accurate anatomical reconstruction with:
        - Proper slice ordering using multiple criteria
        - Robust Z-spacing calculation from all slices
        - Correct affine matrix construction with ImageOrientationPatient
        - Comprehensive validation of all DICOM parameters

        Returns:
            Dictionary with metadata if successful, None otherwise
        """
        try:
            self.logger.info(
                f"Converting {len(dicom_files)} DICOM files to NIfTI with medical-grade validation"
            )

            # STEP 1: Collect comprehensive slice information from ALL files
            slice_data = []
            failed_files = []

            for i, dicom_file in enumerate(dicom_files):
                try:
                    # Read DICOM header and pixel data
                    ds = pydicom.dcmread(str(dicom_file))

                    # Validate essential DICOM tags
                    if not hasattr(ds, "pixel_array"):
                        raise ValueError(f"No pixel data in {dicom_file}")

                    # Get spatial information with robust fallbacks
                    slice_location = ds.get("SliceLocation", None)
                    image_position = ds.get("ImagePositionPatient", None)
                    instance_number = ds.get("InstanceNumber", i + 1)

                    # Calculate Z-position from ImagePositionPatient if SliceLocation missing
                    if slice_location is None and image_position is not None:
                        slice_location = float(image_position[2])
                        self.logger.debug(
                            f"Using ImagePositionPatient[2] as slice location: {slice_location}"
                        )
                    elif slice_location is None:
                        # Last resort: use instance number as relative position
                        slice_location = float(instance_number)
                        self.logger.warning(
                            f"Using InstanceNumber as slice location for {dicom_file}"
                        )

                    # Get pixel array and convert to HU
                    pixel_array = ds.pixel_array.astype(np.float32)
                    slope = float(ds.get("RescaleSlope", 1.0))
                    intercept = float(ds.get("RescaleIntercept", 0.0))

                    # Validate rescale parameters
                    if slope <= 0:
                        self.logger.warning(
                            f"Invalid RescaleSlope {slope} in {dicom_file}, using 1.0"
                        )
                        slope = 1.0

                    # Convert to Hounsfield Units
                    pixel_array = pixel_array * slope + intercept

                    slice_info = {
                        "pixel_array": pixel_array,
                        "slice_location": float(slice_location),
                        "instance_number": int(instance_number),
                        "image_position_patient": image_position,
                        "image_orientation_patient": ds.get(
                            "ImageOrientationPatient", None
                        ),
                        "pixel_spacing": ds.get("PixelSpacing", None),
                        "acquisition_time": ds.get("AcquisitionTime", ""),
                        "dicom_file": str(dicom_file),
                        "dicom_dataset": ds,  # Keep reference for metadata
                    }

                    slice_data.append(slice_info)

                except Exception as e:
                    self.logger.error(f"Failed to process DICOM file {dicom_file}: {e}")
                    failed_files.append(str(dicom_file))
                    continue

            if len(slice_data) == 0:
                raise ValueError("No valid DICOM files could be processed")

            if failed_files:
                self.logger.warning(
                    f"Failed to process {len(failed_files)} files: {failed_files}"
                )

            # STEP 2: Calculate accurate Z-spacing from ALL slices
            ds_first = slice_data[0]["dicom_dataset"]

            # Method 1: Try SpacingBetweenSlices DICOM tag
            z_spacing = ds_first.get("SpacingBetweenSlices", None)
            spacing_method = "SpacingBetweenSlices"

            if z_spacing is None:
                # Method 2: Calculate from all slice locations
                slice_positions = [item["slice_location"] for item in slice_data]
                slice_positions.sort()

                if len(slice_positions) > 1:
                    # Calculate all consecutive spacings
                    spacings = [
                        abs(slice_positions[i + 1] - slice_positions[i])
                        for i in range(len(slice_positions) - 1)
                    ]

                    # Remove outliers (spacing variations > 50% of median)
                    median_spacing = np.median(spacings)
                    filtered_spacings = [
                        s
                        for s in spacings
                        if abs(s - median_spacing) < 0.5 * median_spacing
                    ]

                    if filtered_spacings:
                        z_spacing = np.mean(filtered_spacings)
                        spacing_method = (
                            f"calculated_from_{len(slice_positions)}_slices"
                        )
                        self.logger.info(
                            f"Calculated Z-spacing: {z_spacing:.3f}mm (median: {median_spacing:.3f}mm)"
                        )
                    else:
                        z_spacing = median_spacing
                        spacing_method = "median_spacing"
                        self.logger.warning(
                            f"High spacing variation detected, using median: {z_spacing:.3f}mm"
                        )
                else:
                    # Method 3: Fallback to SliceThickness
                    z_spacing = ds_first.get("SliceThickness", 1.0)
                    spacing_method = "SliceThickness_fallback"
                    self.logger.warning(
                        f"Single slice or no spacing info, using SliceThickness: {z_spacing:.3f}mm"
                    )

            # Validate Z-spacing
            if z_spacing <= 0 or z_spacing > 50:  # Medical imaging reasonable bounds
                self.logger.error(f"Invalid Z-spacing: {z_spacing}mm")
                raise ValueError(f"Invalid Z-spacing calculated: {z_spacing}mm")

            # STEP 3: Sort slices in correct anatomical order
            # Primary sort: slice location, Secondary sort: instance number, Tertiary: acquisition time
            slice_data.sort(
                key=lambda x: (
                    x["slice_location"],
                    x["instance_number"],
                    x["acquisition_time"],
                )
            )

            # Verify slice ordering makes anatomical sense
            sorted_locations = [item["slice_location"] for item in slice_data]
            location_diffs = [
                sorted_locations[i + 1] - sorted_locations[i]
                for i in range(len(sorted_locations) - 1)
            ]

            # Check for major inconsistencies
            if any(abs(diff) > 3 * z_spacing for diff in location_diffs):
                self.logger.warning(
                    "Detected large gaps in slice spacing - potential missing slices"
                )

            # STEP 4: Extract spatial parameters with validation
            pixel_spacing = ds_first.get("PixelSpacing", [1.0, 1.0])
            image_orientation = ds_first.get(
                "ImageOrientationPatient", [1, 0, 0, 0, 1, 0]
            )
            image_position = ds_first.get("ImagePositionPatient", [0.0, 0.0, 0.0])

            # Validate pixel spacing
            if not pixel_spacing or len(pixel_spacing) != 2:
                self.logger.warning("Invalid PixelSpacing, using default [1.0, 1.0]")
                pixel_spacing = [1.0, 1.0]

            if any(
                s <= 0 or s > 10 for s in pixel_spacing
            ):  # Reasonable CT pixel spacing bounds
                self.logger.warning(f"Suspicious pixel spacing: {pixel_spacing}mm")

            # Validate image orientation
            if not image_orientation or len(image_orientation) != 6:
                self.logger.warning(
                    "Invalid ImageOrientationPatient, using axial default"
                )
                image_orientation = [1, 0, 0, 0, 1, 0]

            # STEP 5: Construct proper affine matrix with ImageOrientationPatient
            # Extract row and column direction vectors from DICOM (LPS coordinate system)
            row_cosines = np.array(
                image_orientation[:3]
            )  # X-direction in patient coordinate system
            col_cosines = np.array(
                image_orientation[3:6]
            )  # Y-direction in patient coordinate system

            # Calculate slice direction vector (cross product)
            slice_cosines = np.cross(row_cosines, col_cosines)

            # Normalize vectors (should be unit vectors but ensure robustness)
            row_cosines = row_cosines / np.linalg.norm(row_cosines)
            col_cosines = col_cosines / np.linalg.norm(col_cosines)
            slice_cosines = slice_cosines / np.linalg.norm(slice_cosines)

            # Validate orthogonality of direction vectors
            dot_product = np.abs(np.dot(row_cosines, col_cosines))
            if dot_product > 0.01:  # Allow small numerical errors
                self.logger.warning(
                    f"Direction vectors not orthogonal: dot product = {dot_product:.4f}"
                )

            # CRITICAL: Convert from DICOM LPS to NIfTI RAS coordinate system
            # LPS (Left-Posterior-Superior) → RAS (Right-Anterior-Superior)
            # This requires flipping X and Y axes: RAS = [-LPS_X, -LPS_Y, LPS_Z]

            # Apply LPS→RAS conversion to direction vectors
            row_cosines_ras = -row_cosines  # Flip X-direction (Left → Right)
            col_cosines_ras = -col_cosines  # Flip Y-direction (Posterior → Anterior)
            slice_cosines_ras = slice_cosines  # Keep Z-direction (Superior unchanged)

            # Convert origin position from LPS to RAS
            image_position_ras = np.array(
                [-image_position[0], -image_position[1], image_position[2]]
            )

            # Construct affine matrix in RAS coordinate system for NIfTI
            affine = np.eye(4)

            # Set direction vectors scaled by pixel spacing (following DICOM convention)
            # DICOM PixelSpacing = [row_spacing, col_spacing] = [Y, X] in mm
            # CRITICAL: row_cosines defines X-direction, col_cosines defines Y-direction
            affine[0:3, 0] = (
                row_cosines_ras * pixel_spacing[1]
            )  # X-direction uses column spacing
            affine[0:3, 1] = (
                col_cosines_ras * pixel_spacing[0]
            )  # Y-direction uses row spacing
            affine[0:3, 2] = slice_cosines_ras * z_spacing  # Z-direction (slices)

            # Set origin position in RAS coordinates
            affine[0:3, 3] = image_position_ras

            # Log coordinate system conversion for debugging
            self.logger.debug("LPS→RAS conversion applied:")
            self.logger.debug(f"  Original position (LPS): {image_position}")
            self.logger.debug(f"  Converted position (RAS): {image_position_ras}")
            self.logger.debug(f"  Pixel spacing [Y,X]: {pixel_spacing} mm")
            self.logger.debug(f"  Z spacing: {z_spacing} mm")

            # STEP 6: Create volume with proper ordering and shape validation
            pixel_arrays = [item["pixel_array"] for item in slice_data]

            # Validate and standardize slice dimensions
            if not pixel_arrays:
                raise ValueError("No pixel arrays available for volume creation")

            # Check slice dimensions for consistency
            shapes = [arr.shape for arr in pixel_arrays]
            unique_shapes = list(set(shapes))

            if len(unique_shapes) > 1:
                self.logger.warning(
                    f"Inconsistent slice dimensions detected: {unique_shapes}"
                )
                # Find the most common shape
                shape_counts = {shape: shapes.count(shape) for shape in unique_shapes}
                target_shape = max(shape_counts, key=shape_counts.get)
                self.logger.info(
                    f"Using target shape: {target_shape} (found in {shape_counts[target_shape]}/{len(pixel_arrays)} slices)"
                )

                # Filter or resize slices to match target shape
                filtered_arrays = []
                filtered_slice_data = []
                for i, (arr, slice_info) in enumerate(
                    zip(pixel_arrays, slice_data, strict=False)
                ):
                    if arr.shape == target_shape:
                        filtered_arrays.append(arr)
                        filtered_slice_data.append(slice_info)
                    else:
                        self.logger.warning(
                            f"Excluding slice {slice_info['dicom_file']} with shape {arr.shape}"
                        )

                if (
                    len(filtered_arrays) < len(pixel_arrays) * 0.5
                ):  # Less than 50% valid slices
                    raise ValueError(
                        f"Too many slices excluded due to shape mismatch: {len(filtered_arrays)}/{len(pixel_arrays)} valid"
                    )

                pixel_arrays = filtered_arrays
                slice_data = filtered_slice_data
                self.logger.info(
                    f"Using {len(pixel_arrays)} slices with consistent dimensions"
                )

            volume = np.stack(pixel_arrays, axis=0)

            # Validate volume dimensions
            if volume.ndim != 3:
                raise ValueError(f"Expected 3D volume, got {volume.ndim}D")

            self.logger.info(f"Created volume with shape: {volume.shape}")
            self.logger.info(
                f"Volume range: [{volume.min():.1f}, {volume.max():.1f}] HU"
            )

            # STEP 7: Create and save NIfTI image with mathematically correct orientation
            # For standard axial acquisitions, use corrected simple affine
            # Complex affine reserved for oblique/rotated cases

            # Check if this is a standard axial acquisition
            is_axial = (
                abs(image_orientation[0] - 1.0) < 0.01
                and abs(image_orientation[1] - 0.0) < 0.01
                and abs(image_orientation[2] - 0.0) < 0.01
                and abs(image_orientation[3] - 0.0) < 0.01
                and abs(image_orientation[4] - 1.0) < 0.01
                and abs(image_orientation[5] - 0.0) < 0.01
            )

            if is_axial:
                # Use mathematically correct simple affine for axial scans
                self.logger.info(
                    "Standard axial acquisition detected - using corrected simple affine"
                )

                # Calculate spacing
                original_spacing = [
                    float(pixel_spacing[1]),  # X spacing (column spacing)
                    float(pixel_spacing[0]),  # Y spacing (row spacing)
                    float(z_spacing),  # Z spacing
                ]

                # Get slice coordinate range
                slice_locations = [item["slice_location"] for item in slice_data]
                min_slice_z = min(slice_locations)

                # Use first slice position as reference
                first_slice_position = slice_data[0][
                    "image_position_patient"
                ]  # LPS coordinates

                # Convert LPS origin to RAS origin
                origin_x_ras = -first_slice_position[0]  # LPS → RAS: flip X
                origin_y_ras = -first_slice_position[1]  # LPS → RAS: flip Y
                origin_z_ras = min_slice_z  # LPS → RAS: Z unchanged

                # Create mathematically correct simple affine
                corrected_affine = np.eye(4)
                # Positive diagonal for proper RAS orientation
                corrected_affine[0, 0] = original_spacing[0]  # X spacing (positive)
                corrected_affine[1, 1] = original_spacing[1]  # Y spacing (positive)
                corrected_affine[2, 2] = original_spacing[2]  # Z spacing (positive)

                # Set RAS origin
                corrected_affine[0, 3] = origin_x_ras
                corrected_affine[1, 3] = origin_y_ras
                corrected_affine[2, 3] = origin_z_ras

                nifti_img = nib.Nifti1Image(volume, corrected_affine)
                final_affine = corrected_affine

                self.logger.info(
                    "Using corrected simple affine with positive diagonal for proper RAS orientation"
                )

            else:
                # Use complex affine for non-axial acquisitions
                self.logger.info(
                    "Non-axial acquisition detected - using complex affine"
                )
                nifti_img = nib.Nifti1Image(volume, affine)
                final_affine = affine

                # Calculate spacing from complex affine
                spacing_x = np.linalg.norm(affine[:3, 0])
                spacing_y = np.linalg.norm(affine[:3, 1])
                spacing_z = np.linalg.norm(affine[:3, 2])
                original_spacing = [spacing_x, spacing_y, spacing_z]

            # Set proper NIfTI header information
            nifti_img.header.set_xyzt_units("mm", "sec")
            nifti_img.header.set_data_dtype(np.float32)

            output_path.parent.mkdir(parents=True, exist_ok=True)
            nib.save(nifti_img, str(output_path))

            self.logger.info(
                f"NIfTI saved with {'simple' if is_axial else 'complex'} affine matrix"
            )
            self.logger.debug(
                f"Final affine determinant: {np.linalg.det(final_affine[:3,:3]):.6f}"
            )

            # STEP 8: Prepare comprehensive metadata
            # original_spacing already defined above

            # Create detailed slice metadata for coordinate conversion
            slice_metadata = []
            for item in slice_data:
                slice_info = {
                    "dicom_file": item["dicom_file"],
                    "instance_number": item["instance_number"],
                    "slice_location": item["slice_location"],
                    "image_position_patient": item["image_position_patient"],
                    "image_orientation_patient": item["image_orientation_patient"],
                    "pixel_spacing": item["pixel_spacing"],
                }
                slice_metadata.append(slice_info)

            metadata = {
                "patient_id": ds_first.get("PatientID", "unknown"),
                "study_instance_uid": ds_first.get("StudyInstanceUID", "unknown"),
                "series_instance_uid": ds_first.get("SeriesInstanceUID", "unknown"),
                "series_description": ds_first.get("SeriesDescription", "unknown"),
                "num_slices": len(slice_data),
                "failed_files": failed_files,
                # Spatial transformation information (in RAS coordinates)
                "original_spacing": original_spacing,
                "original_origin": list(image_position_ras),  # RAS coordinates
                "original_origin_lps": list(
                    image_position
                ),  # Original LPS coordinates for reference
                "original_orientation": list(
                    image_orientation
                ),  # Original DICOM orientation vectors
                "slice_metadata": slice_metadata,
                "resampled_spacing": [0.7, 0.7, 1.25],  # Target spacing from model
                # Quality metrics
                "z_spacing_method": spacing_method,
                "volume_shape": list(volume.shape),
                "volume_range_hu": [float(volume.min()), float(volume.max())],
                "nifti_path": str(output_path),
                "affine_matrix": final_affine.tolist(),  # Affine matrix used in NIfTI
            }

            # Log final results with coordinate system validation
            self.logger.info(
                f"Successfully converted DICOM series to NIfTI: {output_path}"
            )
            self.logger.info(
                f"Final spacing (X,Y,Z): {original_spacing} mm (method: {spacing_method})"
            )
            self.logger.info(
                f"Volume shape: {volume.shape}, range: [{volume.min():.1f}, {volume.max():.1f}] HU"
            )
            self.logger.info(
                "Coordinate system: DICOM LPS → NIfTI RAS conversion applied"
            )

            # Validate affine matrix properties
            det = np.linalg.det(final_affine[:3, :3])
            self.logger.debug(
                f"Final affine matrix determinant: {det:.6f} (should be positive for corrected RAS)"
            )
            if det < 0:
                self.logger.warning(
                    "Negative determinant detected in final affine - coordinate system may be flipped"
                )

            # Log transformation summary for debugging
            self.logger.debug("Coordinate system transformation summary:")
            self.logger.debug(f"  DICOM origin (LPS): {image_position}")
            self.logger.debug(f"  NIfTI origin (RAS): {image_position_ras}")
            self.logger.debug(f"  DICOM pixel spacing [Y,X]: {pixel_spacing} mm")
            self.logger.debug(f"  Z spacing: {z_spacing} mm")
            self.logger.debug(f"  Final spacing [X,Y,Z]: {original_spacing} mm")
            self.logger.debug(
                "  Affine matrix assignment verified: X uses col_spacing, Y uses row_spacing"
            )

            return metadata

        except Exception as e:
            self.logger.error(f"Critical failure in DICOM to NIfTI conversion: {e}")
            import traceback

            self.logger.error(traceback.format_exc())
            return None


class LungNoduleDetector:
    """Lung nodule detection using MONAI RetinaNet model."""

    def __init__(self, model_path: Path | None, logger: logging.Logger):
        self.logger = logger
        self.model_path = model_path
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.detector = None

        # Detection parameters (from ai-lung-health-benchmarking)
        self.target_spacing = [0.703125, 0.703125, 1.25]  # x, y, z spacing in mm
        self.patch_size = [192, 192, 80]  # x, y, z patch size for training
        self.val_patch_size = [512, 512, 208]  # x, y, z patch size for inference
        self.intensity_range = [-1024, 300]  # HU range for clipping (matches submodule)
        self.score_thresh = 0.02
        self.nms_thresh = 0.22

        self._setup_transforms()

        if model_path and model_path.exists():
            self._load_model()
        else:
            self.logger.warning("No model path provided or model file not found")

    def _setup_transforms(self):
        """Set up preprocessing transforms."""
        self.transforms = Compose(
            [
                LoadImage(image_only=True, ensure_channel_first=True),
                EnsureChannelFirst(),
                # Skip Orientation transform - assume NIfTI is already in RAS
                Spacing(pixdim=self.target_spacing, mode="bilinear"),
                ScaleIntensityRange(
                    a_min=self.intensity_range[0],
                    a_max=self.intensity_range[1],
                    b_min=0.0,
                    b_max=1.0,
                    clip=True,
                ),
                ToTensor(),
                EnsureType(),
            ]
        )

    def _load_model(self):
        """Load pre-trained MONAI RetinaNet model."""
        try:
            # Model architecture parameters (from ai-lung-health-benchmarking config)
            returned_layers = [1, 2]
            base_anchor_shapes = [[6, 8, 4], [8, 6, 5], [10, 10, 6]]
            conv1_t_stride = [2, 2, 1]
            spatial_dims = 3
            n_input_channels = 1
            fg_labels = [0]  # Single class: nodule

            # Build anchor generator
            anchor_generator = AnchorGeneratorWithAnchorShape(
                feature_map_scales=[2**l for l in range(len(returned_layers) + 1)],
                base_anchor_shapes=base_anchor_shapes,
            )

            # Build network architecture
            conv1_t_size = [max(7, 2 * s + 1) for s in conv1_t_stride]
            backbone = resnet.ResNet(
                block=resnet.ResNetBottleneck,
                layers=[3, 4, 6, 3],
                block_inplanes=resnet.get_inplanes(),
                n_input_channels=n_input_channels,
                conv1_t_stride=conv1_t_stride,
                conv1_t_size=conv1_t_size,
            )

            feature_extractor = resnet_fpn_feature_extractor(
                backbone=backbone,
                spatial_dims=spatial_dims,
                pretrained_backbone=False,
                trainable_backbone_layers=None,
                returned_layers=returned_layers,
            )

            num_anchors = anchor_generator.num_anchors_per_location()[0]
            size_divisible = [
                s * 2 * 2 ** max(returned_layers)
                for s in feature_extractor.body.conv1.stride
            ]

            # Load the trained model - prioritize TorchScript format
            if self.model_path.suffix == ".pt":
                # TorchScript model (preferred format from reference)
                net = torch.jit.load(str(self.model_path), map_location=self.device)
                self.logger.info("Loaded TorchScript model")
            else:
                # Fallback to regular PyTorch model
                net = RetinaNet(
                    spatial_dims=spatial_dims,
                    num_classes=len(fg_labels),
                    num_anchors=num_anchors,
                    feature_extractor=feature_extractor,
                    size_divisible=size_divisible,
                )
                checkpoint = torch.load(str(self.model_path), map_location=self.device)
                # Handle different checkpoint formats
                if "state_dict" in checkpoint:
                    net.load_state_dict(checkpoint["state_dict"])
                elif "model" in checkpoint:
                    net.load_state_dict(checkpoint["model"])
                else:
                    net.load_state_dict(checkpoint)
                self.logger.info("Loaded regular PyTorch model")

            # Create detector
            self.detector = RetinaNetDetector(
                network=net, anchor_generator=anchor_generator, debug=False
            ).to(self.device)

            # Set inference parameters
            self.detector.set_box_selector_parameters(
                score_thresh=self.score_thresh,
                topk_candidates_per_level=1000,
                nms_thresh=self.nms_thresh,
                detections_per_img=100,
            )

            # Set sliding window inference
            self.detector.set_sliding_window_inferer(
                roi_size=self.val_patch_size,
                overlap=0.25,
                sw_batch_size=1,
                mode="gaussian",
                device="cpu",  # Use CPU for sliding window to save GPU memory
            )

            self.detector.eval()
            self.logger.info(f"Successfully loaded model from {self.model_path}")

        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            self.detector = None

    def detect_nodules(self, nifti_path: Path) -> list[dict]:
        """
        Perform nodule detection on a NIfTI image.

        Returns:
            List of detected nodules with bounding boxes and scores
        """
        if self.detector is None:
            self.logger.error("No model loaded for detection")
            return []

        try:
            self.logger.info(f"Processing image: {nifti_path}")

            # Load and preprocess image with detailed error handling
            try:
                image_tensor = self.transforms(str(nifti_path))
            except Exception as transform_error:
                self.logger.error(f"Transform pipeline failed: {transform_error}")
                # Try to load image without transforms to debug
                import nibabel as nib

                try:
                    nifti_img = nib.load(str(nifti_path))
                    self.logger.info(
                        f"NIfTI file info: shape={nifti_img.shape}, header={nifti_img.header.get_data_dtype()}"
                    )
                    self.logger.info(
                        f"Affine matrix determinant: {np.linalg.det(nifti_img.affine[:3,:3])}"
                    )
                except Exception as load_error:
                    self.logger.error(f"Failed to load NIfTI file: {load_error}")
                raise transform_error

            # Debug tensor shape
            self.logger.info(
                f"Image tensor shape after transforms: {image_tensor.shape}"
            )

            # RetinaNetDetector expects 4D tensors [C, D, H, W] in the list, not 5D with batch
            if isinstance(image_tensor, torch.Tensor):
                if image_tensor.dim() == 5:  # [B, C, D, H, W] → [C, D, H, W]
                    image_tensor = image_tensor.squeeze(0)
                elif image_tensor.dim() == 3:  # [D, H, W] → [C, D, H, W]
                    image_tensor = image_tensor.unsqueeze(0)
                self.logger.info(
                    f"Image tensor shape for detector: {image_tensor.shape}"
                )

            # Clear GPU cache before processing
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # Check memory requirements before moving to GPU
            tensor_size_gb = image_tensor.numel() * 4 / (1024**3)  # 4 bytes per float32
            self.logger.info(f"Tensor size: {tensor_size_gb:.2f} GB")

            # Check available GPU memory
            if torch.cuda.is_available():
                gpu_memory_free = (
                    torch.cuda.get_device_properties(0).total_memory
                    - torch.cuda.memory_allocated()
                )
                gpu_memory_free_gb = gpu_memory_free / (1024**3)
                self.logger.info(f"GPU memory available: {gpu_memory_free_gb:.2f} GB")

                # Require at least 3x tensor size in free memory for safe processing
                required_memory_gb = tensor_size_gb * 3
                if required_memory_gb > gpu_memory_free_gb:
                    self.logger.warning(
                        f"Insufficient GPU memory (need {required_memory_gb:.2f} GB, have {gpu_memory_free_gb:.2f} GB)"
                    )

            if tensor_size_gb > 1.0:  # Lower threshold for early warning
                self.logger.warning(
                    f"Large tensor detected ({tensor_size_gb:.2f} GB), monitoring memory closely"
                )

            # Run inference with smart GPU/CPU selection
            with torch.no_grad():
                # Check if we need sliding window inference
                use_inferer = image_tensor.numel() >= np.prod(self.val_patch_size)
                self.logger.info(f"Using sliding window inference: {use_inferer}")

                # Decide on processing strategy based on memory availability
                use_cpu_fallback = False
                if torch.cuda.is_available():
                    gpu_memory_free = (
                        torch.cuda.get_device_properties(0).total_memory
                        - torch.cuda.memory_allocated()
                    )
                    gpu_memory_free_gb = gpu_memory_free / (1024**3)
                    required_memory_gb = tensor_size_gb * 3  # Conservative estimate

                    if required_memory_gb > gpu_memory_free_gb:
                        self.logger.info(
                            f"Proactively using CPU due to insufficient GPU memory ({gpu_memory_free_gb:.2f} GB available, need {required_memory_gb:.2f} GB)"
                        )
                        use_cpu_fallback = True

                if use_cpu_fallback:
                    # Direct CPU processing
                    image_tensor = image_tensor.cpu()
                    detector_cpu = self.detector.cpu()
                    self.logger.info("Running inference on CPU (memory-safe)")
                    outputs = detector_cpu([image_tensor], use_inferer=use_inferer)

                    # Move detector back to GPU
                    if torch.cuda.is_available():
                        self.detector = self.detector.to(self.device)
                else:
                    # Try GPU processing with fallback
                    try:
                        # Move to device and run inference
                        image_tensor = image_tensor.to(self.device)

                        if torch.cuda.is_available():
                            with torch.cuda.amp.autocast():
                                outputs = self.detector(
                                    [image_tensor], use_inferer=use_inferer
                                )
                        else:
                            outputs = self.detector(
                                [image_tensor], use_inferer=use_inferer
                            )

                    except RuntimeError as cuda_error:
                        if "out of memory" in str(cuda_error):
                            self.logger.warning(
                                f"GPU OOM detected despite checks, falling back to CPU: {cuda_error}"
                            )

                            # Clear GPU cache
                            if torch.cuda.is_available():
                                torch.cuda.empty_cache()

                            # Move tensor to CPU and run inference
                            image_tensor = image_tensor.cpu()
                            detector_cpu = self.detector.cpu()

                            self.logger.info(
                                "Running inference on CPU (emergency fallback)"
                            )
                            outputs = detector_cpu(
                                [image_tensor], use_inferer=use_inferer
                            )

                            # Move detector back to GPU for next iteration
                            if torch.cuda.is_available():
                                self.detector = self.detector.to(self.device)
                        else:
                            raise cuda_error

            # Process outputs
            if outputs and len(outputs) > 0:
                output = outputs[0]

                # Extract predictions using detector's attribute keys (not direct dictionary keys)
                # Reference: ai-lung-health-benchmarking/ct_detection/testing.py:181-189
                boxes = (
                    output.get(self.detector.target_box_key, torch.empty((0, 6)))
                    .cpu()
                    .numpy()
                )
                scores = (
                    output.get(self.detector.pred_score_key, torch.empty(0))
                    .cpu()
                    .numpy()
                )
                labels = (
                    output.get(self.detector.target_label_key, torch.empty(0))
                    .cpu()
                    .numpy()
                )

                nodules = []
                for i, (box, score, label) in enumerate(
                    zip(boxes, scores, labels, strict=False)
                ):
                    if len(box) >= 6:  # Ensure we have 6 coordinates in cccwhd format
                        # CRITICAL: Model uses "cccwhd" format: [center_x, center_y, center_z, width, height, depth]
                        # Reference: ai-lung-health-benchmarking training_config.json "gt_box_mode": "cccwhd"
                        nodule = {
                            "nodule_id": i + 1,
                            "center_x": float(
                                box[0]
                            ),  # Direct center X (not calculated)
                            "center_y": float(
                                box[1]
                            ),  # Direct center Y (not calculated)
                            "center_z": float(
                                box[2]
                            ),  # Direct center Z (not calculated)
                            "width": float(box[3]),  # Direct width
                            "height": float(box[4]),  # Direct height
                            "depth": float(box[5]),  # Direct depth
                            "confidence_score": float(score),
                            "label": int(label),
                        }
                        nodules.append(nodule)

                self.logger.info(f"Detected {len(nodules)} nodules")
                return nodules

            else:
                self.logger.info("No nodules detected")
                return []

        except Exception as e:
            self.logger.error(f"Failed to detect nodules in {nifti_path}: {e}")
            return []


def create_output_directories(base_dir: Path) -> tuple[Path, Path]:
    """Create output directories for NIfTI files and results."""
    nifti_dir = base_dir / "converted_nifti"
    results_dir = base_dir / "2507-nodule-output"

    nifti_dir.mkdir(exist_ok=True)
    results_dir.mkdir(exist_ok=True)

    return nifti_dir, results_dir


def save_results_to_csv(results: list[dict], output_file: Path, logger: logging.Logger):
    """Save detection results to CSV file."""
    try:
        if not results:
            logger.warning("No results to save")
            return

        df = pd.DataFrame(results)
        df.to_csv(output_file, index=False)
        logger.info(f"Saved {len(results)} detection results to {output_file}")

    except Exception as e:
        logger.error(f"Failed to save results to CSV: {e}")


def save_incremental_results(
    series_results: list[dict],
    series_slice_results: list[dict],
    output_file: Path,
    slice_output_file: Path,
    logger: logging.Logger,
    append_mode: bool = True,
):
    """
    Save results incrementally after each series is processed.

    Args:
        series_results: 3D nodule detection results for current series
        series_slice_results: Slice-level annotations for current series
        output_file: Path to main results CSV
        slice_output_file: Path to slice-level results CSV
        logger: Logger instance
        append_mode: If True, append to existing files; if False, overwrite
    """
    try:
        # Save 3D detection results
        if series_results:
            df_3d = pd.DataFrame(series_results)
            mode = "a" if append_mode and output_file.exists() else "w"
            header = not (append_mode and output_file.exists())

            df_3d.to_csv(output_file, mode=mode, header=header, index=False)
            logger.info(
                f"Incrementally saved {len(series_results)} 3D results to {output_file}"
            )

        # Save slice-level results
        if series_slice_results:
            # Flatten slice results for CSV
            slice_rows = []
            for result in series_slice_results:
                series_meta = result.get("series_metadata", {})
                row = {
                    # Series identification for traceability
                    "series_uid": series_meta.get("series_uid", "unknown"),
                    "study_folder": series_meta.get("study_folder", "unknown"),
                    "series_folder": series_meta.get("series_folder", "unknown"),
                    "patient_id": series_meta.get("patient_id", "unknown"),
                    "study_instance_uid": series_meta.get(
                        "study_instance_uid", "unknown"
                    ),
                    "series_instance_uid": series_meta.get(
                        "series_instance_uid", "unknown"
                    ),
                    "series_description": series_meta.get(
                        "series_description", "unknown"
                    ),
                    # DICOM slice information
                    "dicom_file": result["dicom_file"],
                    "instance_number": result["instance_number"],
                    "slice_location": result["slice_location"],
                    # Nodule information
                    "nodule_id": result["nodule_metadata"]["nodule_id"],
                    "confidence_score": result["nodule_metadata"]["confidence_score"],
                    "label": result["nodule_metadata"]["label"],
                    # 2D bounding box in pixel coordinates
                    "bbox_x_min": result["bbox_2d"]["x_min"],
                    "bbox_y_min": result["bbox_2d"]["y_min"],
                    "bbox_x_max": result["bbox_2d"]["x_max"],
                    "bbox_y_max": result["bbox_2d"]["y_max"],
                    "bbox_width": result["bbox_2d"]["width"],
                    "bbox_height": result["bbox_2d"]["height"],
                    # 3D world coordinates in mm
                    "world_x_min": result["world_coords"]["x_min"],
                    "world_x_max": result["world_coords"]["x_max"],
                    "world_y_min": result["world_coords"]["y_min"],
                    "world_y_max": result["world_coords"]["y_max"],
                    "world_z_location": result["world_coords"]["z_location"],
                }
                slice_rows.append(row)

            if slice_rows:
                df_slice = pd.DataFrame(slice_rows)
                mode = "a" if append_mode and slice_output_file.exists() else "w"
                header = not (append_mode and slice_output_file.exists())

                df_slice.to_csv(
                    slice_output_file, mode=mode, header=header, index=False
                )
                logger.info(
                    f"Incrementally saved {len(slice_rows)} slice-level results to {slice_output_file}"
                )

    except Exception as e:
        logger.error(f"Failed to save incremental results: {e}")
        # Continue processing even if saving fails


def create_result_files_with_headers(
    output_file: Path, slice_output_file: Path, logger: logging.Logger
):
    """
    Create result CSV files with proper headers at the start of processing.
    This ensures consistent column ordering for incremental saves.
    """
    try:
        # Create 3D results file with headers
        if not output_file.exists():
            # Define expected 3D result columns based on current implementation
            headers_3d = [
                "patient_id",
                "study_instance_uid",
                "series_instance_uid",
                "series_description",
                "scan_filename",
                "study_folder",
                "series_folder",
                "nodule_id",
                "center_x",
                "center_y",
                "center_z",
                "width",
                "height",
                "depth",
                "confidence_score",
                "label",
            ]
            pd.DataFrame(columns=headers_3d).to_csv(output_file, index=False)
            logger.info(f"Created 3D results file with headers: {output_file}")

        # Create slice-level results file with headers
        if not slice_output_file.exists():
            headers_slice = [
                "series_uid",
                "study_folder",
                "series_folder",
                "patient_id",
                "study_instance_uid",
                "series_instance_uid",
                "series_description",
                "dicom_file",
                "instance_number",
                "slice_location",
                "nodule_id",
                "confidence_score",
                "label",
                "bbox_x_min",
                "bbox_y_min",
                "bbox_x_max",
                "bbox_y_max",
                "bbox_width",
                "bbox_height",
                "world_x_min",
                "world_x_max",
                "world_y_min",
                "world_y_max",
                "world_z_location",
            ]
            pd.DataFrame(columns=headers_slice).to_csv(slice_output_file, index=False)
            logger.info(
                f"Created slice-level results file with headers: {slice_output_file}"
            )

    except Exception as e:
        logger.error(f"Failed to create result files with headers: {e}")
        # Continue processing even if header creation fails


def main():
    parser = argparse.ArgumentParser(
        description="CT Lung Nodule Detection using Duke AI-Lung-Health-Benchmarking Models"
    )
    parser.add_argument(
        "--csv-path",
        type=Path,
        default=DATA_DIR / "raw" / "lung_ct_scans.csv",
        help="Path to CSV file with series information (default: clarion DATA_DIR/raw/lung_ct_scans.csv)",
    )
    parser.add_argument(
        "--model-path",
        type=Path,
        default=Path(__file__).parent / "models" / "LUNA16_mD.pt",
        help="Path to pre-trained model file (.pt or .pth) (default: ./models/LUNA16_mD.pt)",
    )
    parser.add_argument(
        "--output-dir",
        type=Path,
        default=DATA_DIR
        / "experiments"
        / "2507-nodule-detection-baseline"
        / "run_detection_luna16_250720",
        help="Output directory for results (default: current directory)",
    )
    parser.add_argument(
        "--keep-nifti",
        action="store_true",
        help="Keep converted NIfTI files after processing",
    )

    args = parser.parse_args()

    # Create output directories
    nifti_dir, results_dir = create_output_directories(args.output_dir)

    # Set up logging
    logger = setup_logging(results_dir)

    # Check if CSV file exists
    if not args.csv_path.exists():
        logger.error(f"CSV file not found: {args.csv_path}")
        logger.info("Expected CSV structure:")
        logger.info("  - useful_series column (1 for useful series)")
        logger.info("  - study_folder column (study directory name)")
        logger.info("  - series_folder column (series directory name)")
        logger.info(
            f"  - Located at: {DATA_DIR}/raw/lung_ct_scans/[study_folder]/[series_folder]/"
        )
        return

    # Initialize processors
    dicom_processor = DICOMProcessor(logger)
    detector = LungNoduleDetector(args.model_path, logger)

    if args.model_path and not args.model_path.exists():
        logger.warning(f"Model file not found: {args.model_path}")
        logger.info("You can download pre-trained models from:")
        logger.info("📥 Zenodo: https://zenodo.org/records/14967976")
        logger.info("Available models:")
        logger.info("  - DLCSD-mD: Trained on Duke Lung Cancer Screening Dataset")
        logger.info("  - LUNA16-mD: Trained on LUNA16 dataset")
        logger.info("Alternative baseline models:")
        logger.info(
            "  - Models Genesis: https://drive.google.com/file/d/16iIIRkl6zYAfQ14i9NOakwFd6w_xKBSY/view"
        )
        logger.info(
            "  - Med3D: https://drive.google.com/file/d/1dIyJd3jpz9mBx534UA7deqT7f8N0sbJL/view"
        )
        logger.info("Or set up a basic pipeline without model for DICOM processing")

    # Set up incremental result saving
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    output_file = results_dir / f"nodule_detection_results_{timestamp}.csv"
    slice_output_file = results_dir / f"slice_level_annotations_{timestamp}.csv"

    # Create result files with headers
    create_result_files_with_headers(output_file, slice_output_file, logger)

    # Process each series using streaming approach
    logger.info(f"Starting streaming processing of series from CSV: {args.csv_path}")

    all_results = []
    all_slice_results = []  # Collect all slice-level results for final summary
    series_count = 0
    successful_series = 0
    failed_series = 0

    for series_uid, series_data in tqdm(
        dicom_processor.iterate_series_from_csv(args.csv_path), desc="Processing series"
    ):
        series_count += 1
        series_results = []  # Results for current series only
        series_slice_results = []  # Slice results for current series only

        try:
            dicom_files = series_data["files"]
            csv_row = series_data["csv_row"]

            logger.info(
                f"Processing series {series_count}: {series_data['study_folder']}/{series_data['series_folder']} ({len(dicom_files)} files)"
            )

            # Convert DICOM to NIfTI with error handling
            nifti_filename = f"series_{series_data['study_folder']}_{series_data['series_folder']}.nii.gz"
            nifti_path = nifti_dir / nifti_filename

            try:
                metadata = dicom_processor.dicom_to_nifti(dicom_files, nifti_path)
                if not metadata:
                    logger.error(
                        f"Failed to convert series {series_uid} - no metadata returned"
                    )
                    failed_series += 1
                    continue
            except Exception as conversion_error:
                logger.error(
                    f"Exception during DICOM to NIfTI conversion for series {series_uid}: {conversion_error}"
                )
                failed_series += 1
                continue

            # Perform nodule detection with error handling
            try:
                nodules = detector.detect_nodules(nifti_path)
                logger.info(f"Detected {len(nodules)} nodules in series {series_uid}")
            except Exception as detection_error:
                logger.error(
                    f"Exception during nodule detection for series {series_uid}: {detection_error}"
                )
                nodules = []  # Continue with empty nodules list

            # Convert 3D nodules to DICOM slice coordinates with error handling
            if nodules and len(nodules) > 0:
                transformation_record = {
                    **metadata,
                    "timestamp": time.strftime("%Y%m%d_%H%M%S"),
                    "study_folder": series_data["study_folder"],
                    "series_folder": series_data["series_folder"],
                }

                try:
                    from coordinate_converter import CoordinateConverter

                    converter = CoordinateConverter(logger)

                    # Prepare series metadata for traceability
                    series_metadata = {
                        "series_uid": series_uid,
                        "study_folder": series_data["study_folder"],
                        "series_folder": series_data["series_folder"],
                        "patient_id": metadata["patient_id"],
                        "study_instance_uid": metadata["study_instance_uid"],
                        "series_instance_uid": metadata["series_instance_uid"],
                        "series_description": metadata["series_description"],
                    }

                    for nodule in nodules:
                        try:
                            slice_results = converter.convert_3d_bbox_to_dicom_slices(
                                nodule, transformation_record, series_metadata
                            )
                            series_slice_results.extend(slice_results)
                        except Exception as coord_error:
                            logger.warning(
                                f"Failed to convert nodule {nodule.get('nodule_id', 'unknown')} to slice coordinates: {coord_error}"
                            )

                    logger.info(
                        f"Converted {len(nodules)} 3D nodules to {len(series_slice_results)} slice-level annotations"
                    )
                except Exception as e:
                    logger.warning(
                        f"Failed to convert nodules to slice coordinates for series {series_uid}: {e}"
                    )

            # Add metadata to results including CSV data
            for nodule in nodules:
                try:
                    result = {
                        "patient_id": metadata["patient_id"],
                        "study_instance_uid": metadata["study_instance_uid"],
                        "series_instance_uid": metadata["series_instance_uid"],
                        "series_description": metadata["series_description"],
                        "scan_filename": nifti_filename,
                        "study_folder": series_data["study_folder"],
                        "series_folder": series_data["series_folder"],
                        **nodule,
                        # Add any additional CSV columns that might be useful
                        **{
                            k: v
                            for k, v in csv_row.items()
                            if k
                            not in ["study_folder", "series_folder", "useful_series"]
                        },
                    }
                    series_results.append(result)
                except Exception as result_error:
                    logger.warning(
                        f"Failed to create result record for nodule {nodule.get('nodule_id', 'unknown')}: {result_error}"
                    )

            # Incrementally save results for this series
            try:
                save_incremental_results(
                    series_results,
                    series_slice_results,
                    output_file,
                    slice_output_file,
                    logger,
                    append_mode=True,
                )

                # Add to overall results for final summary
                all_results.extend(series_results)
                all_slice_results.extend(series_slice_results)
                successful_series += 1

            except Exception as save_error:
                logger.error(
                    f"Failed to save results for series {series_uid}: {save_error}"
                )
                # Continue processing even if saving fails
                failed_series += 1

            # Clean up NIfTI file if not keeping them
            try:
                if not args.keep_nifti and nifti_path.exists():
                    nifti_path.unlink()
            except Exception as cleanup_error:
                logger.warning(
                    f"Failed to clean up NIfTI file {nifti_path}: {cleanup_error}"
                )

        except Exception as series_error:
            logger.error(
                f"Unexpected error processing series {series_uid}: {series_error}"
            )
            failed_series += 1
            continue

    # Processing complete - results have been saved incrementally throughout the run

    # Print summary
    logger.info("=" * 50)
    logger.info("DETECTION SUMMARY")
    logger.info("=" * 50)
    logger.info(f"Total series examined: {series_count}")
    logger.info(f"Successfully processed series: {successful_series}")
    logger.info(f"Failed series: {failed_series}")
    logger.info(
        f"Success rate: {(successful_series/series_count*100):.1f}%"
        if series_count > 0
        else "N/A"
    )
    logger.info(f"Total nodules detected: {len(all_results)}")
    logger.info(f"Total slice-level annotations: {len(all_slice_results)}")
    logger.info(f"3D Results saved incrementally to: {output_file}")
    if all_slice_results:
        logger.info(f"Slice Results saved incrementally to: {slice_output_file}")

    if all_results:
        df = pd.DataFrame(all_results)
        logger.info(f"Average confidence score: {df['confidence_score'].mean():.3f}")
        logger.info(f"Max confidence score: {df['confidence_score'].max():.3f}")
        logger.info(f"Unique studies processed: {df['study_folder'].nunique()}")
        logger.info(f"Patients processed: {df['patient_id'].nunique()}")
    else:
        logger.warning("No results were successfully generated")

    logger.info("=" * 50)
    if failed_series > 0:
        logger.warning(
            f"⚠️  {failed_series} series failed to process - check logs above for details"
        )
    logger.info(
        "Results have been saved incrementally - data is preserved even if some series failed"
    )


if __name__ == "__main__":
    main()
