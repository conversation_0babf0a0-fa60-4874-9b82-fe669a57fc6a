#!/usr/bin/env python3
"""
Coordinate Transformation Development and Testing Environment

This script allows us to develop, test, and validate correct coordinate transformations
before applying fixes to the production code. It includes:

1. Current (broken) transformation logic
2. Fixed transformation logic  
3. Comprehensive validation tests
4. Model coordinate interpretation testing
5. Bidirectional transformation verification

Uses real parameters from the test series data.
"""

import numpy as np
import logging
from typing import Tuple, Dict, List

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class CoordinateTransformationTester:
    """Test environment for coordinate transformation development."""
    
    def __init__(self):
        # Real parameters from test series 20131104_78091c4ef799c7360673
        
        # DICOM parameters (from slice metadata)
        self.pixel_spacing = [0.69140625, 0.69140625]  # [row_spacing, col_spacing] = [Y, X] in mm
        self.image_position_patient = [-192.6063, -0.**********, 23.0]  # [X, Y, Z] in LPS coordinates (mm)
        self.slice_location = 23.0  # Z position in mm
        
        # Current (problematic) NIfTI affine matrix from the pipeline
        self.current_affine = np.array([
            [0.69140625, 0.0, 0.0, 192.6063],         # X: spacing=0.691, origin=192.6063
            [0.0, 0.69140625, 0.0, 0.**********],     # Y: spacing=0.691, origin=0.055
            [0.0, 0.0, 3.0, -1.0],                    # Z: spacing=3.0, origin=-1.0
            [0.0, 0.0, 0.0, 1.0]                      # Homogeneous coordinates
        ])
        
        
        # Volume parameters
        self.volume_shape = [117, 512, 512]  # [Z, Y, X] - depth, height, width
        self.resampled_spacing = [0.7, 0.7, 1.25]  # [X, Y, Z] target spacing
        
        # Model detection example (from debug output)
        self.example_nodule_raw = {
            'center_x': 41.986,   # Model's raw output 
            'center_y': 119.274,  # Model's raw output
            'center_z': 290.716,  # Model's raw output
            'width': 45.624,      # Model's raw output
            'height': 122.756,    # Model's raw output  
            'depth': 293.272,     # Model's raw output
        }

    def test_current_implementation(self):
        """Test the current (broken) implementation to understand the issues."""
        logger.info("=" * 80)
        logger.info("TESTING CURRENT (BROKEN) IMPLEMENTATION")
        logger.info("=" * 80)
        
        # Test a representative DICOM pixel
        pixel_x, pixel_y = 256, 256  # Center of image
        
        logger.info(f"Testing DICOM pixel: ({pixel_x}, {pixel_y})")
        
        # Step 1: DICOM pixel → DICOM world (LPS)
        world_x_lps = self.image_position_patient[0] + pixel_x * self.pixel_spacing[1]
        world_y_lps = self.image_position_patient[1] + pixel_y * self.pixel_spacing[0]
        world_z_lps = self.slice_location
        
        logger.info(f"DICOM world (LPS): ({world_x_lps:.3f}, {world_y_lps:.3f}, {world_z_lps:.3f})")
        
        # Step 2: LPS → RAS conversion (current implementation)
        world_x_ras = -world_x_lps  # Current implementation flips X
        world_y_ras = -world_y_lps  # Current implementation flips Y
        world_z_ras = world_z_lps   # Z unchanged
        
        logger.info(f"NIfTI world (RAS): ({world_x_ras:.3f}, {world_y_ras:.3f}, {world_z_ras:.3f})")
        
        # Step 3: World → Voxel using current affine
        world_homogeneous = np.array([world_x_ras, world_y_ras, world_z_ras, 1.0])
        affine_inverse = np.linalg.inv(self.current_affine)
        voxel_homogeneous = affine_inverse @ world_homogeneous
        voxel_x, voxel_y, voxel_z = voxel_homogeneous[:3]
        
        logger.info(f"NIfTI voxel: ({voxel_x:.3f}, {voxel_y:.3f}, {voxel_z:.3f})")
        
        # Check bounds
        x_valid = 0 <= voxel_x <= self.volume_shape[2]
        y_valid = 0 <= voxel_y <= self.volume_shape[1]  
        z_valid = 0 <= voxel_z <= self.volume_shape[0]
        
        logger.info(f"Bounds check: X={x_valid}, Y={y_valid}, Z={z_valid}")
        
        if not (x_valid and y_valid and z_valid):
            logger.warning("❌ Current implementation maps DICOM pixels outside valid voxel space!")
        else:
            logger.info("✅ Current implementation maps correctly")
            
        return voxel_x, voxel_y, voxel_z

    def create_corrected_affine(self) -> np.ndarray:
        """Create a corrected affine matrix that properly maps DICOM to NIfTI space."""
        logger.info("=" * 80)
        logger.info("CREATING CORRECTED AFFINE MATRIX")
        logger.info("=" * 80)
        
        # The issue: current affine origin doesn't account for LPS→RAS conversion
        # We need to set the origin so that DICOM (0,0) maps to NIfTI (0,0)
        
        # DICOM (0,0) in world coordinates (LPS)
        dicom_origin_world_lps = np.array([
            self.image_position_patient[0],  # X in LPS
            self.image_position_patient[1],  # Y in LPS  
            self.slice_location              # Z in LPS
        ])
        
        logger.info(f"DICOM (0,0) world coordinates (LPS): {dicom_origin_world_lps}")
        
        # Convert to RAS for NIfTI
        dicom_origin_world_ras = np.array([
            -dicom_origin_world_lps[0],  # LPS → RAS: flip X
            -dicom_origin_world_lps[1],  # LPS → RAS: flip Y
            dicom_origin_world_lps[2]    # LPS → RAS: Z unchanged
        ])
        
        logger.info(f"DICOM (0,0) world coordinates (RAS): {dicom_origin_world_ras}")
        
        # Create corrected affine matrix
        # We want DICOM (0,0) → NIfTI voxel (0,0)
        # So: voxel = (world - origin) / spacing
        # For voxel (0,0): world = origin
        # Therefore: origin = dicom_origin_world_ras
        
        corrected_affine = np.array([
            [self.pixel_spacing[1], 0.0, 0.0, dicom_origin_world_ras[0]],  # X: col spacing
            [0.0, self.pixel_spacing[0], 0.0, dicom_origin_world_ras[1]],  # Y: row spacing
            [0.0, 0.0, 3.0, dicom_origin_world_ras[2]],                    # Z: slice spacing
            [0.0, 0.0, 0.0, 1.0]                                           # Homogeneous
        ])
        
        logger.info("Corrected affine matrix:")
        logger.info(corrected_affine)
        
        return corrected_affine

    def test_corrected_implementation(self, corrected_affine: np.ndarray):
        """Test the corrected implementation."""
        logger.info("=" * 80)
        logger.info("TESTING CORRECTED IMPLEMENTATION")
        logger.info("=" * 80)
        
        # Test multiple DICOM pixels
        test_pixels = [
            (0, 0),       # Top-left corner
            (256, 256),   # Center
            (511, 511),   # Bottom-right corner
            (100, 150),   # Arbitrary point
        ]
        
        all_valid = True
        
        for pixel_x, pixel_y in test_pixels:
            logger.info(f"\nTesting DICOM pixel: ({pixel_x}, {pixel_y})")
            
            # Step 1: DICOM pixel → DICOM world (LPS)
            world_x_lps = self.image_position_patient[0] + pixel_x * self.pixel_spacing[1]
            world_y_lps = self.image_position_patient[1] + pixel_y * self.pixel_spacing[0]
            world_z_lps = self.slice_location
            
            # Step 2: LPS → RAS conversion  
            world_x_ras = -world_x_lps
            world_y_ras = -world_y_lps
            world_z_ras = world_z_lps
            
            # Step 3: World → Voxel using corrected affine
            world_homogeneous = np.array([world_x_ras, world_y_ras, world_z_ras, 1.0])
            affine_inverse = np.linalg.inv(corrected_affine)
            voxel_homogeneous = affine_inverse @ world_homogeneous
            voxel_x, voxel_y, voxel_z = voxel_homogeneous[:3]
            
            logger.info(f"  DICOM world (LPS): ({world_x_lps:.3f}, {world_y_lps:.3f}, {world_z_lps:.3f})")
            logger.info(f"  NIfTI world (RAS): ({world_x_ras:.3f}, {world_y_ras:.3f}, {world_z_ras:.3f})")
            logger.info(f"  NIfTI voxel: ({voxel_x:.3f}, {voxel_y:.3f}, {voxel_z:.3f})")
            
            # Check bounds
            x_valid = 0 <= voxel_x <= self.volume_shape[2]
            y_valid = 0 <= voxel_y <= self.volume_shape[1]
            z_valid = 0 <= voxel_z <= self.volume_shape[0]
            
            logger.info(f"  Bounds check: X={x_valid} [0,{self.volume_shape[2]}], Y={y_valid} [0,{self.volume_shape[1]}], Z={z_valid} [0,{self.volume_shape[0]}]")
            
            if x_valid and y_valid and z_valid:
                logger.info("  ✅ Valid voxel coordinates")
            else:
                logger.warning("  ❌ Invalid voxel coordinates")
                all_valid = False
                
            # Test round-trip
            voxel_homogeneous_rt = np.array([voxel_x, voxel_y, voxel_z, 1.0])
            world_homogeneous_rt = corrected_affine @ voxel_homogeneous_rt
            world_x_ras_rt, world_y_ras_rt, world_z_ras_rt = world_homogeneous_rt[:3]
            
            # RAS → LPS
            world_x_lps_rt = -world_x_ras_rt
            world_y_lps_rt = -world_y_ras_rt
            world_z_lps_rt = world_z_ras_rt
            
            # World → Pixel
            pixel_x_rt = (world_x_lps_rt - self.image_position_patient[0]) / self.pixel_spacing[1]
            pixel_y_rt = (world_y_lps_rt - self.image_position_patient[1]) / self.pixel_spacing[0]
            
            # Check round-trip accuracy
            pixel_diff_x = abs(pixel_x - pixel_x_rt)
            pixel_diff_y = abs(pixel_y - pixel_y_rt)
            
            if pixel_diff_x < 1e-6 and pixel_diff_y < 1e-6:
                logger.info("  ✅ Round-trip validation: SUCCESS")
            else:
                logger.warning(f"  ❌ Round-trip validation: FAILED (ΔX={pixel_diff_x:.8f}, ΔY={pixel_diff_y:.8f})")
                all_valid = False
        
        return all_valid

    def test_model_coordinate_interpretation(self, corrected_affine: np.ndarray):
        """Test different interpretations of model coordinates."""
        logger.info("=" * 80)
        logger.info("TESTING MODEL COORDINATE INTERPRETATION")
        logger.info("=" * 80)
        
        # Test the example nodule from debug output
        nodule = self.example_nodule_raw
        logger.info(f"Raw model output: center=({nodule['center_x']:.1f}, {nodule['center_y']:.1f}, {nodule['center_z']:.1f})")
        logger.info(f"Raw model dims: size=({nodule['width']:.1f}, {nodule['height']:.1f}, {nodule['depth']:.1f})")
        logger.info(f"Volume shape [Z,Y,X]: {self.volume_shape}")
        
        # Test different coordinate interpretations
        interpretations = [
            {
                'name': 'Original (X,Y,Z)',
                'center_x': nodule['center_x'],
                'center_y': nodule['center_y'], 
                'center_z': nodule['center_z'],
                'width': nodule['width'],
                'height': nodule['height'],
                'depth': nodule['depth']
            },
            {
                'name': 'Swapped (Z,Y,X)',
                'center_x': nodule['center_z'],  # Z→X
                'center_y': nodule['center_y'],  # Y→Y
                'center_z': nodule['center_x'],  # X→Z
                'width': nodule['depth'],        # depth→width
                'height': nodule['height'],     # height→height
                'depth': nodule['width']        # width→depth
            }
        ]
        
        for interp in interpretations:
            logger.info(f"\n--- Testing {interp['name']} interpretation ---")
            
            center_x_voxel = interp['center_x']
            center_y_voxel = interp['center_y']
            center_z_voxel = interp['center_z']
            
            logger.info(f"Interpreted center: ({center_x_voxel:.1f}, {center_y_voxel:.1f}, {center_z_voxel:.1f})")
            
            # Check bounds in voxel space
            x_valid = 0 <= center_x_voxel <= self.volume_shape[2]  # X within width
            y_valid = 0 <= center_y_voxel <= self.volume_shape[1]  # Y within height
            z_valid = 0 <= center_z_voxel <= self.volume_shape[0]  # Z within depth
            
            logger.info(f"Voxel bounds check: X={x_valid} [0,{self.volume_shape[2]}], Y={y_valid} [0,{self.volume_shape[1]}], Z={z_valid} [0,{self.volume_shape[0]}]")
            
            if x_valid and y_valid and z_valid:
                logger.info("✅ Valid voxel coordinates - testing world conversion")
                
                # Convert to world coordinates using resampled spacing
                # Model outputs are in resampled voxel space, not original DICOM space
                center_x_world_ras = center_x_voxel * self.resampled_spacing[0]  # X spacing  
                center_y_world_ras = center_y_voxel * self.resampled_spacing[1]  # Y spacing
                center_z_world_ras = center_z_voxel * self.resampled_spacing[2]  # Z spacing
                
                logger.info(f"Model voxel→world (RAS): ({center_x_world_ras:.1f}, {center_y_world_ras:.1f}, {center_z_world_ras:.1f})")
                
                # Convert RAS → LPS for DICOM compatibility
                center_x_world_lps = -center_x_world_ras
                center_y_world_lps = -center_y_world_ras  
                center_z_world_lps = center_z_world_ras
                
                logger.info(f"DICOM world (LPS): ({center_x_world_lps:.1f}, {center_y_world_lps:.1f}, {center_z_world_lps:.1f})")
                
                # Convert to DICOM pixel coordinates for slice 23.0mm
                if abs(center_z_world_lps - self.slice_location) < 50:  # Within reasonable Z range
                    pixel_x = (center_x_world_lps - self.image_position_patient[0]) / self.pixel_spacing[1]
                    pixel_y = (center_y_world_lps - self.image_position_patient[1]) / self.pixel_spacing[0]
                    
                    logger.info(f"DICOM pixel coordinates: ({pixel_x:.1f}, {pixel_y:.1f})")
                    
                    # Check if pixel coordinates are reasonable
                    pixel_x_valid = 0 <= pixel_x <= 512
                    pixel_y_valid = 0 <= pixel_y <= 512
                    
                    if pixel_x_valid and pixel_y_valid:
                        logger.info("✅ Results in valid DICOM pixel coordinates")
                    else:
                        logger.warning("❌ Results in invalid DICOM pixel coordinates")
                else:
                    logger.warning(f"❌ Z coordinate {center_z_world_lps:.1f} too far from slice {self.slice_location}")
            else:
                logger.warning("❌ Invalid voxel coordinates")

    def develop_complete_pipeline(self):
        """Develop and test the complete corrected pipeline."""
        logger.info("=" * 80)
        logger.info("DEVELOPING COMPLETE CORRECTED PIPELINE")
        logger.info("=" * 80)
        
        # The complete corrected transformation pipeline should:
        # 1. Properly interpret model coordinates (likely Z,Y,X order)
        # 2. Convert model voxel coords to world coords using resampled spacing AND proper origin
        # 3. Convert RAS→LPS correctly
        # 4. Convert world→pixel correctly
        
        nodule = self.example_nodule_raw
        
        # Step 1: Interpret model coordinates (use swapped Z,Y,X interpretation)
        logger.info("STEP 1: Model coordinate interpretation")
        center_x_voxel = nodule['center_z']  # Model Z → actual X
        center_y_voxel = nodule['center_y']  # Model Y → actual Y  
        center_z_voxel = nodule['center_x']  # Model X → actual Z
        
        logger.info(f"Model raw: ({nodule['center_x']:.1f}, {nodule['center_y']:.1f}, {nodule['center_z']:.1f})")
        logger.info(f"Interpreted: ({center_x_voxel:.1f}, {center_y_voxel:.1f}, {center_z_voxel:.1f})")
        
        # Step 2: Convert model voxel coordinates to world coordinates
        # CRITICAL: Model operates in resampled space, but we need to account for the origin offset
        logger.info("\nSTEP 2: Model voxel → world coordinate conversion")
        
        # The model's voxel space origin should align with the resampled NIfTI origin
        # For proper alignment, we need to calculate where the resampled volume origin is
        
        # Get the world coordinates of NIfTI voxel (0,0,0)
        nifti_origin_voxel = np.array([0, 0, 0, 1])  # NIfTI (0,0,0) in homogeneous coords
        corrected_affine = self.create_corrected_affine()
        nifti_origin_world_ras = (corrected_affine @ nifti_origin_voxel)[:3]
        
        logger.info(f"NIfTI voxel (0,0,0) → world (RAS): {nifti_origin_world_ras}")
        
        # Convert model voxel to world using resampled spacing + this origin
        center_x_world_ras = center_x_voxel * self.resampled_spacing[0] + nifti_origin_world_ras[0]
        center_y_world_ras = center_y_voxel * self.resampled_spacing[1] + nifti_origin_world_ras[1]
        center_z_world_ras = center_z_voxel * self.resampled_spacing[2] + nifti_origin_world_ras[2]
        
        logger.info(f"Model world (RAS): ({center_x_world_ras:.1f}, {center_y_world_ras:.1f}, {center_z_world_ras:.1f})")
        
        # Step 3: Convert RAS → LPS
        logger.info("\nSTEP 3: RAS → LPS conversion")
        center_x_world_lps = -center_x_world_ras
        center_y_world_lps = -center_y_world_ras
        center_z_world_lps = center_z_world_ras
        
        logger.info(f"DICOM world (LPS): ({center_x_world_lps:.1f}, {center_y_world_lps:.1f}, {center_z_world_lps:.1f})")
        
        # Step 4: Find intersecting slices and convert to pixel coordinates
        logger.info("\nSTEP 4: Find intersecting slices")
        
        # For this test, check if nodule intersects with our test slice at Z=23.0
        nodule_depth_world = nodule['width'] * self.resampled_spacing[2]  # width→depth after swap
        z_min_world = center_z_world_lps - nodule_depth_world / 2
        z_max_world = center_z_world_lps + nodule_depth_world / 2
        
        logger.info(f"Nodule Z range (LPS): [{z_min_world:.1f}, {z_max_world:.1f}] mm")
        logger.info(f"Test slice Z: {self.slice_location} mm")
        
        if z_min_world <= self.slice_location <= z_max_world:
            logger.info("✅ Nodule intersects with test slice")
            
            # Convert to pixel coordinates
            nodule_width_world = nodule['depth'] * self.resampled_spacing[0]   # depth→width after swap
            nodule_height_world = nodule['height'] * self.resampled_spacing[1]  # height unchanged
            
            x_min_world = center_x_world_lps - nodule_width_world / 2
            x_max_world = center_x_world_lps + nodule_width_world / 2
            y_min_world = center_y_world_lps - nodule_height_world / 2
            y_max_world = center_y_world_lps + nodule_height_world / 2
            
            logger.info(f"Nodule 2D bounding box (LPS): X=[{x_min_world:.1f}, {x_max_world:.1f}], Y=[{y_min_world:.1f}, {y_max_world:.1f}]")
            
            # Convert to pixel coordinates
            pixel_x_min = (x_min_world - self.image_position_patient[0]) / self.pixel_spacing[1]
            pixel_x_max = (x_max_world - self.image_position_patient[0]) / self.pixel_spacing[1]
            pixel_y_min = (y_min_world - self.image_position_patient[1]) / self.pixel_spacing[0]
            pixel_y_max = (y_max_world - self.image_position_patient[1]) / self.pixel_spacing[0]
            
            logger.info(f"DICOM pixel bounding box: X=[{pixel_x_min:.1f}, {pixel_x_max:.1f}], Y=[{pixel_y_min:.1f}, {pixel_y_max:.1f}]")
            
            # Check if reasonable
            bbox_width = pixel_x_max - pixel_x_min
            bbox_height = pixel_y_max - pixel_y_min
            
            reasonable_x = 0 <= pixel_x_min <= 512 and 0 <= pixel_x_max <= 512
            reasonable_y = 0 <= pixel_y_min <= 512 and 0 <= pixel_y_max <= 512
            reasonable_size = 5 <= bbox_width <= 200 and 5 <= bbox_height <= 200
            
            logger.info(f"Bounding box size: {bbox_width:.1f} × {bbox_height:.1f} pixels")
            logger.info(f"Validation: X_bounds={reasonable_x}, Y_bounds={reasonable_y}, Size={reasonable_size}")
            
            if reasonable_x and reasonable_y and reasonable_size:
                logger.info("✅ COMPLETE PIPELINE: Results look reasonable!")
                return True
            else:
                logger.warning("❌ COMPLETE PIPELINE: Results still problematic")
                return False
        else:
            logger.warning("❌ Nodule does not intersect with test slice")
            return False

    def run_all_tests(self):
        """Run all tests and development phases."""
        logger.info("🧪 STARTING COMPREHENSIVE COORDINATE TRANSFORMATION TESTING")
        
        # Test 1: Current broken implementation
        logger.info("\n" + "🔍 TEST 1: Analyzing current implementation issues")
        self.test_current_implementation()
        
        # Test 2: Create corrected affine matrix
        logger.info("\n" + "🔧 TEST 2: Developing corrected affine matrix")
        corrected_affine = self.create_corrected_affine()
        
        # Test 3: Test corrected basic transformations
        logger.info("\n" + "✅ TEST 3: Validating corrected transformations")
        basic_success = self.test_corrected_implementation(corrected_affine)
        
        # Test 4: Model coordinate interpretation
        logger.info("\n" + "🤖 TEST 4: Testing model coordinate interpretations")
        self.test_model_coordinate_interpretation(corrected_affine)
        
        # Test 5: Complete pipeline development
        logger.info("\n" + "🚀 TEST 5: Developing complete corrected pipeline")
        pipeline_success = self.develop_complete_pipeline()
        
        # Summary
        logger.info("\n" + "=" * 80)
        logger.info("🎯 TESTING SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Basic transformation validation: {'✅ PASS' if basic_success else '❌ FAIL'}")
        logger.info(f"Complete pipeline validation: {'✅ PASS' if pipeline_success else '❌ FAIL'}")
        
        if basic_success and pipeline_success:
            logger.info("🎉 ALL TESTS PASSED - Ready to implement fixes!")
            return True
        else:
            logger.info("⚠️  Some tests failed - need more development")
            return False

if __name__ == "__main__":
    tester = CoordinateTransformationTester()
    success = tester.run_all_tests()
    
    if success:
        logger.info("\n✨ Development complete! The corrected transformations are ready for implementation.")
    else:
        logger.info("\n🔧 Development incomplete - continue testing and refinement needed.")