"""
Coordinate Conversion Utilities for CT Lung Nodule Detection

This module provides utilities to convert 3D bounding boxes from model output space
back to individual DICOM slice coordinates for visualization and analysis.

CRITICAL: This implementation correctly handles the fact that MONAI RetinaNet models
output bounding boxes in VOXEL coordinates relative to the resampled image,
not world coordinates.

Coordinate transformation pipeline:
1. Model voxel coords (RAS space) → World coords (RAS space)
2. World coords (RAS space) → World coords (LPS space)
3. World coords (LPS space) → DICOM pixel coords

Key fixes implemented:
- Proper voxel-to-world conversion using resampled spacing
- RAS→LPS coordinate system transformation for DICOM compatibility
- Correct pixel spacing index usage (X uses column spacing, Y uses row spacing)
- Comprehensive validation and debug logging
"""

import logging
from pathlib import Path

import numpy as np


class CoordinateConverter:
    """
    Handles coordinate transformations between different spaces:
    1. Model voxel space (RAS, 0.7*0.7*1.25mm resampled resolution)
    2. World coordinate space (RAS → LPS transformation)
    3. Individual DICOM slice pixel coordinates (2D bounding boxes)

    The pipeline correctly converts from MONAI model outputs (voxel coordinates)
    to DICOM pixel coordinates with proper coordinate system transformations.
    """

    def __init__(self, logger: logging.Logger):
        self.logger = logger

    def create_transformation_record(self, metadata: dict) -> dict:
        """
        Create a comprehensive transformation record from DICOM metadata.

        Args:
            metadata: Dictionary containing DICOM metadata and slice info

        Returns:
            Transformation record for coordinate conversion
        """
        transformation_record = {
            "original_spacing": metadata.get("original_spacing"),
            "original_origin": metadata.get("original_origin"),
            "original_direction": metadata.get("original_direction"),
            "original_size": metadata.get("original_size"),
            "resampled_spacing": [0.7, 0.7, 1.25],  # Target spacing from model
            "slice_metadata": metadata.get("slice_metadata", []),
            "patient_id": metadata.get("patient_id"),
            "series_instance_uid": metadata.get("series_instance_uid"),
            "nifti_path": metadata.get("nifti_path"),
        }

        return transformation_record

    def world_to_voxel_coordinates(
        self,
        world_coords: np.ndarray,
        spacing: tuple[float, float, float],
        origin: tuple[float, float, float],
    ) -> np.ndarray:
        """
        Convert world coordinates to voxel coordinates.

        Args:
            world_coords: World coordinates (x, y, z) in mm
            spacing: Voxel spacing (x, y, z) in mm
            origin: Image origin (x, y, z) in mm

        Returns:
            Voxel coordinates (i, j, k)
        """
        world_coords = np.array(world_coords)
        spacing = np.array(spacing)
        origin = np.array(origin)

        voxel_coords = (world_coords - origin) / spacing
        return voxel_coords

    def voxel_to_world_coordinates(
        self,
        voxel_coords: np.ndarray,
        spacing: tuple[float, float, float],
        origin: tuple[float, float, float],
    ) -> np.ndarray:
        """
        Convert voxel coordinates to world coordinates.

        Args:
            voxel_coords: Voxel coordinates (i, j, k)
            spacing: Voxel spacing (x, y, z) in mm
            origin: Image origin (x, y, z) in mm

        Returns:
            World coordinates (x, y, z) in mm
        """
        voxel_coords = np.array(voxel_coords)
        spacing = np.array(spacing)
        origin = np.array(origin)

        world_coords = voxel_coords * spacing + origin
        return world_coords

    def convert_3d_bbox_to_dicom_slices(
        self,
        nodule: dict,
        transformation_record: dict,
        series_metadata: dict | None = None,
    ) -> list[dict]:
        """
        Convert 3D bounding box from model output to 2D bounding boxes
        on individual DICOM slices.

        CRITICAL: Model outputs are in VOXEL coordinates relative to resampled image,
        NOT world coordinates.

        Coordinate conversion pipeline:
        1. Model voxel coords (RAS) → World coords (RAS)
        2. World coords (RAS) → World coords (LPS)
        3. World coords (LPS) → DICOM pixel coords

        Args:
            nodule: Dictionary containing 3D nodule information from model (in voxel coordinates)
            transformation_record: Transformation information from DICOM conversion
            series_metadata: Optional series-level metadata for traceability

        Returns:
            List of dictionaries, each containing 2D bounding box for a DICOM slice
        """
        try:
            # Extract model outputs (these are VOXEL coordinates in resampled space)
            center_x_voxel = nodule["center_x"]  # Voxel coordinate in resampled image
            center_y_voxel = nodule["center_y"]  # Voxel coordinate in resampled image
            center_z_voxel = nodule["center_z"]  # Voxel coordinate in resampled image
            width_voxel = nodule["width"]  # Width in voxels
            height_voxel = nodule["height"]  # Height in voxels
            depth_voxel = nodule["depth"]  # Depth in voxels

            # The simple_affine matrix now properly maps voxel coordinates to DICOM world coordinates
            # Extract the origin from the affine matrix stored in transformation record
            simple_affine = transformation_record.get("affine_matrix")
            if simple_affine:
                # Extract origin from affine matrix [0:3, 3]
                affine_origin_ras = [
                    simple_affine[0][3],
                    simple_affine[1][3],
                    simple_affine[2][3],
                ]
                self.logger.info(
                    f"COORD DEBUG: Simple affine origin (RAS): {affine_origin_ras}"
                )
            else:
                affine_origin_ras = [0.0, 0.0, 0.0]  # Fallback

            # Validate inputs
            if any(
                coord < 0 for coord in [center_x_voxel, center_y_voxel, center_z_voxel]
            ):
                self.logger.warning(
                    f"Negative voxel coordinates detected: ({center_x_voxel},{center_y_voxel},{center_z_voxel})"
                )

            if any(size <= 0 for size in [width_voxel, height_voxel, depth_voxel]):
                self.logger.warning(
                    f"Invalid nodule dimensions: ({width_voxel},{height_voxel},{depth_voxel})"
                )

            self.logger.debug(
                f"Model output coords: center=({center_x_voxel:.1f},{center_y_voxel:.1f},{center_z_voxel:.1f}), size=({width_voxel:.1f},{height_voxel:.1f},{depth_voxel:.1f})"
            )

            # STEP 2: Convert model voxel outputs to world coordinates
            # CRITICAL: Model outputs are in VOXEL coordinates relative to resampled image space
            # Need to convert voxel coords → world coords using resampled spacing and affine origin
            
            resampled_spacing = transformation_record.get("resampled_spacing", [0.7, 0.7, 1.25])
            
            # Extract origin from the affine matrix stored in transformation record
            affine_matrix = transformation_record.get("affine_matrix")
            if affine_matrix:
                affine_origin_ras = [
                    affine_matrix[0][3],  # X origin in RAS
                    affine_matrix[1][3],  # Y origin in RAS  
                    affine_matrix[2][3],  # Z origin in RAS
                ]
            else:
                affine_origin_ras = [0.0, 0.0, 0.0]  # Fallback
            
            # =============================================================================
            # COMPREHENSIVE COORDINATE SYSTEM DEBUGGING FROM FIRST PRINCIPLES
            # =============================================================================
            
            self.logger.info("="*80)
            self.logger.info("COORDINATE SYSTEM DEBUG - FIRST PRINCIPLES ANALYSIS")
            self.logger.info("="*80)
            
            # STEP 1: Examine raw model outputs
            self.logger.info("STEP 1: RAW MODEL OUTPUTS")
            self.logger.info(f"  Model nodule ID: {nodule.get('nodule_id', 'unknown')}")
            self.logger.info(f"  Raw center_x: {center_x_voxel:.3f}")
            self.logger.info(f"  Raw center_y: {center_y_voxel:.3f}")  
            self.logger.info(f"  Raw center_z: {center_z_voxel:.3f}")
            self.logger.info(f"  Raw width: {width_voxel:.3f}")
            self.logger.info(f"  Raw height: {height_voxel:.3f}")
            self.logger.info(f"  Raw depth: {depth_voxel:.3f}")
            
            # STEP 2: Examine volume and tensor dimensions
            volume_shape = transformation_record.get("volume_shape", [0, 0, 0])
            self.logger.info("STEP 2: VOLUME AND TENSOR DIMENSIONS")
            self.logger.info(f"  Volume shape from np.stack: {volume_shape} (assumed Z,Y,X)")
            self.logger.info(f"  Resampled spacing: {transformation_record.get('resampled_spacing', 'unknown')} (assumed X,Y,Z)")
            
            # Check coordinate bounds vs volume dimensions
            self.logger.info("STEP 3: COORDINATE BOUNDS VALIDATION")
            if len(volume_shape) >= 3:
                self.logger.info(f"  Volume Z-dimension [0, {volume_shape[0]}]: center_z={center_z_voxel:.1f} ({'VALID' if 0 <= center_z_voxel <= volume_shape[0] else 'INVALID'})")
                self.logger.info(f"  Volume Y-dimension [0, {volume_shape[1]}]: center_y={center_y_voxel:.1f} ({'VALID' if 0 <= center_y_voxel <= volume_shape[1] else 'INVALID'})")
                self.logger.info(f"  Volume X-dimension [0, {volume_shape[2]}]: center_x={center_x_voxel:.1f} ({'VALID' if 0 <= center_x_voxel <= volume_shape[2] else 'INVALID'})")
                
                # Test hypothesis: Are model coords actually in (Z,Y,X) order?
                z_in_range = 0 <= center_x_voxel <= volume_shape[0]  # center_x as Z
                y_in_range = 0 <= center_y_voxel <= volume_shape[1]  # center_y as Y  
                x_in_range = 0 <= center_z_voxel <= volume_shape[2]  # center_z as X
                
                self.logger.info("HYPOTHESIS TEST: Model coordinates in (Z,Y,X) order")
                self.logger.info(f"  Z-coordinate (center_x): {center_x_voxel:.1f} in [0,{volume_shape[0]}] = {z_in_range}")
                self.logger.info(f"  Y-coordinate (center_y): {center_y_voxel:.1f} in [0,{volume_shape[1]}] = {y_in_range}")
                self.logger.info(f"  X-coordinate (center_z): {center_z_voxel:.1f} in [0,{volume_shape[2]}] = {x_in_range}")
                self.logger.info(f"  Hypothesis validity: {z_in_range and y_in_range and x_in_range}")
            
            # STEP 4: Examine DICOM coordinate system details
            slice_metadata = transformation_record["slice_metadata"]
            if slice_metadata and len(slice_metadata) > 0:
                first_slice = slice_metadata[0]
                last_slice = slice_metadata[-1] if len(slice_metadata) > 1 else first_slice
                
                self.logger.info("STEP 4: DICOM COORDINATE SYSTEM ANALYSIS")
                self.logger.info(f"  Number of DICOM slices: {len(slice_metadata)}")
                self.logger.info(f"  First slice location: {first_slice.get('slice_location', 'unknown')}")
                self.logger.info(f"  Last slice location: {last_slice.get('slice_location', 'unknown')}")
                self.logger.info(f"  First slice ImagePositionPatient: {first_slice.get('image_position_patient', 'unknown')}")
                self.logger.info(f"  First slice PixelSpacing: {first_slice.get('pixel_spacing', 'unknown')}")
                self.logger.info(f"  First slice ImageOrientationPatient: {first_slice.get('image_orientation_patient', 'unknown')}")
                
                # Calculate actual DICOM coordinate range
                slice_locations = [s.get('slice_location') for s in slice_metadata if s.get('slice_location') is not None]
                if slice_locations:
                    min_z, max_z = min(slice_locations), max(slice_locations)
                    self.logger.info(f"  DICOM Z-range: [{min_z:.3f}, {max_z:.3f}] mm")
                    self.logger.info(f"  DICOM Z-span: {max_z - min_z:.3f} mm")
            
            # STEP 5: Examine affine matrix and coordinate transformation
            affine_matrix = transformation_record.get("affine_matrix")
            if affine_matrix:
                self.logger.info("STEP 5: AFFINE MATRIX ANALYSIS")
                self.logger.info(f"  Affine matrix origin: [{affine_matrix[0][3]:.3f}, {affine_matrix[1][3]:.3f}, {affine_matrix[2][3]:.3f}]")
                self.logger.info(f"  Affine matrix diagonal: [{affine_matrix[0][0]:.3f}, {affine_matrix[1][1]:.3f}, {affine_matrix[2][2]:.3f}]")
                self.logger.info(f"  Affine determinant: {np.linalg.det(np.array(affine_matrix)[:3, :3]):.6f}")
                
                # Check if affine is simple or complex
                is_diagonal = (abs(affine_matrix[0][1]) < 0.001 and abs(affine_matrix[0][2]) < 0.001 and
                              abs(affine_matrix[1][0]) < 0.001 and abs(affine_matrix[1][2]) < 0.001 and
                              abs(affine_matrix[2][0]) < 0.001 and abs(affine_matrix[2][1]) < 0.001)
                self.logger.info(f"  Affine matrix type: {'Simple (diagonal)' if is_diagonal else 'Complex (non-diagonal)'}")
                
                if is_diagonal:
                    # For simple affine, the coordinate system should be straightforward
                    self.logger.info("  Simple affine detected - coordinate mapping should be direct")
                    self.logger.info(f"    X-axis scaling: {affine_matrix[0][0]:.6f}")
                    self.logger.info(f"    Y-axis scaling: {affine_matrix[1][1]:.6f}")
                    self.logger.info(f"    Z-axis scaling: {affine_matrix[2][2]:.6f}")
                    
                    # Check if this is RAS (positive) or LPS (negative) style
                    x_positive = affine_matrix[0][0] > 0
                    y_positive = affine_matrix[1][1] > 0
                    z_positive = affine_matrix[2][2] > 0
                    
                    self.logger.info(f"    X-axis direction: {'Positive (Right)' if x_positive else 'Negative (Left)'}")
                    self.logger.info(f"    Y-axis direction: {'Positive (Anterior)' if y_positive else 'Negative (Posterior)'}")
                    self.logger.info(f"    Z-axis direction: {'Positive (Superior)' if z_positive else 'Negative (Inferior)'}")
                    
                    if x_positive and y_positive and z_positive:
                        coord_system = "RAS (Right-Anterior-Superior)"
                    elif not x_positive and not y_positive and z_positive:
                        coord_system = "LPS (Left-Posterior-Superior)"
                    else:
                        coord_system = f"Mixed/Custom ({('R' if x_positive else 'L')}{('A' if y_positive else 'P')}{('S' if z_positive else 'I')})"
                    
                    self.logger.info(f"    Inferred coordinate system: {coord_system}")
            
            self.logger.info("="*80)
            
            # =============================================================================
            # COORDINATE TRANSFORMATION DEBUGGING
            # =============================================================================
            
            # Get spacing and origin information
            resampled_spacing = transformation_record.get("resampled_spacing", [0.7, 0.7, 1.25])
            
            # Debug: Check slice location range vs affine-computed Z range
            slice_metadata = transformation_record["slice_metadata"]
            slice_locations = [
                s.get("slice_location")
                for s in slice_metadata
                if s.get("slice_location") is not None
            ]
            
            self.logger.info("STEP 6: COORDINATE TRANSFORMATION ANALYSIS")
            if slice_locations:
                min_slice_z, max_slice_z = min(slice_locations), max(slice_locations)
                first_slice_z = slice_locations[0] if slice_locations else 0.0
                last_slice_z = slice_locations[-1] if slice_locations else 0.0
                
                self.logger.info(f"  DICOM slice Z range: [{min_slice_z:.3f}, {max_slice_z:.3f}] mm")
                self.logger.info(f"  First slice Z: {first_slice_z:.3f}, Last slice Z: {last_slice_z:.3f}")
                self.logger.info(f"  Affine origin Z: {affine_origin_ras[2]:.3f}")
                
                # Determine which Z origin to use
                corrected_z_origin = first_slice_z
                self.logger.info(f"  Using first slice position as Z origin: {corrected_z_origin:.3f} mm")
            else:
                corrected_z_origin = affine_origin_ras[2]
                self.logger.info(f"  No slice locations found, using affine origin Z: {corrected_z_origin:.3f}")
            
            # CRITICAL FIX: Apply consistent coordinate order correction
            # The model outputs coordinates in a different order than expected
            # Based on analysis: model outputs are in (Z,Y,X) order while we expect (X,Y,Z)
            volume_shape = transformation_record.get("volume_shape", [0, 0, 0])
            if len(volume_shape) >= 3:
                self.logger.info("APPLYING CONSISTENT COORDINATE ORDER CORRECTION: (center_x, center_y, center_z) → (center_z, center_y, center_x)")
                self.logger.info(f"  Original model output: center_x={center_x_voxel:.3f}, center_y={center_y_voxel:.3f}, center_z={center_z_voxel:.3f}")
                
                # Consistently swap X and Z coordinates to match (Z,Y,X) → (X,Y,Z) conversion
                # Model outputs: center_x=Z_voxel, center_y=Y_voxel, center_z=X_voxel
                # Convert to: center_x=X_voxel, center_y=Y_voxel, center_z=Z_voxel
                corrected_x = center_z_voxel  # Model's center_z is actually X
                corrected_y = center_y_voxel  # Model's center_y is actually Y
                corrected_z = center_x_voxel  # Model's center_x is actually Z
                
                corrected_width = depth_voxel   # Model's depth is actually width
                corrected_height = height_voxel # Model's height is actually height
                corrected_depth = width_voxel   # Model's width is actually depth
                
                self.logger.info(f"  Corrected coordinates: center_x={corrected_x:.3f}, center_y={corrected_y:.3f}, center_z={corrected_z:.3f}")
                self.logger.info(f"  Corrected dimensions: width={corrected_width:.3f}, height={corrected_height:.3f}, depth={corrected_depth:.3f}")
                
                # Validate corrected coordinates are within bounds
                x_valid = 0 <= corrected_x <= volume_shape[2]  # X should be within width
                y_valid = 0 <= corrected_y <= volume_shape[1]  # Y should be within height
                z_valid = 0 <= corrected_z <= volume_shape[0]  # Z should be within depth
                
                self.logger.info(f"  Validation: X={x_valid} (0 <= {corrected_x:.1f} <= {volume_shape[2]}), Y={y_valid} (0 <= {corrected_y:.1f} <= {volume_shape[1]}), Z={z_valid} (0 <= {corrected_z:.1f} <= {volume_shape[0]})")
                
                if x_valid and y_valid and z_valid:
                    # Apply corrections
                    center_x_voxel = corrected_x
                    center_y_voxel = corrected_y
                    center_z_voxel = corrected_z
                    width_voxel = corrected_width
                    height_voxel = corrected_height
                    depth_voxel = corrected_depth
                    self.logger.info("  ✓ Coordinate correction applied successfully")
                else:
                    self.logger.warning(f"  ⚠ Coordinate correction failed validation - using original coordinates")
                    # Keep original coordinates but warn about potential issues
            
            # Convert voxel coordinates to world coordinates using resampled spacing
            self.logger.info("STEP 7: VOXEL TO WORLD COORDINATE CONVERSION")
            self.logger.info(f"  Resampled spacing [X,Y,Z]: {resampled_spacing}")
            self.logger.info(f"  Affine origin [X,Y,Z]: {affine_origin_ras}")
            
            center_x_world_ras = center_x_voxel * resampled_spacing[0] + affine_origin_ras[0]
            center_y_world_ras = center_y_voxel * resampled_spacing[1] + affine_origin_ras[1]  
            center_z_world_ras = center_z_voxel * resampled_spacing[2] + corrected_z_origin
            
            self.logger.info(f"  Voxel coordinates: ({center_x_voxel:.3f}, {center_y_voxel:.3f}, {center_z_voxel:.3f})")
            self.logger.info(f"  World coordinates (RAS): ({center_x_world_ras:.3f}, {center_y_world_ras:.3f}, {center_z_world_ras:.3f})")
            
            # Convert voxel dimensions to world dimensions using resampled spacing
            width_world = width_voxel * resampled_spacing[0]
            height_world = height_voxel * resampled_spacing[1] 
            depth_world = depth_voxel * resampled_spacing[2]
            
            self.logger.info(f"  Voxel dimensions: ({width_voxel:.3f}, {height_voxel:.3f}, {depth_voxel:.3f})")
            self.logger.info(f"  World dimensions: ({width_world:.3f}, {height_world:.3f}, {depth_world:.3f})")
            
            # =============================================================================
            # COORDINATE SYSTEM CONVERSION ANALYSIS WITH FIXES
            # =============================================================================
            
            self.logger.info("STEP 8: COORDINATE SYSTEM CONVERSION ANALYSIS WITH FIXES")
            
            # Check what coordinate system we're actually dealing with
            affine_matrix = transformation_record.get("affine_matrix")
            if affine_matrix:
                x_positive = affine_matrix[0][0] > 0
                y_positive = affine_matrix[1][1] > 0
                z_positive = affine_matrix[2][2] > 0
                
                if x_positive and y_positive and z_positive:
                    detected_system = "RAS"
                    self.logger.info("  Detected coordinate system: RAS (Right-Anterior-Superior)")
                    
                    # CRITICAL FIX: Even for RAS systems, we need to convert to LPS for DICOM compatibility
                    # Because ImagePositionPatient is always in LPS coordinates
                    self.logger.info("  Converting RAS → LPS for DICOM ImagePositionPatient compatibility")
                    
                    # Apply RAS → LPS conversion
                    center_x_world_lps = -center_x_world_ras  # Right → Left
                    center_y_world_lps = -center_y_world_ras  # Anterior → Posterior
                    center_z_world_lps = center_z_world_ras   # Superior unchanged
                    
                elif not x_positive and not y_positive and z_positive:
                    detected_system = "LPS"
                    self.logger.info("  Detected coordinate system: LPS (Left-Posterior-Superior)")
                    self.logger.info("  No conversion needed - already in LPS")
                    
                    # No conversion needed
                    center_x_world_lps = center_x_world_ras
                    center_y_world_lps = center_y_world_ras
                    center_z_world_lps = center_z_world_ras
                    
                else:
                    detected_system = "MIXED"
                    self.logger.warning(f"  Detected mixed coordinate system: {('R' if x_positive else 'L')}{('A' if y_positive else 'P')}{('S' if z_positive else 'I')}")
                    self.logger.warning("  Applying standard RAS → LPS conversion as fallback")
                    
                    # Apply standard conversion as fallback
                    center_x_world_lps = -center_x_world_ras
                    center_y_world_lps = -center_y_world_ras
                    center_z_world_lps = center_z_world_ras
            else:
                detected_system = "UNKNOWN"
                self.logger.warning("  No affine matrix available - assuming RAS → LPS conversion needed")
                center_x_world_lps = -center_x_world_ras
                center_y_world_lps = -center_y_world_ras  
                center_z_world_lps = center_z_world_ras
            
            self.logger.info(f"  World coordinates after conversion: ({center_x_world_lps:.3f}, {center_y_world_lps:.3f}, {center_z_world_lps:.3f})")
            
            # Dimensions don't change during coordinate system conversion
            width_world_lps = width_world
            height_world_lps = height_world
            depth_world_lps = depth_world

            # Calculate bounding box extents in final coordinate system
            x_min_world_lps = center_x_world_lps - width_world_lps / 2
            x_max_world_lps = center_x_world_lps + width_world_lps / 2
            y_min_world_lps = center_y_world_lps - height_world_lps / 2
            y_max_world_lps = center_y_world_lps + height_world_lps / 2
            z_min_world_lps = center_z_world_lps - depth_world_lps / 2
            z_max_world_lps = center_z_world_lps + depth_world_lps / 2

            self.logger.info("STEP 9: BOUNDING BOX CALCULATION")
            self.logger.info(f"  3D Bounding box center: ({center_x_world_lps:.3f}, {center_y_world_lps:.3f}, {center_z_world_lps:.3f})")
            self.logger.info(f"  3D Bounding box extents: X=[{x_min_world_lps:.3f}, {x_max_world_lps:.3f}], Y=[{y_min_world_lps:.3f}, {y_max_world_lps:.3f}], Z=[{z_min_world_lps:.3f}, {z_max_world_lps:.3f}]")

            # =============================================================================
            # SLICE INTERSECTION ANALYSIS
            # =============================================================================
            
            self.logger.info("STEP 10: SLICE INTERSECTION ANALYSIS")
            slice_results = []
            
            # Debug: Check slice location range vs nodule Z range
            if slice_locations:
                min_slice_z, max_slice_z = min(slice_locations), max(slice_locations)
                self.logger.info(f"  DICOM slice Z range: [{min_slice_z:.3f}, {max_slice_z:.3f}] mm")
                self.logger.info(f"  Nodule Z range: [{z_min_world_lps:.3f}, {z_max_world_lps:.3f}] mm")
                
                # Check overlap
                z_overlap = not (z_max_world_lps < min_slice_z or z_min_world_lps > max_slice_z)
                self.logger.info(f"  Z ranges overlap: {z_overlap}")
                
                if z_overlap:
                    overlap_start = max(z_min_world_lps, min_slice_z)
                    overlap_end = min(z_max_world_lps, max_slice_z)
                    self.logger.info(f"  Overlap region: [{overlap_start:.3f}, {overlap_end:.3f}] mm")
                else:
                    if z_max_world_lps < min_slice_z:
                        gap = min_slice_z - z_max_world_lps
                        self.logger.warning(f"  Nodule is {gap:.3f} mm BELOW the slice volume")
                    else:
                        gap = z_min_world_lps - max_slice_z
                        self.logger.warning(f"  Nodule is {gap:.3f} mm ABOVE the slice volume")
            
            intersecting_slices = 0
            for slice_info in slice_metadata:
                slice_location = slice_info.get("slice_location")
                if slice_location is None:
                    continue

                # Check if this slice intersects with the 3D bounding box Z range
                if z_min_world_lps <= slice_location <= z_max_world_lps:
                    intersecting_slices += 1
                    
                    # Get DICOM slice parameters
                    pixel_spacing = slice_info.get("pixel_spacing", [1.0, 1.0])  # [row_spacing, col_spacing] = [Y, X]
                    image_position = slice_info.get("image_position_patient", [0, 0, slice_location])  # [X, Y, Z] in LPS coordinate system
                    
                    # CRITICAL FIX: Use consistent slice location from slice_location field rather than ImagePositionPatient[2]
                    # ImagePositionPatient[2] can be inconsistent, while slice_location is the authoritative Z position
                    if len(image_position) >= 3:
                        # Replace the Z component with the authoritative slice_location
                        image_position = [image_position[0], image_position[1], slice_location]
                    
                    self.logger.info(f"  Processing intersecting slice at Z={slice_location:.3f}")
                    self.logger.info(f"    DICOM ImagePositionPatient: {image_position}")
                    self.logger.info(f"    DICOM PixelSpacing [Y,X]: {pixel_spacing}")

                    # Convert world coordinates to DICOM pixel coordinates
                    # CRITICAL FIX: DICOM PixelSpacing = [row_spacing, col_spacing] = [Y_spacing, X_spacing]
                    # X direction uses column spacing (index 1), Y direction uses row spacing (index 0)
                    # IMPORTANT: ImagePositionPatient is in LPS coordinates
                    
                    # Calculate pixel coordinates with proper bounds checking
                    pixel_x_min = (x_min_world_lps - image_position[0]) / pixel_spacing[1]  # X uses column spacing
                    pixel_x_max = (x_max_world_lps - image_position[0]) / pixel_spacing[1]  # X uses column spacing
                    pixel_y_min = (y_min_world_lps - image_position[1]) / pixel_spacing[0]  # Y uses row spacing
                    pixel_y_max = (y_max_world_lps - image_position[1]) / pixel_spacing[0]  # Y uses row spacing

                    # Ensure proper ordering and positive coordinates
                    pixel_x_min, pixel_x_max = min(pixel_x_min, pixel_x_max), max(pixel_x_min, pixel_x_max)
                    pixel_y_min, pixel_y_max = min(pixel_y_min, pixel_y_max), max(pixel_y_min, pixel_y_max)
                    
                    # CRITICAL FIX: Add bounds validation and clipping
                    # Get expected image dimensions (typically 512x512 for CT)
                    expected_width = 512  # Standard CT width
                    expected_height = 512  # Standard CT height
                    
                    # Validate and clip pixel coordinates to image bounds
                    pixel_x_min = max(0, min(pixel_x_min, expected_width - 1))
                    pixel_x_max = max(0, min(pixel_x_max, expected_width - 1))
                    pixel_y_min = max(0, min(pixel_y_min, expected_height - 1))
                    pixel_y_max = max(0, min(pixel_y_max, expected_height - 1))
                    
                    # Ensure minimum bounding box size
                    if pixel_x_max <= pixel_x_min:
                        pixel_x_max = min(pixel_x_min + 1, expected_width - 1)
                    if pixel_y_max <= pixel_y_min:
                        pixel_y_max = min(pixel_y_min + 1, expected_height - 1)

                    self.logger.info(f"    World→Pixel conversion:")
                    self.logger.info(f"      X: [{x_min_world_lps:.3f}, {x_max_world_lps:.3f}] mm → [{pixel_x_min:.1f}, {pixel_x_max:.1f}] pixels")
                    self.logger.info(f"      Y: [{y_min_world_lps:.3f}, {y_max_world_lps:.3f}] mm → [{pixel_y_min:.1f}, {pixel_y_max:.1f}] pixels")
                    
                    # Validate pixel coordinates
                    bbox_width = int(pixel_x_max - pixel_x_min)
                    bbox_height = int(pixel_y_max - pixel_y_min)

                    # CRITICAL FIX: Add comprehensive validation with better error handling
                    if bbox_width <= 0 or bbox_height <= 0:
                        self.logger.warning(f"    Invalid bounding box dimensions: {bbox_width}x{bbox_height} - skipping slice")
                        continue

                    if pixel_x_max < 0 or pixel_y_max < 0 or pixel_x_min >= expected_width or pixel_y_min >= expected_height:
                        self.logger.warning(f"    Bounding box completely outside image bounds - skipping slice")
                        continue
                    
                    # Warn about large bounding boxes but don't skip them if they're within reasonable bounds
                    if bbox_width > 200 or bbox_height > 200:
                        self.logger.warning(f"    Large bounding box detected: {bbox_width}x{bbox_height} pixels - may indicate coordinate error")
                        # Continue processing but flag for review

                    slice_result = {
                        "dicom_file": slice_info["dicom_file"],
                        "instance_number": slice_info["instance_number"],
                        "slice_location": slice_location,
                        "bbox_2d": {
                            "x_min": int(pixel_x_min),
                            "y_min": int(pixel_y_min),
                            "x_max": int(pixel_x_max),
                            "y_max": int(pixel_y_max),
                            "width": bbox_width,
                            "height": bbox_height,
                        },
                        "world_coords": {
                            "x_min": x_min_world_lps,
                            "x_max": x_max_world_lps,
                            "y_min": y_min_world_lps,
                            "y_max": y_max_world_lps,
                            "z_location": slice_location,
                        },
                        "nodule_metadata": {
                            "nodule_id": nodule["nodule_id"],
                            "confidence_score": nodule["confidence_score"],
                            "label": nodule["label"],
                        },
                        "series_metadata": series_metadata or {},
                    }

                    slice_results.append(slice_result)
                    self.logger.info(f"    ✓ Valid slice result added")

            self.logger.info(f"FINAL RESULT: {intersecting_slices} intersecting slices found, {len(slice_results)} valid slice results created")
            self.logger.info("="*80)

            # Validate coordinate conversion results
            self._validate_coordinate_conversion(
                nodule,
                slice_metadata,
                slice_results,
                center_x_world_lps,
                center_y_world_lps,
                center_z_world_lps,
                z_min_world_lps,
                z_max_world_lps,
            )

            # Log coordinate transformation summary
            total_slices_in_volume = len(slice_metadata)
            intersecting_slices = len(slice_results)
            self.logger.info(
                f"Nodule {nodule['nodule_id']} coordinate conversion summary:"
            )
            self.logger.info(
                f"  • Model coords (RAS): center=({center_x_world_ras:.1f},{center_y_world_ras:.1f},{center_z_world_ras:.1f}) mm"
            )
            self.logger.info(
                f"  • DICOM coords (LPS): center=({center_x_world_lps:.1f},{center_y_world_lps:.1f},{center_z_world_lps:.1f}) mm"
            )
            self.logger.info(
                f"  • Z range (LPS): {z_min_world_lps:.1f} to {z_max_world_lps:.1f} mm"
            )
            self.logger.info(
                f"  • Intersects {intersecting_slices}/{total_slices_in_volume} DICOM slices"
            )

            return slice_results

        except Exception as e:
            self.logger.error(f"Failed to convert 3D bbox to DICOM slices: {e}")
            return []

    def _validate_coordinate_conversion(
        self,
        nodule: dict,
        slice_metadata: list[dict],
        slice_results: list[dict],
        center_x_lps: float,
        center_y_lps: float,
        center_z_lps: float,
        z_min_lps: float,
        z_max_lps: float,
    ) -> None:
        """
        Validate coordinate conversion results for potential issues.
        """
        try:
            # Check Z-coordinate range validity
            slice_locations = [
                s["slice_location"]
                for s in slice_metadata
                if s.get("slice_location") is not None
            ]
            if slice_locations:
                volume_z_min, volume_z_max = min(slice_locations), max(slice_locations)

                # Check if nodule Z range is within volume bounds (with reasonable tolerance)
                z_tolerance = abs(volume_z_max - volume_z_min) * 0.1  # 10% tolerance

                if (
                    z_max_lps < volume_z_min - z_tolerance
                    or z_min_lps > volume_z_max + z_tolerance
                ):
                    self.logger.warning(
                        f"Nodule {nodule['nodule_id']} Z range ({z_min_lps:.1f},{z_max_lps:.1f}) "
                        f"outside volume bounds ({volume_z_min:.1f},{volume_z_max:.1f}) - "
                        f"possible coordinate conversion error"
                    )

                # Check if nodule center is reasonable
                if not (
                    volume_z_min - z_tolerance
                    <= center_z_lps
                    <= volume_z_max + z_tolerance
                ):
                    self.logger.warning(
                        f"Nodule {nodule['nodule_id']} center Z ({center_z_lps:.1f}) "
                        f"outside volume bounds - possible coordinate system issue"
                    )

            # Validate pixel coordinates make sense
            for result in slice_results:
                bbox = result["bbox_2d"]
                if bbox["width"] <= 0 or bbox["height"] <= 0:
                    self.logger.warning(
                        f"Invalid bounding box dimensions: {bbox['width']}x{bbox['height']} "
                        f"in slice {result['slice_location']}"
                    )

                if bbox["x_max"] < 0 or bbox["y_max"] < 0:
                    self.logger.warning(
                        f"Bounding box completely outside image bounds in slice {result['slice_location']}"
                    )

                # Check for extremely large bounding boxes (likely coordinate errors)
                if bbox["width"] > 1000 or bbox["height"] > 1000:
                    self.logger.warning(
                        f"Suspiciously large bounding box ({bbox['width']}x{bbox['height']}) "
                        f"in slice {result['slice_location']} - possible coordinate error"
                    )

        except Exception as e:
            self.logger.debug(f"Coordinate validation failed: {e}")

    def save_slice_annotations(
        self, slice_results: list[dict], output_path: Path
    ) -> None:
        """
        Save slice-level annotations to CSV format.

        Args:
            slice_results: List of slice-level bounding box results
            output_path: Path to save CSV file
        """
        try:
            import pandas as pd

            # Flatten the results for CSV export
            rows = []
            for result in slice_results:
                row = {
                    "dicom_file": result["dicom_file"],
                    "instance_number": result["instance_number"],
                    "slice_location": result["slice_location"],
                    "nodule_id": result["nodule_metadata"]["nodule_id"],
                    "confidence_score": result["nodule_metadata"]["confidence_score"],
                    "label": result["nodule_metadata"]["label"],
                    "bbox_x_min": result["bbox_2d"]["x_min"],
                    "bbox_y_min": result["bbox_2d"]["y_min"],
                    "bbox_x_max": result["bbox_2d"]["x_max"],
                    "bbox_y_max": result["bbox_2d"]["y_max"],
                    "bbox_width": result["bbox_2d"]["width"],
                    "bbox_height": result["bbox_2d"]["height"],
                    "world_x_min": result["world_coords"]["x_min"],
                    "world_x_max": result["world_coords"]["x_max"],
                    "world_y_min": result["world_coords"]["y_min"],
                    "world_y_max": result["world_coords"]["y_max"],
                    "world_z_location": result["world_coords"]["z_location"],
                }
                rows.append(row)

            df = pd.DataFrame(rows)
            df.to_csv(output_path, index=False)

            self.logger.info(f"Saved {len(rows)} slice annotations to {output_path}")

        except Exception as e:
            self.logger.error(f"Failed to save slice annotations: {e}")


def convert_nodules_to_dicom_slices(
    nodules: list[dict],
    transformation_record: dict,
    output_dir: Path,
    logger: logging.Logger,
) -> None:
    """
    Convert all detected nodules to DICOM slice-level annotations.

    Args:
        nodules: List of detected nodules from model
        transformation_record: Transformation information from DICOM conversion
        output_dir: Directory to save slice-level results
        logger: Logger instance
    """
    converter = CoordinateConverter(logger)

    all_slice_results = []

    for nodule in nodules:
        slice_results = converter.convert_3d_bbox_to_dicom_slices(
            nodule, transformation_record
        )
        all_slice_results.extend(slice_results)

    # Save slice-level annotations
    timestamp = transformation_record.get("timestamp", "unknown")
    series_uid = transformation_record.get("series_instance_uid", "unknown")
    slice_output_file = output_dir / f"slice_annotations_{series_uid}_{timestamp}.csv"

    converter.save_slice_annotations(all_slice_results, slice_output_file)

    return all_slice_results
