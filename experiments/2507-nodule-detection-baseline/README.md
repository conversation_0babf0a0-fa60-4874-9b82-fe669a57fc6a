# CT Lung Nodule Detection Baseline - 2507

This experiment implements lung nodule detection using pre-trained MONAI RetinaNet models from the AI-Lung-Health-Benchmarking framework. The pipeline processes DICOM CT scans, performs preprocessing, runs inference, and outputs structured CSV results.

## Overview

The `ct_nodule_detection_duke.py` script provides a complete end-to-end pipeline for lung nodule detection, leveraging state-of-the-art models trained on the Duke Lung Cancer Screening Dataset (DLCS24) and benchmarked across multiple datasets (LUNA16, NLST).

### Based on Research

This implementation is based on:
- **Paper**: "AI in Lung Health: Benchmarking Detection and Diagnostic Models Across Multiple CT Scan Datasets" (<PERSON><PERSON><PERSON> et al., 2024)
- **Models**: MONAI RetinaNet with ResNet backbone
- **Framework**: ai-lung-health-benchmarking submodule

## Key Features

### 🔍 Complete DICOM Processing Pipeline
- **Automatic DICOM discovery**: Recursively finds .dcm files and groups by SeriesInstanceUID
- **DICOM to NIfTI conversion**: Handles rescale slope/intercept, spacing, and coordinate systems
- **Metadata extraction**: Patient ID, Study UID, Series UID, descriptions

### 🧠 State-of-the-Art Model Integration
- **MONAI RetinaNet**: 3D object detection with ResNet50 backbone
- **Pre-trained weights**: Models trained on 1613 CT volumes, 2487 expert-verified nodules
- **Sliding window inference**: Handles large volumes with configurable overlap
- **GPU acceleration**: CUDA support with automatic mixed precision

### 📊 Preprocessing Pipeline
- **Resampling**: Standardized 0.7×0.7×1.25mm resolution
- **Intensity normalization**: HU clipping [-1000, 500], mean=0, std=1
- **Orientation standardization**: RAS coordinate system
- **Patch extraction**: 192×192×80 training patches, 512×512×208 inference patches

### 📈 Structured Output
- **CSV format**: Timestamped results with comprehensive metadata
- **Coordinate system**: World coordinates for nodule centers and dimensions
- **Confidence scores**: Model prediction confidence (0-1 range)
- **Logging**: Detailed processing logs with error handling

## Installation & Setup

### Prerequisites

```bash
# Navigate to project root
cd /home/<USER>/projects/clarion/ct-lung-nodule

# Install dependencies (if not already installed)
poetry install

# Install MONAI with detection support
poetry add "monai[all]"

# Additional medical imaging dependencies
poetry add pydicom nibabel pandas torch torchvision
```

### Download Pre-trained Models

1. **Download from Zenodo**:
   ```bash
   # Create models directory
   mkdir -p models/

   # Download pre-trained weights (example)
   wget https://zenodo.org/records/14967976/files/duke_lung_model.pt -O models/duke_lung_model.pt
   ```

2. **Alternative baseline models**:
   - **Models Genesis**: [Genesis_Chest_CT.pt](https://drive.google.com/file/d/16iIIRkl6zYAfQ14i9NOakwFd6w_xKBSY/view?usp=sharing)
   - **Med3D**: [resnet_50_23dataset.pth](https://drive.google.com/file/d/1dIyJd3jpz9mBx534UA7deqT7f8N0sbJL/view?usp=sharing)

## Usage

### Basic Usage

```bash
# Navigate to the experiment directory
cd experiments/2507-nodule-detection-baseline/

# Run with default settings (creates sample directory structure)
poetry run python ct_nodule_detection_duke.py
```

### With Custom Parameters

```bash
# Full pipeline with model
poetry run python ct_nodule_detection_duke.py \
  --dicom-dir /path/to/dicom/files \
  --model-path /path/to/model.pt \
  --output-dir ./results \
  --keep-nifti
```

### Command Line Arguments

| Argument | Default | Description |
|----------|---------|-------------|
| `--dicom-dir` | `../../../lung_ct_scans` | Directory containing DICOM files |
| `--model-path` | None | Path to pre-trained model (.pt or .pth) |
| `--output-dir` | `.` | Output directory for results |
| `--keep-nifti` | False | Keep converted NIfTI files after processing |

## Input Data Structure

### DICOM Directory Structure

The script accepts flexible DICOM organization:

```
lung_ct_scans/
├── patient_001/
│   └── study_001/
│       └── series_001/
│           ├── slice_001.dcm
│           ├── slice_002.dcm
│           └── ...
├── patient_002/
│   └── study_001/
│       └── series_001/
│           └── ...
└── ...
```

**Or simply place all .dcm files anywhere in the directory** - the script automatically groups by SeriesInstanceUID.

### Supported DICOM Types

- **CT scans**: Low-dose or standard chest CT
- **Slice thickness**: Any (resampled to 1.25mm)
- **Pixel spacing**: Any (resampled to 0.7×0.7mm)
- **Series types**: Axial chest CT series

## Output Structure

### Directory Layout

```
experiments/2507-nodule-detection-baseline/
├── converted_nifti/              # Temporary NIfTI files (optional)
│   └── series_*.nii.gz
└── 2507-nodule-output/           # Results directory
    ├── nodule_detection_results_YYYYMMDD_HHMMSS.csv
    └── nodule_detection_YYYYMMDD_HHMMSS.log
```

### CSV Output Format

| Column | Description | Example |
|--------|-------------|---------|
| `patient_id` | DICOM Patient ID | "PATIENT_001" |
| `study_instance_uid` | DICOM Study Instance UID | "1.2.840.113..." |
| `series_instance_uid` | DICOM Series Instance UID | "1.2.840.113..." |
| `series_description` | Series description | "CHEST CT" |
| `scan_filename` | NIfTI filename | "series_xyz.nii.gz" |
| `nodule_id` | Sequential nodule ID per scan | 1, 2, 3... |
| `center_x` | Nodule center X coordinate (mm) | 45.2 |
| `center_y` | Nodule center Y coordinate (mm) | -123.8 |
| `center_z` | Nodule center Z coordinate (mm) | 67.5 |
| `width` | Bounding box width (mm) | 12.4 |
| `height` | Bounding box height (mm) | 15.2 |
| `depth` | Bounding box depth (mm) | 8.7 |
| `confidence_score` | Model confidence (0-1) | 0.847 |
| `label` | Class label (0=nodule) | 0 |

## Model Performance

### Benchmarking Results (from paper)

- **Training Data**: 1613 CT volumes, 2487 nodules (DLCS24)
- **Validation**: LUNA16, NLST, external datasets
- **Metrics**: FROC analysis, sensitivity at various FP rates
- **Architecture**: 3D RetinaNet with ResNet50 backbone

### Detection Parameters

- **Score threshold**: 0.02 (low threshold for high sensitivity)
- **NMS threshold**: 0.22 (non-maximum suppression)
- **Max detections**: 100 per scan
- **Patch size**: 192×192×80 (training), 512×512×208 (inference)

## Troubleshooting

### Common Issues

1. **No DICOM files found**
   - Check directory path and .dcm file extensions
   - Ensure DICOM files are readable (not corrupted)

2. **Model loading errors**
   - Verify model file path and format (.pt or .pth)
   - Check GPU memory availability
   - Try running on CPU by setting `CUDA_VISIBLE_DEVICES=""`

3. **Memory issues**
   - Reduce batch size in sliding window inference
   - Use `--keep-nifti` flag to debug intermediate files
   - Ensure sufficient disk space for temporary files

4. **Coordinate system issues**
   - Verify DICOM orientation and spacing metadata
   - Check that ImagePositionPatient is available
   - Consider coordinate system differences (RAS vs LPS)

### Debug Mode

```bash
# Enable verbose logging
PYTHONPATH=. poetry run python ct_nodule_detection_duke.py \
  --dicom-dir /path/to/dicoms \
  --model-path /path/to/model.pt \
  --keep-nifti \
  --output-dir ./debug_output
```

### Performance Optimization

```bash
# GPU optimization
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# CPU optimization
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8
```

## Integration with Clarion Framework

This experiment integrates with the broader Clarion lung nodule project:

- **Data paths**: Uses `clarion.utils` for consistent data management
- **Foundation models**: Compatible with `foundation-cancer-image-biomarker` submodule
- **Benchmarking**: Leverages `ai-lung-health-benchmarking` for evaluation

### Usage with Clarion

```python
from clarion.utils import DATA_DIR, PROJECT_DIR

# Use centralized data directory
dicom_path = DATA_DIR / "raw_dicoms"
results_path = DATA_DIR / "nodule_detection_results"
```

## Citation

When using this detection pipeline, please cite:

```bibtex
@article{tushar2024ai,
  title={AI in Lung Health: Benchmarking Detection and Diagnostic Models Across Multiple CT Scan Datasets},
  author={Tushar, Fakrul Islam and Wang, Avivah and Dahal, Lavsen and Harowicz, Michael R and Lafata, Kyle J and Tailor, Tina D and Lo, Joseph Y},
  journal={arXiv preprint arXiv:2405.04605},
  year={2024}
}
```

## Future Enhancements

- [ ] Multi-class detection (nodule types)
- [ ] Integration with classification models
- [ ] FROC analysis and evaluation metrics
- [ ] Batch processing optimization
- [ ] Docker containerization
- [ ] Web interface for visualization

## Support

For issues related to:
- **DICOM processing**: Check pydicom documentation
- **Model loading**: Refer to MONAI detection tutorials
- **Clarion integration**: See project CLAUDE.md files
- **Performance**: Monitor GPU/CPU usage and memory consumption