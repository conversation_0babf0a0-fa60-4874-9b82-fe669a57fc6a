#!/usr/bin/env python3
"""
Visualization script for lung nodule detection results.

This script takes the lung CT scans metadata and slice-level nodule predictions,
then creates visualizations grouped by series folders for debugging purposes.
"""

import argparse
import os
import sys
from pathlib import Path

import matplotlib

matplotlib.use("Agg")  # Use non-interactive backend
import matplotlib.patches as patches
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import pydicom

from clarion.utils import DATA_DIR


def load_dicom_image(dicom_path: str) -> tuple[np.ndarray, pydicom.Dataset]:
    """Load DICOM image and return pixel array and metadata."""
    try:
        ds = pydicom.dcmread(dicom_path)

        # Apply rescale slope and intercept if available
        pixel_array = ds.pixel_array.astype(np.float32)
        if hasattr(ds, "RescaleSlope") and hasattr(ds, "RescaleIntercept"):
            pixel_array = pixel_array * ds.RescaleSlope + ds.RescaleIntercept

        return pixel_array, ds
    except Exception as e:
        print(f"Error loading DICOM {dicom_path}: {e}")
        return None, None


def apply_window_level(
    pixel_array: np.ndarray, window_center: float, window_width: float
) -> np.ndarray:
    """Apply window/level adjustment to DICOM image."""
    min_val = window_center - window_width / 2
    max_val = window_center + window_width / 2

    # Clip values to window range and normalize to 0-255
    windowed = np.clip(pixel_array, min_val, max_val)
    windowed = ((windowed - min_val) / (max_val - min_val) * 255).astype(np.uint8)

    return windowed


def visualize_nodule_detections(
    ct_scans_df: pd.DataFrame,
    predictions_df: pd.DataFrame,
    output_dir: Path,
    max_series: int = 10,
    max_slices_per_series: int = 5,
) -> None:
    """
    Visualize nodule detections grouped by series.

    Args:
        ct_scans_df: DataFrame with CT scan metadata
        predictions_df: DataFrame with nodule predictions
        output_dir: Directory to save visualization images
        max_series: Maximum number of series to process
        max_slices_per_series: Maximum number of slices to visualize per series
    """

    # Group predictions by series_uid
    series_groups = predictions_df.groupby("series_uid")

    print(f"Found {len(series_groups)} series with nodule predictions")

    processed_series = 0

    for series_uid, series_predictions in series_groups:
        if processed_series >= max_series:
            break

        print(f"\nProcessing series: {series_uid}")

        # Get series metadata
        series_info = ct_scans_df[ct_scans_df["series_uid"] == series_uid]
        if series_info.empty:
            print(f"Warning: No metadata found for series {series_uid}")
            continue

        series_info = series_info.iloc[0]

        # Create output directory for this series
        series_output_dir = output_dir / series_uid
        series_output_dir.mkdir(parents=True, exist_ok=True)

        # Group predictions by slice/DICOM file
        slice_groups = series_predictions.groupby("dicom_file")

        print(f"  Found {len(slice_groups)} slices with predictions")

        processed_slices = 0

        for dicom_file, slice_predictions in slice_groups:
            if processed_slices >= max_slices_per_series:
                break

            # Load DICOM image
            pixel_array, _ = load_dicom_image(dicom_file)
            if pixel_array is None:
                continue

            # Apply lung windowing (typical values for lung CT)
            lung_windowed = apply_window_level(pixel_array, -500, 1700)

            # Create visualization
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

            # Original image with lung windowing
            ax1.imshow(lung_windowed, cmap="gray")
            ax1.set_title(f"Original Image\nSlice: {os.path.basename(dicom_file)}")
            ax1.axis("off")

            # Image with nodule detections
            ax2.imshow(lung_windowed, cmap="gray")
            ax2.set_title(f"Nodule Detections\n{len(slice_predictions)} detections")
            ax2.axis("off")

            # Draw bounding boxes for detected nodules
            for _, prediction in slice_predictions.iterrows():
                # Extract bounding box coordinates
                x_min = prediction["bbox_x_min"]
                y_min = prediction["bbox_y_min"]
                width = prediction["bbox_width"]
                height = prediction["bbox_height"]
                confidence = prediction["confidence_score"]

                # Convert coordinates from DICOM/numpy (top-left origin) to matplotlib (bottom-left origin)
                image_height = pixel_array.shape[0]
                y_min_matplotlib = image_height - y_min - height

                # Create rectangle patch
                rect = patches.Rectangle(
                    (x_min, y_min_matplotlib),
                    width,
                    height,
                    linewidth=2,
                    edgecolor="red" if confidence > 0.5 else "yellow",
                    facecolor="none",
                    alpha=0.8,
                )
                ax2.add_patch(rect)

                # Add confidence score text (also adjust y position)
                ax2.text(
                    x_min,
                    y_min_matplotlib - 5,
                    f"{confidence:.3f}",
                    color="white",
                    fontsize=8,
                    bbox=dict(
                        boxstyle="round,pad=0.2",
                        facecolor="red" if confidence > 0.5 else "yellow",
                        alpha=0.7,
                    ),
                )

            # Add series information
            info_text = f"""Series: {series_uid}
Description: {series_info.get('series_description', 'N/A')}
Slice Thickness: {series_info.get('slice_thickness', 'N/A')} mm
Pixel Spacing: {series_info.get('pixel_spacing', 'N/A')} mm
Instance: {prediction.get('instance_number', 'N/A')}
Slice Location: {prediction.get('slice_location', 'N/A')} mm"""

            fig.text(
                0.02,
                0.98,
                info_text,
                fontsize=8,
                verticalalignment="top",
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8),
            )

            plt.tight_layout()

            # Save visualization
            slice_name = os.path.basename(dicom_file).replace(".dcm", "")
            output_filename = f"slice_{slice_predictions.iloc[0]['instance_number']:03d}_{slice_name}.png"
            output_path = series_output_dir / output_filename

            plt.savefig(output_path, dpi=150, bbox_inches="tight")
            plt.close()

            print(f"    Saved: {output_filename}")
            processed_slices += 1

        # Create series summary plot
        create_series_summary(series_predictions, series_info, series_output_dir)

        processed_series += 1

    print(f"\nVisualization complete! Processed {processed_series} series.")
    print(f"Output saved to: {output_dir}")


def create_series_summary(
    series_predictions: pd.DataFrame, series_info: pd.Series, output_dir: Path
) -> None:
    """Create a summary plot for the series showing confidence distribution and slice coverage."""

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # Confidence score distribution
    ax1.hist(
        series_predictions["confidence_score"],
        bins=20,
        alpha=0.7,
        color="blue",
        edgecolor="black",
    )
    ax1.set_xlabel("Confidence Score")
    ax1.set_ylabel("Count")
    ax1.set_title("Confidence Score Distribution")
    ax1.axvline(0.5, color="red", linestyle="--", label="Threshold = 0.5")
    ax1.legend()

    # Predictions by slice location
    slice_counts = series_predictions.groupby("slice_location").size()
    ax2.plot(slice_counts.index, slice_counts.values, "o-", alpha=0.7)
    ax2.set_xlabel("Slice Location (mm)")
    ax2.set_ylabel("Number of Detections")
    ax2.set_title("Detections by Slice Location")
    ax2.grid(True, alpha=0.3)

    # Bounding box size distribution
    bbox_areas = series_predictions["bbox_width"] * series_predictions["bbox_height"]
    ax3.hist(bbox_areas, bins=20, alpha=0.7, color="green", edgecolor="black")
    ax3.set_xlabel("Bounding Box Area (pixels²)")
    ax3.set_ylabel("Count")
    ax3.set_title("Nodule Size Distribution")

    # High confidence detections by slice
    high_conf_df = series_predictions[series_predictions["confidence_score"] > 0.5]
    if not high_conf_df.empty:
        high_conf_counts = high_conf_df.groupby("slice_location").size()
        ax4.plot(high_conf_counts.index, high_conf_counts.values, "ro-", alpha=0.7)
        ax4.set_xlabel("Slice Location (mm)")
        ax4.set_ylabel("High Confidence Detections")
        ax4.set_title("High Confidence Detections (>0.5)")
        ax4.grid(True, alpha=0.3)
    else:
        ax4.text(
            0.5,
            0.5,
            "No high confidence\ndetections found",
            ha="center",
            va="center",
            transform=ax4.transAxes,
            fontsize=12,
        )
        ax4.set_title("High Confidence Detections (>0.5)")

    # Add series information
    info_text = f"""Series Summary: {series_info.get('series_uid', 'N/A')}
Description: {series_info.get('series_description', 'N/A')}
Total Detections: {len(series_predictions)}
High Confidence (>0.5): {len(series_predictions[series_predictions['confidence_score'] > 0.5])}
Unique Slices: {series_predictions['slice_location'].nunique()}
Confidence Range: {series_predictions['confidence_score'].min():.3f} - {series_predictions['confidence_score'].max():.3f}"""

    fig.suptitle(
        f"Series Analysis: {series_info.get('series_description', 'Unknown')}",
        fontsize=14,
        fontweight="bold",
    )
    fig.text(
        0.02,
        0.02,
        info_text,
        fontsize=10,
        verticalalignment="bottom",
        bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.8),
    )

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)

    # Save summary
    summary_path = output_dir / "series_summary.png"
    plt.savefig(summary_path, dpi=150, bbox_inches="tight")
    plt.close()

    print("    Saved series summary: series_summary.png")


def main():
    parser = argparse.ArgumentParser(
        description="Visualize lung nodule detection results"
    )
    parser.add_argument(
        "--ct_scans_csv",
        type=str,
        default=str(DATA_DIR / "raw" / "lung_ct_scans.csv"),
        help="Path to CT scans metadata CSV file",
    )
    parser.add_argument(
        "--predictions_csv",
        type=str,
        default="/mnt/data/clarion-data/experiments/2507-nodule-detection-baseline/run_detection_luna16_250720/2507-nodule-output/slice_level_annotations_20250802_222828.csv",
        help="Path to nodule predictions CSV file",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default=str(
            DATA_DIR
            / "experiments"
            / "2507-nodule-detection-baseline"
            / "run_detection_luna16_250720"
            / "viz"
        ),
        help="Output directory for visualization images",
    )
    parser.add_argument(
        "--max_series", type=int, default=10, help="Maximum number of series to process"
    )
    parser.add_argument(
        "--max_slices_per_series",
        type=int,
        default=5,
        help="Maximum number of slices to visualize per series",
    )

    args = parser.parse_args()

    # Verify input files exist
    if not os.path.exists(args.ct_scans_csv):
        print(f"Error: CT scans CSV file not found: {args.ct_scans_csv}")
        sys.exit(1)

    if not os.path.exists(args.predictions_csv):
        print(f"Error: Predictions CSV file not found: {args.predictions_csv}")
        sys.exit(1)

    # Load data
    print("Loading CT scans metadata...")
    ct_scans_df = pd.read_csv(args.ct_scans_csv)
    print(f"Loaded {len(ct_scans_df)} CT scan records")

    print("Loading nodule predictions...")
    predictions_df = pd.read_csv(args.predictions_csv)
    print(f"Loaded {len(predictions_df)} prediction records")

    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Run visualization
    visualize_nodule_detections(
        ct_scans_df=ct_scans_df,
        predictions_df=predictions_df,
        output_dir=output_dir,
        max_series=args.max_series,
        max_slices_per_series=args.max_slices_per_series,
    )


if __name__ == "__main__":
    main()
