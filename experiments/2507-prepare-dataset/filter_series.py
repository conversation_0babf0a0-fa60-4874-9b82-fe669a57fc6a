import logging

import numpy as np
import pandas as pd

from clarion.utils import DATA_DIR

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def is_transverse_plane(image_orientation):
    """
    Check if the series is in transverse (axial) plane.
    Transverse plane should have image orientation close to:
    [1,0,0,0,1,0] or [1,0,0,0,-1,0].
    """
    if not image_orientation or image_orientation == "" or pd.isna(image_orientation):
        return False

    try:
        # Parse the orientation string - format like:
        # "[1.000000, 0.000000, 0.000000, 0.000000, 1.000000, 0.000000]"
        orientation_str = str(image_orientation).strip("[]")
        if not orientation_str:
            return False

        # Split by comma and convert to float
        values = [float(x.strip()) for x in orientation_str.split(",")]

        if len(values) != 6:
            return False

        # For transverse plane, we expect:
        # - First 3 values (row direction): [1,0,0] or [-1,0,0]
        # - Last 3 values (column direction): [0,1,0] or [0,-1,0]
        # Allow some tolerance for floating point errors
        tolerance = 0.1

        row_dir = np.array(values[:3])
        col_dir = np.array(values[3:])

        # Check if row direction is along x-axis (±1,0,0)
        row_x_aligned = (
            abs(abs(row_dir[0]) - 1.0) < tolerance
            and abs(row_dir[1]) < tolerance
            and abs(row_dir[2]) < tolerance
        )

        # Check if column direction is along y-axis (0,±1,0)
        col_y_aligned = (
            abs(col_dir[0]) < tolerance
            and abs(abs(col_dir[1]) - 1.0) < tolerance
            and abs(col_dir[2]) < tolerance
        )

        return row_x_aligned and col_y_aligned

    except (ValueError, TypeError, IndexError):
        logger.debug(f"Could not parse image orientation: {image_orientation}")
        return False


def is_suitable_for_nodule_detection(row):
    """
    Determine if a series is suitable for lung nodule detection.

    Criteria:
    1. Must be CT modality
    2. Must be transverse (axial) plane
    3. Should be chest/thorax region (from series description)
    4. Prefer thinner slice thickness
    5. Should have reasonable number of slices
    6. Exclude scout/surview/localizer images
    7. Allow both normal dose and low-dose CT
    """

    # Must be CT
    if row["modality"] != "CT":
        return False, "Not CT modality"

    # Must be transverse plane
    if not is_transverse_plane(row["image_orientation"]):
        return False, "Not transverse plane"

    # Must have reasonable number of slices (at least 10 for 3D analysis)
    if pd.isna(row["dicom_count"]) or row["dicom_count"] < 10:
        return False, "Insufficient slices"

    # Check series description for chest/lung content
    series_desc = str(row["series_description"]).lower()

    # Exclude scout/surview/localizer images
    exclude_keywords = ["scout", "surview", "localizer", "screen save", "topogram"]
    if any(keyword in series_desc for keyword in exclude_keywords):
        return False, "Scout/localizer image"

    # Exclude pure coronal/sagittal reconstructions based on description
    exclude_planes = ["coronal", "sagittal", "cor ", "sag "]
    if any(plane in series_desc for plane in exclude_planes):
        return False, "Non-axial reconstruction"

    # Must be chest/thorax region
    chest_keywords = ["chest", "thorax", "lung", "pulmonary", "pul"]
    abdomen_keywords = ["abdomen", "abdo", "pelvis", "liver", "kidney"]

    has_chest = any(keyword in series_desc for keyword in chest_keywords)
    has_abdomen_only = (
        any(keyword in series_desc for keyword in abdomen_keywords) and not has_chest
    )

    if has_abdomen_only:
        return False, "Abdomen/pelvis only"

    # If no clear chest indication, check if it could be a whole-body or chest series
    # Accept if description doesn't clearly exclude chest
    if not has_chest and any(
        keyword in series_desc for keyword in ["head", "brain", "neck"]
    ):
        return False, "Head/neck only"

    return True, "Suitable"


def calculate_series_score(row):
    """
    Calculate a score for series ranking.
    Higher score = better for nodule detection.
    """
    score = 0
    series_desc = str(row["series_description"]).lower()

    # Base score for being suitable
    score += 100

    # Prefer lung/chest specific series
    if "lung" in series_desc:
        score += 50
    elif "chest" in series_desc or "thorax" in series_desc:
        score += 30

    # Prefer thin slice thickness
    if not pd.isna(row["slice_thickness"]):
        thickness = float(row["slice_thickness"])
        if thickness <= 1.25:
            score += 40  # Very thin slices
        elif thickness <= 2.5:
            score += 30  # Thin slices
        elif thickness <= 5.0:
            score += 20  # Moderate slices
        else:
            score += 10  # Thick slices

    # Prefer more slices (more coverage)
    if not pd.isna(row["dicom_count"]):
        slice_count = int(row["dicom_count"])
        if slice_count >= 200:
            score += 30
        elif slice_count >= 100:
            score += 20
        elif slice_count >= 50:
            score += 10

    # Prefer non-contrast or specify type
    if "contrast" not in series_desc:
        score += 10
    elif "pre" in series_desc and "contrast" in series_desc:
        score += 15  # Pre-contrast is often preferred

    # Allow both normal and low-dose
    if "low dose" in series_desc or "ldct" in series_desc:
        score += 5  # Slight preference for LDCT in screening

    return score


def filter_series_for_nodule_detection():
    """
    Filter CT series for lung nodule detection.
    Keep only the most suitable transverse series from each study.
    """

    # Load the manifest
    manifest_file = DATA_DIR / "raw" / "lung_ct_scans.csv"
    if not manifest_file.exists():
        raise FileNotFoundError(f"Manifest file not found: {manifest_file}")

    df = pd.read_csv(manifest_file)
    logger.info(f"Loaded manifest with {len(df)} series")

    # Remove existing useful_series column if it exists
    if "useful_series" in df.columns:
        df = df.drop("useful_series", axis=1)
        logger.info("Removed existing 'useful_series' column")

    # Add useful_series column (default empty)
    df["useful_series"] = ""

    # Group by accession_number (study level)
    studies_processed = 0
    studies_with_suitable_series = 0
    total_suitable_series = 0

    for accession_num, study_group in df.groupby("accession_number"):
        studies_processed += 1

        if pd.isna(accession_num) or accession_num == "":
            logger.warning(
                f"Study {studies_processed}: Missing accession number, skipping"
            )
            continue

        logger.info(
            f"\nProcessing study {studies_processed}: {accession_num} "
            f"({len(study_group)} series)"
        )

        # Filter suitable series within this study
        suitable_series = []

        for idx, row in study_group.iterrows():
            is_suitable, reason = is_suitable_for_nodule_detection(row)

            logger.debug(f"  Series {row['series_description']}: {reason}")

            if is_suitable:
                score = calculate_series_score(row)
                suitable_series.append((idx, row, score))
                total_suitable_series += 1

        # Select the best series from this study
        if suitable_series:
            # Sort by score (descending) and select the best one
            suitable_series.sort(key=lambda x: x[2], reverse=True)
            best_idx, best_row, best_score = suitable_series[0]

            # Mark as useful
            df.at[best_idx, "useful_series"] = 1
            studies_with_suitable_series += 1

            logger.info(
                f"  ✅ Selected: {best_row['series_description']} "
                f"(score: {best_score:.1f}, slices: {best_row['dicom_count']}, "
                f"thickness: {best_row['slice_thickness']}mm)"
            )
        else:
            logger.info("  ❌ No suitable series found")

    # Summary statistics
    selected_series = df[df["useful_series"] == 1]

    logger.info("\n=== Filtering Summary ===")
    logger.info(f"Total studies processed: {studies_processed}")
    logger.info(f"Studies with suitable series: {studies_with_suitable_series}")
    logger.info(f"Total suitable series found: {total_suitable_series}")
    logger.info(f"Series selected for nodule detection: {len(selected_series)}")
    logger.info(f"Selection rate: {len(selected_series)/studies_processed*100:.1f}%")

    # Save the updated manifest back to the original file
    df.to_csv(manifest_file, index=False)

    logger.info(f"\n💾 Updated manifest saved to: {manifest_file}")

    # Validation: Check that each group has at most 1 series selected
    logger.info("\n=== Validation Check ===")
    validation_groups = selected_series.groupby("accession_number").size()
    multiple_selections = validation_groups[validation_groups > 1]

    if len(multiple_selections) > 0:
        logger.error(
            f"❌ VALIDATION FAILED: Found {len(multiple_selections)} studies "
            f"with multiple selected series:"
        )
        for accession_num, count in multiple_selections.items():
            logger.error(f"  Study {accession_num}: {count} selected series")
        raise ValueError(
            "Validation failed: Some studies have multiple selected series"
        )
    else:
        logger.info("✅ VALIDATION PASSED: Each study has at most 1 selected series")
        logger.info(
            f"   - {len(validation_groups)} studies with exactly 1 selected series"
        )
        logger.info(
            f"   - {studies_processed - len(validation_groups)} studies "
            f"with 0 selected series"
        )

    # Show some statistics about selected series
    if len(selected_series) > 0:
        logger.info("\n=== Selected Series Statistics ===")

        # Slice thickness distribution
        thickness_stats = selected_series["slice_thickness"].describe()
        logger.info(
            f"Slice thickness (mm): min={thickness_stats['min']:.2f}, "
            f"mean={thickness_stats['mean']:.2f}, max={thickness_stats['max']:.2f}"
        )

        # Slice count distribution
        count_stats = selected_series["dicom_count"].describe()
        logger.info(
            f"Slice count: min={count_stats['min']:.0f}, "
            f"mean={count_stats['mean']:.0f}, max={count_stats['max']:.0f}"
        )

        # Series description distribution
        logger.info("\nSeries descriptions of selected series:")
        desc_counts = selected_series["series_description"].value_counts()
        for desc, count in desc_counts.head(10).items():
            logger.info(f"  {desc}: {count} studies")

    return manifest_file


if __name__ == "__main__":
    filter_series_for_nodule_detection()
