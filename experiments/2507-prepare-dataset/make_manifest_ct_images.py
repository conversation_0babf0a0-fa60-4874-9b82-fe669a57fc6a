import csv
import logging

import pydicom

from clarion.utils import DATA_DIR

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

ct_dir = DATA_DIR / "raw" / "lung_ct_scans"
assert ct_dir.exists(), f"Directory {ct_dir} does not exist"
manifest_file = DATA_DIR / "raw" / "lung_ct_scans.csv"


def extract_dicom_metadata(dicom_file_path):
    """Extract relevant metadata from a DICOM file."""
    try:
        # Skip pixel data for faster reading
        ds = pydicom.dcmread(dicom_file_path, force=True, stop_before_pixels=True)

        # Extract basic metadata
        metadata = {
            "patient_id": getattr(ds, "PatientID", ""),
            "study_date": getattr(ds, "StudyDate", ""),
            "study_time": getattr(ds, "StudyTime", ""),
            "study_uid": getattr(ds, "StudyInstanceUID", ""),
            "series_uid": getattr(ds, "SeriesInstanceUID", ""),
            "series_description": getattr(ds, "SeriesDescription", ""),
            "accession_number": getattr(ds, "AccessionNumber", ""),
            "slice_thickness": getattr(ds, "SliceThickness", ""),
            "pixel_spacing": str(getattr(ds, "PixelSpacing", "")),
            "rows": getattr(ds, "Rows", ""),
            "columns": getattr(ds, "Columns", ""),
            "modality": getattr(ds, "Modality", ""),
            "manufacturer": getattr(ds, "Manufacturer", ""),
            "scanner_model": getattr(ds, "ManufacturerModelName", ""),
            "kvp": getattr(ds, "KVP", ""),
            "exposure_time": getattr(ds, "ExposureTime", ""),
            "tube_current": getattr(ds, "XRayTubeCurrent", ""),
            "slice_location": getattr(ds, "SliceLocation", ""),
            "image_position": str(getattr(ds, "ImagePositionPatient", "")),
            "image_orientation": str(getattr(ds, "ImageOrientationPatient", "")),
            "window_center": str(getattr(ds, "WindowCenter", "")),
            "window_width": str(getattr(ds, "WindowWidth", "")),
            "rescale_intercept": getattr(ds, "RescaleIntercept", ""),
            "rescale_slope": getattr(ds, "RescaleSlope", ""),
        }

        return metadata
    except Exception as e:
        logger.warning(f"Error reading DICOM file {dicom_file_path}: {e}")
        return None


def create_ct_manifest():
    """Create manifest CSV file with CT scan metadata."""

    fieldnames = [
        "study_folder",
        "series_folder",
        "dicom_count",
        "patient_id",
        "study_date",
        "study_time",
        "study_uid",
        "series_uid",
        "series_description",
        "accession_number",
        "slice_thickness",
        "pixel_spacing",
        "rows",
        "columns",
        "modality",
        "manufacturer",
        "scanner_model",
        "kvp",
        "exposure_time",
        "tube_current",
        "slice_location_min",
        "slice_location_max",
        "image_position",
        "image_orientation",
        "window_center",
        "window_width",
        "rescale_intercept",
        "rescale_slope",
    ]

    with open(manifest_file, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        study_count = 0
        series_count = 0

        # Iterate through study directories
        for study_dir in sorted(ct_dir.iterdir()):
            if not study_dir.is_dir():
                continue

            study_count += 1
            logger.info(f"Processing study {study_count}: {study_dir.name}")

            # Iterate through series directories within each study
            for series_dir in sorted(study_dir.iterdir()):
                if not series_dir.is_dir():
                    continue

                # Find DICOM files in this series
                dicom_files = list(series_dir.glob("*.dcm"))

                if len(dicom_files) == 0:
                    logger.info(f"No DICOM files found in {series_dir}")
                    continue

                series_count += 1
                logger.info(
                    f"  Processing series {series_count}: {series_dir.name} "
                    f"({len(dicom_files)} files)"
                )

                # Extract metadata from first DICOM file as representative
                first_metadata = extract_dicom_metadata(dicom_files[0])
                if first_metadata is None:
                    continue

                # Calculate slice location range from a sample of files for efficiency
                sample_size = min(10, len(dicom_files))  # Sample up to 10 files
                sample_files = dicom_files[:: max(1, len(dicom_files) // sample_size)]

                slice_locations = []
                for dcm_file in sample_files:
                    try:
                        ds = pydicom.dcmread(
                            dcm_file, force=True, stop_before_pixels=True
                        )
                        if hasattr(ds, "SliceLocation") and ds.SliceLocation:
                            slice_locations.append(float(ds.SliceLocation))
                    except (Exception, ValueError, TypeError):
                        pass

                slice_loc_min = min(slice_locations) if slice_locations else ""
                slice_loc_max = max(slice_locations) if slice_locations else ""

                # Create row for CSV - exclude slice_location from first_metadata
                # as we have min/max
                row_metadata = {
                    k: v for k, v in first_metadata.items() if k != "slice_location"
                }
                row = {
                    "study_folder": study_dir.name,
                    "series_folder": series_dir.name,
                    "dicom_count": len(dicom_files),
                    "slice_location_min": slice_loc_min,
                    "slice_location_max": slice_loc_max,
                    **row_metadata,
                }

                writer.writerow(row)

        logger.info(f"Processed {study_count} studies and {series_count} series")
        logger.info(f"Manifest saved to: {manifest_file}")


if __name__ == "__main__":
    create_ct_manifest()
