import csv
import io
import shutil

import msoffcrypto
import pandas as pd

from clarion.utils import DATA_DIR


def convert_to_csv():
    """Convert password-protected Excel file with two-row headers to CSV."""
    manifest = (
        DATA_DIR / "raw" / "Scan reports_DE-IDENTIFIED (IHPC)_8July2025 LATEST.xlsx"
    )
    password = "IHPC"

    # Check if file exists
    if not manifest.exists():
        print(f"Error: Excel file not found at {manifest}")
        return

    try:
        # Try to read with pandas first (in case file is not password protected)
        try:
            df_temp = pd.read_excel(manifest, engine="openpyxl", header=[0, 1])
            # If successful, extract the data directly
            header_row1 = df_temp.columns.get_level_values(0).tolist()
            header_row2 = df_temp.columns.get_level_values(1).tolist()
            df = df_temp.copy()
            df.columns = range(len(df.columns))  # Reset to numeric columns temporarily

        except Exception:
            # If that fails, try opening as password-protected file
            print(
                "File appears to be password protected, attempting with provided "
                "password..."
            )

            # Use msoffcrypto to decrypt the file
            with open(manifest, "rb") as f:
                office_file = msoffcrypto.OfficeFile(f)
                office_file.load_key(password=password)

                # Create a BytesIO buffer to hold decrypted data
                decrypted = io.BytesIO()
                office_file.decrypt(decrypted)
                decrypted.seek(0)

                # Read the decrypted Excel file
                df_temp = pd.read_excel(decrypted, engine="openpyxl", header=[0, 1])
                header_row1 = df_temp.columns.get_level_values(0).tolist()
                header_row2 = df_temp.columns.get_level_values(1).tolist()
                df = df_temp.copy()
                df.columns = range(
                    len(df.columns)
                )  # Reset to numeric columns temporarily

        # Create column names by combining the two header rows
        new_columns = []
        for i in range(len(header_row1)):
            # Get values from both header rows
            h1 = (
                header_row1[i]
                if i < len(header_row1) and header_row1[i] is not None
                else ""
            )
            h2 = (
                header_row2[i]
                if i < len(header_row2) and header_row2[i] is not None
                else ""
            )

            # Convert to strings and strip whitespace
            h1_str = str(h1).strip()
            h2_str = str(h2).strip()

            # Clean up "Unnamed" patterns
            if "Unnamed:" in h1_str:
                h1_str = ""
            if "Unnamed:" in h2_str or "level_" in h2_str:
                h2_str = ""

            # Combine headers
            if h1_str and h2_str and h1_str != h2_str:
                # Both headers have content and are different
                column_name = f"{h1_str}_{h2_str}"
            elif h1_str:
                # Only first header has content
                column_name = h1_str
            elif h2_str:
                # Only second header has content
                column_name = h2_str
            else:
                # Neither header has content
                column_name = f"Column_{i+1}"

            new_columns.append(column_name)

        # Assign column names to DataFrame
        df.columns = new_columns[
            : len(df.columns)
        ]  # Ensure we don't exceed DataFrame columns

        # Clean up column names (remove extra spaces, special chars)
        df.columns = [
            col.replace(" ", "_").replace("(", "").replace(")", "").replace("-", "_")
            for col in df.columns
        ]

        # Define output CSV path
        output_csv = DATA_DIR / "processed" / "scan_reports_manifest.csv"
        output_csv.parent.mkdir(parents=True, exist_ok=True)

        # Save to CSV
        df.to_csv(output_csv, index=False)

        print("Successfully converted Excel to CSV:")
        print(f"  Input: {manifest}")
        print(f"  Output: {output_csv}")
        print(f"  Shape: {df.shape}")
        print(f"  Columns: {list(df.columns)}")

        return output_csv

    except Exception as e:
        print(f"Error processing Excel file: {e}")
        return None


def analyze_scan_types():
    """Analyze the scan type column for unique values and patterns."""
    manifest = DATA_DIR / "processed" / "scan_reports_manifest.csv"

    if not manifest.exists():
        print(f"Error: Manifest CSV not found at {manifest}")
        return

    df = pd.read_csv(manifest)
    scan_type_col = "Scan_type"

    if scan_type_col not in df.columns:
        print(f"Error: Column '{scan_type_col}' not found")
        return

    print("=== Scan Type Analysis ===")
    print(f"Total rows: {len(df)}")

    # Get unique scan type values
    unique_scan_types = df[scan_type_col].unique()
    print(f"Unique scan type values: {len(unique_scan_types)}")
    print()

    # Show all unique scan types with counts
    print("All unique scan types with counts:")
    scan_type_counts = df[scan_type_col].value_counts()
    for i, (scan_type, count) in enumerate(scan_type_counts.items(), 1):
        percentage = (count / len(df)) * 100
        print(f"  {i:2d}. '{scan_type}': {count} scans ({percentage:.1f}%)")

    print()

    # Categorize scan types
    print("Scan type categories:")
    categories = {
        "Basic CT Chest": [],
        "Extended CT (multi-region)": [],
        "Low-dose CT (LDCT)": [],
        "High-resolution CT": [],
        "PET/CT": [],
        "Other/Specialized": [],
    }

    for scan_type in unique_scan_types:
        scan_str = str(scan_type).lower()

        if "ldct" in scan_str or "low dose" in scan_str:
            categories["Low-dose CT (LDCT)"].append(scan_type)
        elif "pet" in scan_str:
            categories["PET/CT"].append(scan_type)
        elif "high resolution" in scan_str:
            categories["High-resolution CT"].append(scan_type)
        elif (
            "chest, abdomen and pelvis" in scan_str
            or "chest and abdomen" in scan_str
            or "brain, chest and abdomen" in scan_str
            or "pns, chest and abdomen" in scan_str
        ):
            categories["Extended CT (multi-region)"].append(scan_type)
        elif (
            "ct chest" in scan_str
            and "abdomen" not in scan_str
            and "pelvis" not in scan_str
        ):
            categories["Basic CT Chest"].append(scan_type)
        else:
            categories["Other/Specialized"].append(scan_type)

    for category, scan_types in categories.items():
        if scan_types:
            total_count = sum(scan_type_counts[st] for st in scan_types)
            print(f"\n  {category} ({total_count} total scans):")
            for scan_type in scan_types:
                count = scan_type_counts[scan_type]
                print(f"    - '{scan_type}': {count} scans")

    print()

    # Check for potential data quality issues
    print("Potential data quality issues:")
    issues_found = []

    for scan_type in unique_scan_types:
        scan_str = str(scan_type)

        # Check for leading/trailing spaces
        if scan_str != scan_str.strip():
            issues_found.append(f"Leading/trailing spaces: '{scan_type}'")

        # Check for unusual characters or typos
        if scan_str.startswith(" ") or scan_str.endswith(" "):
            issues_found.append(f"Extra whitespace: '{scan_type}'")

    if issues_found:
        for issue in issues_found:
            print(f"  - {issue}")
    else:
        print("  No obvious formatting issues detected")


def analyze_manifest():
    """Analyze the manifest CSV for data quality and patient uniqueness."""
    manifest = DATA_DIR / "processed" / "scan_reports_manifest.csv"

    if not manifest.exists():
        print(f"Error: Manifest CSV not found at {manifest}")
        print("Please run convert_to_csv() first.")
        return

    df = pd.read_csv(manifest)
    print("=== Manifest Data Analysis ===")
    print(f"Total rows: {len(df)}")
    print(f"Total columns: {len(df.columns)}")
    print()

    # Check if patient ID is unique for each row
    patient_col = "Patient_S/N"
    if patient_col in df.columns:
        total_rows = len(df)
        unique_patients = df[patient_col].nunique()
        duplicate_count = total_rows - unique_patients

        print("=== Patient ID Uniqueness Analysis ===")
        print(f"Total rows: {total_rows}")
        print(f"Unique patients: {unique_patients}")
        print(f"Duplicate patient entries: {duplicate_count}")

        if duplicate_count == 0:
            print("✅ Each row has a unique patient ID")
        else:
            print(f"❌ Found {duplicate_count} duplicate patient entries")
            print("\nAll patients with multiple scans:")
            duplicates = (
                df[patient_col]
                .value_counts()[df[patient_col].value_counts() > 1]
                .sort_index()
            )
            for patient, count in duplicates.items():
                print(f"  Patient {patient}: {count} scans")
        print()

        # Additional analysis
        print("=== Data Overview ===")
        print(
            f"Date range: {df['Scan_date_DDMMMYY'].min()} to "
            f"{df['Scan_date_DDMMMYY'].max()}"
        )

        if "Cohort" in df.columns:
            print("\nCohort distribution:")
            cohort_counts = df["Cohort"].value_counts()
            for cohort, count in cohort_counts.items():
                print(f"  {cohort}: {count} scans")

        if "Scan_type" in df.columns:
            print("\nScan types:")
            scan_types = df["Scan_type"].value_counts()
            for scan_type, count in scan_types.items():
                print(f"  {scan_type}: {count} scans")

        # Check for missing values in key columns
        print("\n=== Missing Data Analysis ===")
        key_columns = [
            patient_col,
            "Scan_date_DDMMMYY",
            "Set_A_Scan_de_identified_ID_1",
            "Set_B_Scan_de_identified_ID_1",
        ]
        for col in key_columns:
            if col in df.columns:
                missing = df[col].isna().sum()
                if missing > 0:
                    print(
                        f"❌ {col}: {missing} missing values "
                        f"({missing/len(df)*100:.1f}%)"
                    )
                else:
                    print(f"✅ {col}: No missing values")
    else:
        print(f"Error: Column '{patient_col}' not found in the data")
        print(f"Available columns: {list(df.columns)}")


def back_up_versions():
    """Create a CSV mapping folder names to their source directories."""
    scan_dir = DATA_DIR / "raw" / "RAW_FROM_NCCS"

    # Define source directories
    gid_new_dir = scan_dir / "GID NEW"
    new_gid3_dir = scan_dir / "New GID3"

    # Create output directory and file path
    output_dir = DATA_DIR / "raw"
    output_dir.mkdir(parents=True, exist_ok=True)
    output_csv = output_dir / "folder_mapping.csv"

    # Collect folder mappings
    folder_mappings = []

    # Check GID NEW directory
    if gid_new_dir.exists() and gid_new_dir.is_dir():
        print(f"Scanning {gid_new_dir}...")
        for folder in gid_new_dir.iterdir():
            if folder.is_dir():
                folder_mappings.append(
                    {"folder_name": folder.name, "source_directory": "GID_NEW"}
                )
    else:
        print(f"Warning: Directory {gid_new_dir} not found")

    # Check NEW GID3 directory
    if new_gid3_dir.exists() and new_gid3_dir.is_dir():
        print(f"Scanning {new_gid3_dir}...")
        for folder in new_gid3_dir.iterdir():
            if folder.is_dir():
                folder_mappings.append(
                    {"folder_name": folder.name, "source_directory": "NEW_GID3"}
                )
    else:
        print(f"Warning: Directory {new_gid3_dir} not found")

    # Write to CSV
    if folder_mappings:
        with open(output_csv, "w", newline="", encoding="utf-8") as csvfile:
            fieldnames = ["folder_name", "source_directory"]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for mapping in folder_mappings:
                writer.writerow(mapping)

        print(f"\n✅ Successfully created folder mapping CSV: {output_csv}")
        print(f"   Total folders mapped: {len(folder_mappings)}")

        # Show summary statistics
        gid_new_count = sum(
            1 for m in folder_mappings if m["source_directory"] == "GID_NEW"
        )
        new_gid3_count = sum(
            1 for m in folder_mappings if m["source_directory"] == "NEW_GID3"
        )

        print(f"   - GID_NEW: {gid_new_count} folders")
        print(f"   - NEW_GID3: {new_gid3_count} folders")

        # Show first few examples
        if len(folder_mappings) > 0:
            print("\n📋 Sample mappings:")
            for mapping in folder_mappings[:5]:
                print(f"   {mapping['folder_name']} -> {mapping['source_directory']}")
            if len(folder_mappings) > 5:
                print(f"   ... and {len(folder_mappings) - 5} more")

    else:
        print("❌ No folders found to map")


def merge_folders():
    """Move folders from GID NEW and New GID3 directories into a single merged
    directory."""
    scan_dir = DATA_DIR / "raw" / "RAW_FROM_NCCS"

    # Define source directories
    gid_new_dir = scan_dir / "GID NEW"
    new_gid3_dir = scan_dir / "New GID3"

    # Define target directory
    target_dir = DATA_DIR / "raw" / "lung_ct_scans"
    target_dir.mkdir(parents=True, exist_ok=True)

    # Track moved folders
    moved_count = 0
    skipped_count = 0
    error_count = 0

    print("🔄 Starting folder merge...")
    print(f"Target directory: {target_dir}")
    print()

    # Function to move folders from a source directory
    def move_folders_from_source(source_dir, source_name):
        nonlocal moved_count, skipped_count, error_count

        if not source_dir.exists() or not source_dir.is_dir():
            print(f"⚠️  Warning: {source_name} directory not found at {source_dir}")
            return

        print(f"📁 Processing {source_name}: {source_dir}")

        folders = [f for f in source_dir.iterdir() if f.is_dir()]
        print(f"   Found {len(folders)} folders to move")

        for folder in folders:
            target_path = target_dir / folder.name

            try:
                if target_path.exists():
                    print(f"   ⏭️  Skipping {folder.name} (already exists in target)")
                    skipped_count += 1
                else:
                    shutil.move(str(folder), str(target_path))
                    print(f"   ✅ Moved {folder.name}")
                    moved_count += 1
            except Exception as e:
                print(f"   ❌ Error moving {folder.name}: {e}")
                error_count += 1

    # Move folders from both source directories
    move_folders_from_source(gid_new_dir, "GID NEW")
    move_folders_from_source(new_gid3_dir, "New GID3")

    # Summary
    print()
    print("📊 Merge Summary:")
    print(f"   ✅ Successfully moved: {moved_count} folders")
    print(f"   ⏭️  Skipped (already exist): {skipped_count} folders")
    print(f"   ❌ Errors: {error_count} folders")
    print(
        f"   🎯 Total folders in target: "
        f"{len([f for f in target_dir.iterdir() if f.is_dir()])}"
    )

    if moved_count > 0:
        print("\n🎉 Folder merge completed successfully!")
        print(f"   All folders are now in: {target_dir}")
        print("   Use the CSV mapping file to trace folder origins")
    elif skipped_count > 0 and error_count == 0:
        print("\n✅ All folders were already in the target directory")
    else:
        print("\n⚠️  Merge completed with issues. Check error messages above.")


def match_scans():
    """Match manifest rows to scan folders and update CSV with scan_path column."""
    scan_dir = DATA_DIR / "raw" / "lung_ct_scans"
    manifest_file = DATA_DIR / "processed" / "scan_reports_manifest.csv"
    df = pd.read_csv(manifest_file)

    print("=== Scan Matching and CSV Update ===")
    print(f"Manifest rows: {len(df)}")

    # Get all folder names
    if not scan_dir.exists():
        print(f"Error: Scan directory not found at {scan_dir}")
        return

    folder_names = {f.name for f in scan_dir.iterdir() if f.is_dir()}
    print(f"Scan folders: {len(folder_names)}")
    print()

    # Extract accession numbers from folder names (remove date prefix)
    folder_to_accession = {}
    for folder_name in folder_names:
        if "_" in folder_name:
            # Split by underscore and take the part after the date
            parts = folder_name.split("_", 1)
            if len(parts) == 2:
                accession_number = parts[1]
                folder_to_accession[accession_number] = folder_name

    print(f"Extracted {len(folder_to_accession)} accession numbers from folder names")

    # Initialize scan_path column
    df["scan_path"] = ""

    matches = 0
    no_matches = 0

    print("Matching rows to scan folders...")

    for idx, row in df.iterrows():
        # Choose ID: Set B ID 2 if available, otherwise Set A ID 2
        set_b_id = row["Set_B_Scan_de_identified_ID_2"]
        set_a_id = row["Set_A_Scan_de_identified_ID_2"]

        scan_id = None

        if pd.notna(set_b_id) and str(set_b_id).strip():
            scan_id = str(set_b_id).strip()
        elif pd.notna(set_a_id) and str(set_a_id).strip():
            scan_id = str(set_a_id).strip()
        else:
            no_matches += 1
            continue

        # Check if this scan_id matches any accession number
        if scan_id in folder_to_accession:
            folder_name = folder_to_accession[scan_id]
            df.at[idx, "scan_path"] = folder_name
            matches += 1
        else:
            no_matches += 1

    print("\n=== Matching Results ===")
    print(f"✅ Successful matches: {matches}")
    print(f"❌ No matches found: {no_matches}")
    print(f"📊 Match rate: {matches/(matches+no_matches)*100:.1f}%")

    # Save updated CSV
    output_file = DATA_DIR / "processed" / "scan_reports_manifest_with_paths.csv"
    df.to_csv(output_file, index=False)
    print(f"\n💾 Updated CSV saved to: {output_file}")
    print(f"   Added 'scan_path' column with {matches} populated paths")

    return {
        "total_manifest_rows": len(df),
        "total_folders": len(folder_names),
        "matches": matches,
        "no_matches": no_matches,
        "match_rate": (
            matches / (matches + no_matches) * 100 if matches + no_matches > 0 else 0
        ),
        "output_file": str(output_file),
    }


if __name__ == "__main__":
    # convert_to_csv()  # Comment out to avoid re-running conversion
    # analyze_manifest()
    # analyze_scan_types()
    # back_up_versions()  # Already run - mapping CSV created
    # merge_folders()
    # match_scans()
    pass
