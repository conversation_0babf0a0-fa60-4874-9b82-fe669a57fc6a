repos:
  # Ruff linter and formatter for Python files
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.8.0
    hooks:
      # Run the linter
      - id: ruff
        args: [--fix]
        exclude: ^(ai-lung-health-benchmarking/|foundation-cancer-image-biomarker/)
      # Run the formatter
      - id: ruff-format
        exclude: ^(ai-lung-health-benchmarking/|foundation-cancer-image-biomarker/)
  
  # nbQA for Jupyter notebooks with ruff
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.7.1
    hooks:
      - id: nbqa-ruff
        args: [--fix]
        exclude: ^(ai-lung-health-benchmarking/|foundation-cancer-image-biomarker/)