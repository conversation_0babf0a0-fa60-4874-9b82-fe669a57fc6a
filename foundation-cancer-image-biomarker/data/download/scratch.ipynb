{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"nsclc_radiogenomics.csv\")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["df[\"command\"] = df.apply(\n", "    lambda row: f\"cp {row['gcs_url'].replace('gs://', 's3://')} ./{row['SeriesInstanceUID']}/{row['SOPInstanceUID']}.dcm\",\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["0         cp gs://public-datasets-idc/a808ce99-b6ad-4a3f...\n", "1         cp gs://public-datasets-idc/6fe34efa-3fba-482d...\n", "2         cp gs://public-datasets-idc/21428f46-8a8c-4fa8...\n", "3         cp gs://public-datasets-idc/88fa20d5-6831-4aae...\n", "4         cp gs://public-datasets-idc/643172aa-c303-4c04...\n", "                                ...                        \n", "171965    cp gs://public-datasets-idc/d546f0f0-929b-492a...\n", "171966    cp gs://public-datasets-idc/265755ae-a54e-42a5...\n", "171967    cp gs://public-datasets-idc/d546f0f0-929b-492a...\n", "171968    cp gs://public-datasets-idc/d546f0f0-929b-492a...\n", "171969    cp gs://public-datasets-idc/265755ae-a54e-42a5...\n", "Name: command, Length: 171970, dtype: object"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"command\"]"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["df.to_csv(\"nsclc_radiogenomics.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "fmcib_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}