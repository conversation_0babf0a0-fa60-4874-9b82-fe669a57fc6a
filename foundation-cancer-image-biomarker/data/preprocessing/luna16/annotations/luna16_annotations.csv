,calcification,internalStructure,lobulation,malignancy,margin,sphericity,spiculation,subtlety,texture,bbox,centroid,scan
0,6,1,4,3,4,4,4,2,5,"(slice(359, 385, None), slice(158, 177, None), slice(36, 45, None))","[372, 167, 40]",1.3.6.1.4.1.14519.5.2.1.6279.6001.131939324905446238286154504249
1,6,1,4,4,4,4,4,5,5,"(slice(359, 388, None), slice(96, 139, None), slice(79, 90, None))","[373, 117, 84]",1.3.6.1.4.1.14519.5.2.1.6279.6001.131939324905446238286154504249
2,6,1,3,5,3,4,4,5,5,"(slice(340, 392, None), slice(297, 341, None), slice(86, 95, None))","[366, 319, 90]",1.3.6.1.4.1.14519.5.2.1.6279.6001.179049373636438705059720603192
3,6,1,2,4,3,4,3,5,4,"(slice(333, 373, None), slice(344, 389, None), slice(70, 80, None))","[353, 366, 75]",1.3.6.1.4.1.14519.5.2.1.6279.6001.170706757615202213033480003264
4,6,1,2,3,4,4,2,4,5,"(slice(192, 205, None), slice(299, 318, None), slice(81, 85, None))","[198, 308, 83]",1.3.6.1.4.1.14519.5.2.1.6279.6001.170706757615202213033480003264
5,6,1,1,4,5,4,1,4,5,"(slice(209, 229, None), slice(212, 231, None), slice(81, 89, None))","[219, 221, 85]",1.3.6.1.4.1.14519.5.2.1.6279.6001.170706757615202213033480003264
6,4,1,1,1,5,4,1,3,5,"(slice(318, 329, None), slice(136, 147, None), slice(74, 79, None))","[323, 141, 76]",1.3.6.1.4.1.14519.5.2.1.6279.6001.323541312620128092852212458228
7,6,1,1,3,4,5,1,2,5,"(slice(393, 403, None), slice(122, 132, None), slice(77, 79, None))","[398, 127, 78]",1.3.6.1.4.1.14519.5.2.1.6279.6001.129007566048223160327836686225
8,6,1,1,3,4,4,1,4,5,"(slice(401, 415, None), slice(306, 320, None), slice(79, 82, None))","[408, 313, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.129007566048223160327836686225
9,6,1,2,3,4,4,2,3,4,"(slice(350, 365, None), slice(363, 380, None), slice(83, 86, None))","[357, 371, 84]",1.3.6.1.4.1.14519.5.2.1.6279.6001.132817748896065918417924920957
10,6,1,3,5,3,4,5,5,5,"(slice(263, 316, None), slice(173, 218, None), slice(101, 114, None))","[289, 195, 107]",1.3.6.1.4.1.14519.5.2.1.6279.6001.272348349298439120568330857680
11,6,1,1,3,3,4,1,2,3,"(slice(285, 295, None), slice(112, 129, None), slice(70, 72, None))","[290, 120, 71]",1.3.6.1.4.1.14519.5.2.1.6279.6001.774060103415303828812229821954
12,6,1,2,3,3,3,1,3,4,"(slice(265, 276, None), slice(363, 375, None), slice(87, 90, None))","[270, 369, 88]",1.3.6.1.4.1.14519.5.2.1.6279.6001.774060103415303828812229821954
13,6,1,2,3,4,3,2,4,5,"(slice(312, 328, None), slice(295, 306, None), slice(207, 213, None))","[320, 300, 210]",1.3.6.1.4.1.14519.5.2.1.6279.6001.416701701108520592702405866796
14,6,1,2,3,4,4,1,4,5,"(slice(324, 338, None), slice(105, 117, None), slice(27, 32, None))","[331, 111, 29]",1.3.6.1.4.1.14519.5.2.1.6279.6001.140253591510022414496468423138
15,4,1,1,1,5,5,1,4,5,"(slice(184, 194, None), slice(396, 405, None), slice(58, 60, None))","[189, 400, 59]",1.3.6.1.4.1.14519.5.2.1.6279.6001.140253591510022414496468423138
16,6,1,2,4,4,3,4,4,5,"(slice(309, 327, None), slice(143, 171, None), slice(102, 108, None))","[318, 157, 105]",1.3.6.1.4.1.14519.5.2.1.6279.6001.140253591510022414496468423138
17,6,1,1,3,5,5,1,3,5,"(slice(201, 210, None), slice(131, 140, None), slice(43, 47, None))","[205, 135, 45]",1.3.6.1.4.1.14519.5.2.1.6279.6001.328789598898469177563438457842
18,6,1,1,2,4,4,1,3,4,"(slice(211, 221, None), slice(385, 395, None), slice(52, 55, None))","[216, 390, 53]",1.3.6.1.4.1.14519.5.2.1.6279.6001.328789598898469177563438457842
19,6,1,1,3,4,5,1,3,5,"(slice(363, 371, None), slice(160, 170, None), slice(56, 59, None))","[367, 165, 57]",1.3.6.1.4.1.14519.5.2.1.6279.6001.328789598898469177563438457842
20,6,1,1,4,5,5,1,4,5,"(slice(287, 307, None), slice(378, 396, None), slice(62, 67, None))","[297, 387, 64]",1.3.6.1.4.1.14519.5.2.1.6279.6001.328789598898469177563438457842
21,6,1,1,3,4,4,1,4,5,"(slice(210, 222, None), slice(360, 371, None), slice(65, 69, None))","[216, 365, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.328789598898469177563438457842
22,6,1,1,3,4,4,1,4,5,"(slice(195, 205, None), slice(308, 320, None), slice(69, 73, None))","[200, 314, 71]",1.3.6.1.4.1.14519.5.2.1.6279.6001.328789598898469177563438457842
23,6,1,1,2,5,4,1,4,5,"(slice(183, 195, None), slice(354, 369, None), slice(76, 80, None))","[189, 361, 78]",1.3.6.1.4.1.14519.5.2.1.6279.6001.328789598898469177563438457842
24,6,1,1,3,4,5,1,3,4,"(slice(190, 200, None), slice(330, 341, None), slice(78, 82, None))","[195, 335, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.328789598898469177563438457842
25,6,1,1,3,4,4,1,4,4,"(slice(166, 175, None), slice(321, 331, None), slice(78, 81, None))","[170, 326, 79]",1.3.6.1.4.1.14519.5.2.1.6279.6001.328789598898469177563438457842
26,6,1,2,3,4,4,1,4,5,"(slice(174, 191, None), slice(168, 188, None), slice(82, 87, None))","[182, 178, 84]",1.3.6.1.4.1.14519.5.2.1.6279.6001.174907798609768549012640380786
27,6,1,3,5,4,3,3,5,5,"(slice(130, 159, None), slice(172, 217, None), slice(86, 96, None))","[144, 194, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.174907798609768549012640380786
28,6,1,1,4,4,4,1,4,5,"(slice(265, 290, None), slice(345, 372, None), slice(71, 78, None))","[277, 358, 74]",1.3.6.1.4.1.14519.5.2.1.6279.6001.747803439040091794717626507402
29,6,1,2,4,4,4,4,5,4,"(slice(236, 273, None), slice(294, 334, None), slice(168, 187, None))","[254, 314, 177]",1.3.6.1.4.1.14519.5.2.1.6279.6001.227962600322799211676960828223
30,6,1,3,4,4,4,2,5,4,"(slice(168, 194, None), slice(422, 454, None), slice(72, 78, None))","[181, 438, 75]",1.3.6.1.4.1.14519.5.2.1.6279.6001.153536305742006952753134773630
31,6,1,3,4,4,4,2,5,5,"(slice(229, 251, None), slice(415, 435, None), slice(79, 83, None))","[240, 425, 81]",1.3.6.1.4.1.14519.5.2.1.6279.6001.153536305742006952753134773630
32,6,1,3,3,4,4,1,4,4,"(slice(188, 201, None), slice(372, 388, None), slice(95, 98, None))","[194, 380, 96]",1.3.6.1.4.1.14519.5.2.1.6279.6001.153536305742006952753134773630
33,6,1,2,3,4,4,2,4,4,"(slice(277, 290, None), slice(135, 149, None), slice(108, 111, None))","[283, 142, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.153536305742006952753134773630
34,6,1,2,4,3,4,4,5,4,"(slice(258, 292, None), slice(323, 359, None), slice(125, 133, None))","[275, 341, 129]",1.3.6.1.4.1.14519.5.2.1.6279.6001.153536305742006952753134773630
35,6,1,1,2,5,5,1,4,5,"(slice(332, 343, None), slice(307, 320, None), slice(160, 165, None))","[337, 313, 162]",1.3.6.1.4.1.14519.5.2.1.6279.6001.108231420525711026834210228428
36,6,1,1,2,5,5,1,3,5,"(slice(291, 301, None), slice(85, 96, None), slice(59, 62, None))","[296, 90, 60]",1.3.6.1.4.1.14519.5.2.1.6279.6001.882070241245008756731854510592
37,5,1,2,3,4,4,1,4,5,"(slice(272, 291, None), slice(430, 449, None), slice(74, 80, None))","[281, 439, 77]",1.3.6.1.4.1.14519.5.2.1.6279.6001.882070241245008756731854510592
38,6,1,2,3,4,5,1,3,5,"(slice(222, 234, None), slice(373, 387, None), slice(80, 82, None))","[228, 380, 81]",1.3.6.1.4.1.14519.5.2.1.6279.6001.882070241245008756731854510592
39,6,1,3,4,4,4,2,5,5,"(slice(293, 328, None), slice(347, 382, None), slice(107, 114, None))","[310, 364, 110]",1.3.6.1.4.1.14519.5.2.1.6279.6001.882070241245008756731854510592
40,6,1,1,3,4,4,1,4,5,"(slice(214, 224, None), slice(305, 314, None), slice(188, 195, None))","[219, 309, 191]",1.3.6.1.4.1.14519.5.2.1.6279.6001.315214756157389122376518747372
41,4,1,2,2,5,4,1,5,5,"(slice(283, 297, None), slice(173, 188, None), slice(124, 130, None))","[290, 180, 127]",1.3.6.1.4.1.14519.5.2.1.6279.6001.188265424231150847356515802868
42,6,1,3,5,3,4,5,5,5,"(slice(204, 262, None), slice(290, 341, None), slice(112, 124, None))","[233, 315, 118]",1.3.6.1.4.1.14519.5.2.1.6279.6001.487745546557477250336016826588
43,6,1,1,3,4,4,1,2,5,"(slice(131, 139, None), slice(361, 369, None), slice(52, 54, None))","[135, 365, 53]",1.3.6.1.4.1.14519.5.2.1.6279.6001.206028343897359374907954580114
44,6,1,2,3,4,4,2,4,5,"(slice(158, 178, None), slice(157, 177, None), slice(70, 75, None))","[168, 167, 72]",1.3.6.1.4.1.14519.5.2.1.6279.6001.206028343897359374907954580114
45,6,1,2,2,4,4,1,3,4,"(slice(262, 285, None), slice(344, 364, None), slice(83, 87, None))","[273, 354, 85]",1.3.6.1.4.1.14519.5.2.1.6279.6001.206028343897359374907954580114
46,6,1,1,3,5,5,1,4,5,"(slice(276, 289, None), slice(351, 365, None), slice(75, 80, None))","[282, 358, 77]",1.3.6.1.4.1.14519.5.2.1.6279.6001.183843376225716802567192412456
47,6,1,1,3,4,3,1,3,5,"(slice(360, 371, None), slice(285, 298, None), slice(82, 85, None))","[365, 291, 83]",1.3.6.1.4.1.14519.5.2.1.6279.6001.183843376225716802567192412456
48,6,1,1,2,5,5,1,3,5,"(slice(258, 269, None), slice(324, 336, None), slice(90, 94, None))","[263, 330, 92]",1.3.6.1.4.1.14519.5.2.1.6279.6001.183843376225716802567192412456
49,6,1,2,3,5,4,2,2,5,"(slice(232, 244, None), slice(292, 303, None), slice(107, 111, None))","[238, 297, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.183843376225716802567192412456
50,6,1,2,4,4,4,1,4,5,"(slice(346, 363, None), slice(146, 163, None), slice(87, 91, None))","[354, 154, 89]",1.3.6.1.4.1.14519.5.2.1.6279.6001.124663713663969377020085460568
51,6,1,1,2,4,4,1,3,5,"(slice(350, 362, None), slice(181, 192, None), slice(111, 115, None))","[356, 186, 113]",1.3.6.1.4.1.14519.5.2.1.6279.6001.124663713663969377020085460568
52,6,1,2,4,4,4,2,4,4,"(slice(304, 327, None), slice(337, 362, None), slice(109, 118, None))","[315, 349, 113]",1.3.6.1.4.1.14519.5.2.1.6279.6001.124663713663969377020085460568
53,6,1,2,3,3,4,1,4,4,"(slice(315, 329, None), slice(375, 390, None), slice(105, 108, None))","[322, 382, 106]",1.3.6.1.4.1.14519.5.2.1.6279.6001.513023675145166449943177283490
54,6,1,1,2,5,4,1,4,5,"(slice(261, 271, None), slice(451, 460, None), slice(96, 100, None))","[266, 455, 98]",1.3.6.1.4.1.14519.5.2.1.6279.6001.338447145504282422142824032832
55,6,1,2,4,3,4,2,4,4,"(slice(305, 320, None), slice(132, 150, None), slice(198, 206, None))","[312, 141, 202]",1.3.6.1.4.1.14519.5.2.1.6279.6001.177685820605315926524514718990
56,6,1,2,4,4,4,4,5,4,"(slice(271, 306, None), slice(393, 419, None), slice(79, 86, None))","[288, 406, 82]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219909753224298157409438012179
57,6,1,1,4,4,3,3,4,5,"(slice(353, 373, None), slice(368, 387, None), slice(106, 111, None))","[363, 377, 108]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219909753224298157409438012179
58,4,1,1,2,5,4,1,4,5,"(slice(296, 308, None), slice(145, 157, None), slice(117, 122, None))","[302, 151, 119]",1.3.6.1.4.1.14519.5.2.1.6279.6001.332829333783605240302521201463
59,5,1,2,2,5,4,2,4,5,"(slice(263, 272, None), slice(141, 150, None), slice(121, 126, None))","[267, 145, 123]",1.3.6.1.4.1.14519.5.2.1.6279.6001.332829333783605240302521201463
60,4,1,1,1,5,4,2,4,5,"(slice(295, 303, None), slice(247, 256, None), slice(131, 136, None))","[299, 251, 133]",1.3.6.1.4.1.14519.5.2.1.6279.6001.332829333783605240302521201463
61,4,1,2,1,5,3,2,4,5,"(slice(361, 374, None), slice(234, 246, None), slice(162, 167, None))","[367, 240, 164]",1.3.6.1.4.1.14519.5.2.1.6279.6001.332829333783605240302521201463
62,6,1,1,3,5,5,1,4,5,"(slice(202, 214, None), slice(201, 214, None), slice(172, 179, None))","[208, 207, 175]",1.3.6.1.4.1.14519.5.2.1.6279.6001.332829333783605240302521201463
63,6,1,1,3,4,4,1,3,4,"(slice(381, 394, None), slice(95, 108, None), slice(42, 45, None))","[387, 101, 43]",1.3.6.1.4.1.14519.5.2.1.6279.6001.272961322147784625028175033640
64,6,1,4,4,4,4,3,5,5,"(slice(316, 352, None), slice(308, 336, None), slice(45, 54, None))","[334, 322, 49]",1.3.6.1.4.1.14519.5.2.1.6279.6001.138813197521718693188313387015
65,6,1,1,3,4,4,1,3,5,"(slice(343, 354, None), slice(296, 306, None), slice(41, 44, None))","[348, 301, 42]",1.3.6.1.4.1.14519.5.2.1.6279.6001.280125803152924778388346920341
66,6,1,1,3,3,4,3,4,4,"(slice(261, 274, None), slice(170, 185, None), slice(48, 50, None))","[267, 177, 49]",1.3.6.1.4.1.14519.5.2.1.6279.6001.280125803152924778388346920341
67,6,1,1,3,5,5,1,4,5,"(slice(362, 372, None), slice(284, 294, None), slice(77, 80, None))","[367, 289, 78]",1.3.6.1.4.1.14519.5.2.1.6279.6001.280125803152924778388346920341
68,6,1,1,3,4,4,1,4,5,"(slice(317, 331, None), slice(393, 410, None), slice(55, 58, None))","[324, 401, 56]",1.3.6.1.4.1.14519.5.2.1.6279.6001.503980049263254396021509831276
69,6,1,2,3,5,4,1,4,5,"(slice(204, 230, None), slice(123, 149, None), slice(82, 89, None))","[217, 136, 85]",1.3.6.1.4.1.14519.5.2.1.6279.6001.503980049263254396021509831276
70,6,1,4,4,4,4,2,5,5,"(slice(292, 322, None), slice(125, 154, None), slice(88, 95, None))","[307, 139, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.503980049263254396021509831276
71,4,1,1,2,5,4,1,4,5,"(slice(304, 324, None), slice(131, 145, None), slice(63, 68, None))","[314, 138, 65]",1.3.6.1.4.1.14519.5.2.1.6279.6001.511347030803753100045216493273
72,6,1,4,4,4,4,3,5,4,"(slice(355, 408, None), slice(326, 365, None), slice(61, 73, None))","[381, 345, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.511347030803753100045216493273
73,6,1,2,3,4,4,1,4,5,"(slice(213, 233, None), slice(95, 114, None), slice(71, 76, None))","[223, 104, 73]",1.3.6.1.4.1.14519.5.2.1.6279.6001.511347030803753100045216493273
74,6,1,1,3,4,3,1,4,5,"(slice(356, 370, None), slice(353, 367, None), slice(23, 28, None))","[363, 360, 25]",1.3.6.1.4.1.14519.5.2.1.6279.6001.910435939545691201820711078950
75,6,1,4,4,4,3,3,4,5,"(slice(358, 380, None), slice(327, 346, None), slice(45, 48, None))","[369, 336, 46]",1.3.6.1.4.1.14519.5.2.1.6279.6001.910435939545691201820711078950
76,6,1,2,3,2,4,1,2,2,"(slice(221, 233, None), slice(113, 126, None), slice(55, 58, None))","[227, 119, 56]",1.3.6.1.4.1.14519.5.2.1.6279.6001.910435939545691201820711078950
77,4,1,1,2,5,5,1,5,5,"(slice(322, 334, None), slice(80, 91, None), slice(61, 64, None))","[328, 85, 62]",1.3.6.1.4.1.14519.5.2.1.6279.6001.910435939545691201820711078950
78,6,1,3,4,4,4,2,5,5,"(slice(187, 223, None), slice(288, 317, None), slice(80, 87, None))","[205, 302, 83]",1.3.6.1.4.1.14519.5.2.1.6279.6001.910435939545691201820711078950
79,6,1,1,3,4,4,1,4,4,"(slice(355, 371, None), slice(153, 167, None), slice(82, 88, None))","[363, 160, 85]",1.3.6.1.4.1.14519.5.2.1.6279.6001.910435939545691201820711078950
80,6,1,2,4,3,4,2,4,5,"(slice(185, 201, None), slice(272, 289, None), slice(85, 89, None))","[193, 280, 87]",1.3.6.1.4.1.14519.5.2.1.6279.6001.910435939545691201820711078950
81,6,1,1,3,4,4,1,3,5,"(slice(341, 352, None), slice(77, 91, None), slice(32, 36, None))","[346, 84, 34]",1.3.6.1.4.1.14519.5.2.1.6279.6001.122914038048856168343065566972
82,6,1,1,3,5,4,1,3,3,"(slice(231, 241, None), slice(144, 151, None), slice(36, 39, None))","[236, 147, 37]",1.3.6.1.4.1.14519.5.2.1.6279.6001.122914038048856168343065566972
83,6,1,1,3,3,4,1,2,3,"(slice(334, 344, None), slice(79, 88, None), slice(37, 39, None))","[339, 83, 38]",1.3.6.1.4.1.14519.5.2.1.6279.6001.122914038048856168343065566972
84,6,1,2,2,4,4,1,4,5,"(slice(324, 336, None), slice(425, 436, None), slice(39, 42, None))","[330, 430, 40]",1.3.6.1.4.1.14519.5.2.1.6279.6001.122914038048856168343065566972
85,6,1,2,3,4,4,2,3,5,"(slice(215, 226, None), slice(73, 86, None), slice(38, 45, None))","[220, 79, 41]",1.3.6.1.4.1.14519.5.2.1.6279.6001.122914038048856168343065566972
86,6,1,2,3,4,4,1,3,5,"(slice(361, 373, None), slice(371, 382, None), slice(47, 50, None))","[367, 376, 48]",1.3.6.1.4.1.14519.5.2.1.6279.6001.122914038048856168343065566972
87,6,1,3,4,4,4,2,5,5,"(slice(271, 298, None), slice(130, 161, None), slice(74, 79, None))","[284, 145, 76]",1.3.6.1.4.1.14519.5.2.1.6279.6001.338114620394879648539943280992
88,6,1,3,4,3,4,2,3,3,"(slice(207, 228, None), slice(156, 183, None), slice(186, 193, None))","[217, 169, 189]",1.3.6.1.4.1.14519.5.2.1.6279.6001.250863365157630276148828903732
89,6,1,1,2,4,3,1,4,5,"(slice(140, 151, None), slice(355, 369, None), slice(63, 65, None))","[145, 362, 64]",1.3.6.1.4.1.14519.5.2.1.6279.6001.239358021703233250639913775427
90,6,1,1,2,3,3,1,4,4,"(slice(356, 369, None), slice(320, 333, None), slice(65, 68, None))","[362, 326, 66]",1.3.6.1.4.1.14519.5.2.1.6279.6001.239358021703233250639913775427
91,6,1,2,2,3,4,1,3,4,"(slice(362, 374, None), slice(336, 348, None), slice(66, 69, None))","[368, 342, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.239358021703233250639913775427
92,6,1,1,2,4,4,1,4,4,"(slice(221, 234, None), slice(112, 124, None), slice(66, 69, None))","[227, 118, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.239358021703233250639913775427
93,6,1,2,4,4,3,2,4,5,"(slice(358, 375, None), slice(168, 190, None), slice(67, 70, None))","[366, 179, 68]",1.3.6.1.4.1.14519.5.2.1.6279.6001.239358021703233250639913775427
94,6,1,1,2,4,4,1,4,4,"(slice(138, 155, None), slice(331, 343, None), slice(68, 72, None))","[146, 337, 70]",1.3.6.1.4.1.14519.5.2.1.6279.6001.239358021703233250639913775427
95,6,1,1,2,3,4,1,3,4,"(slice(165, 174, None), slice(356, 368, None), slice(69, 71, None))","[169, 362, 70]",1.3.6.1.4.1.14519.5.2.1.6279.6001.239358021703233250639913775427
96,6,1,1,2,4,4,1,4,4,"(slice(193, 208, None), slice(165, 177, None), slice(72, 74, None))","[200, 171, 73]",1.3.6.1.4.1.14519.5.2.1.6279.6001.239358021703233250639913775427
97,6,1,4,4,4,4,4,5,5,"(slice(296, 355, None), slice(142, 199, None), slice(89, 99, None))","[325, 170, 94]",1.3.6.1.4.1.14519.5.2.1.6279.6001.183982839679953938397312236359
98,6,1,2,4,5,4,1,4,5,"(slice(184, 201, None), slice(155, 173, None), slice(77, 83, None))","[192, 164, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.779493719385047675154892222907
99,6,1,1,4,4,4,2,4,5,"(slice(234, 252, None), slice(331, 347, None), slice(92, 98, None))","[243, 339, 95]",1.3.6.1.4.1.14519.5.2.1.6279.6001.779493719385047675154892222907
100,6,1,1,2,5,4,1,3,5,"(slice(226, 240, None), slice(341, 359, None), slice(80, 88, None))","[233, 350, 84]",1.3.6.1.4.1.14519.5.2.1.6279.6001.126631670596873065041988320084
101,6,1,3,5,4,3,4,5,5,"(slice(273, 341, None), slice(140, 224, None), slice(221, 252, None))","[307, 182, 236]",1.3.6.1.4.1.14519.5.2.1.6279.6001.126631670596873065041988320084
102,6,1,2,4,5,4,2,5,5,"(slice(217, 259, None), slice(389, 434, None), slice(32, 41, None))","[238, 411, 36]",1.3.6.1.4.1.14519.5.2.1.6279.6001.300136985030081433029390459071
103,6,1,2,3,4,4,2,4,5,"(slice(161, 182, None), slice(187, 208, None), slice(45, 49, None))","[171, 197, 47]",1.3.6.1.4.1.14519.5.2.1.6279.6001.300136985030081433029390459071
104,6,1,2,4,4,4,2,4,5,"(slice(274, 302, None), slice(388, 419, None), slice(82, 89, None))","[288, 403, 85]",1.3.6.1.4.1.14519.5.2.1.6279.6001.174449669706458092793093760291
105,6,1,2,3,3,4,2,4,4,"(slice(186, 219, None), slice(326, 357, None), slice(88, 94, None))","[202, 341, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.214252223927572015414741039150
106,6,1,2,4,2,4,1,3,3,"(slice(324, 336, None), slice(402, 417, None), slice(90, 93, None))","[330, 409, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.214252223927572015414741039150
107,6,1,3,4,4,4,3,5,4,"(slice(279, 303, None), slice(177, 196, None), slice(119, 124, None))","[291, 186, 121]",1.3.6.1.4.1.14519.5.2.1.6279.6001.214252223927572015414741039150
108,6,1,2,3,4,4,2,4,5,"(slice(297, 313, None), slice(298, 315, None), slice(76, 85, None))","[305, 306, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.161067514225109999586362698069
109,6,1,1,2,4,5,1,3,5,"(slice(293, 300, None), slice(145, 154, None), slice(117, 120, None))","[296, 149, 118]",1.3.6.1.4.1.14519.5.2.1.6279.6001.161067514225109999586362698069
110,6,1,1,2,4,4,1,2,5,"(slice(273, 283, None), slice(359, 368, None), slice(172, 175, None))","[278, 363, 173]",1.3.6.1.4.1.14519.5.2.1.6279.6001.161067514225109999586362698069
111,6,1,4,5,3,3,4,5,5,"(slice(319, 369, None), slice(311, 364, None), slice(153, 177, None))","[344, 337, 165]",1.3.6.1.4.1.14519.5.2.1.6279.6001.252634638822000832774167856951
112,6,1,3,5,4,3,4,5,5,"(slice(239, 291, None), slice(328, 395, None), slice(61, 73, None))","[265, 361, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.323408652979949774528873200770
113,6,1,4,4,4,4,2,5,5,"(slice(315, 345, None), slice(345, 400, None), slice(103, 111, None))","[330, 372, 107]",1.3.6.1.4.1.14519.5.2.1.6279.6001.323408652979949774528873200770
114,6,1,3,4,3,4,3,4,4,"(slice(368, 395, None), slice(312, 333, None), slice(99, 103, None))","[381, 322, 101]",1.3.6.1.4.1.14519.5.2.1.6279.6001.128023902651233986592378348912
115,6,1,3,4,3,4,4,4,4,"(slice(293, 321, None), slice(372, 394, None), slice(38, 44, None))","[307, 383, 41]",1.3.6.1.4.1.14519.5.2.1.6279.6001.463214953282361219537913355115
116,6,1,3,5,2,3,3,4,5,"(slice(210, 240, None), slice(333, 362, None), slice(83, 91, None))","[225, 347, 87]",1.3.6.1.4.1.14519.5.2.1.6279.6001.463214953282361219537913355115
117,6,1,1,4,5,4,1,4,5,"(slice(320, 351, None), slice(368, 398, None), slice(36, 42, None))","[335, 383, 39]",1.3.6.1.4.1.14519.5.2.1.6279.6001.287966244644280690737019247886
118,6,1,1,3,4,4,1,3,4,"(slice(238, 247, None), slice(392, 404, None), slice(68, 70, None))","[242, 398, 69]",1.3.6.1.4.1.14519.5.2.1.6279.6001.287966244644280690737019247886
119,6,1,1,3,5,4,1,3,5,"(slice(290, 308, None), slice(280, 294, None), slice(87, 91, None))","[299, 287, 89]",1.3.6.1.4.1.14519.5.2.1.6279.6001.287966244644280690737019247886
120,6,1,1,2,5,4,1,4,5,"(slice(196, 207, None), slice(324, 341, None), slice(89, 92, None))","[201, 332, 90]",1.3.6.1.4.1.14519.5.2.1.6279.6001.287966244644280690737019247886
121,6,1,1,4,5,5,1,4,5,"(slice(339, 382, None), slice(320, 368, None), slice(85, 100, None))","[360, 344, 92]",1.3.6.1.4.1.14519.5.2.1.6279.6001.287966244644280690737019247886
122,6,3,1,3,5,4,1,3,3,"(slice(326, 341, None), slice(181, 196, None), slice(65, 69, None))","[333, 188, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.272190966764020277652079081128
123,6,1,1,3,4,5,1,4,4,"(slice(276, 289, None), slice(128, 139, None), slice(105, 108, None))","[282, 133, 106]",1.3.6.1.4.1.14519.5.2.1.6279.6001.272190966764020277652079081128
124,6,1,2,3,4,5,1,4,5,"(slice(234, 271, None), slice(379, 417, None), slice(73, 83, None))","[252, 398, 78]",1.3.6.1.4.1.14519.5.2.1.6279.6001.487268565754493433372433148666
125,6,1,4,4,4,4,2,5,5,"(slice(292, 324, None), slice(184, 211, None), slice(107, 118, None))","[308, 197, 112]",1.3.6.1.4.1.14519.5.2.1.6279.6001.487268565754493433372433148666
126,6,1,3,4,4,4,2,4,5,"(slice(360, 383, None), slice(407, 428, None), slice(442, 456, None))","[371, 417, 449]",1.3.6.1.4.1.14519.5.2.1.6279.6001.430109407146633213496148200410
127,6,1,4,4,3,3,4,5,5,"(slice(335, 378, None), slice(177, 229, None), slice(564, 599, None))","[356, 203, 581]",1.3.6.1.4.1.14519.5.2.1.6279.6001.430109407146633213496148200410
128,6,1,2,4,3,3,4,4,4,"(slice(323, 338, None), slice(310, 327, None), slice(660, 667, None))","[330, 318, 663]",1.3.6.1.4.1.14519.5.2.1.6279.6001.430109407146633213496148200410
129,6,1,1,4,5,4,1,5,5,"(slice(273, 288, None), slice(421, 437, None), slice(110, 120, None))","[280, 429, 115]",1.3.6.1.4.1.14519.5.2.1.6279.6001.228511122591230092662900221600
130,5,1,1,2,4,4,1,4,4,"(slice(301, 311, None), slice(415, 427, None), slice(128, 133, None))","[306, 421, 130]",1.3.6.1.4.1.14519.5.2.1.6279.6001.228511122591230092662900221600
131,6,1,2,3,4,4,2,3,5,"(slice(219, 230, None), slice(250, 264, None), slice(137, 142, None))","[224, 257, 139]",1.3.6.1.4.1.14519.5.2.1.6279.6001.228511122591230092662900221600
132,6,1,2,3,5,4,1,4,5,"(slice(338, 347, None), slice(319, 329, None), slice(117, 122, None))","[342, 324, 119]",1.3.6.1.4.1.14519.5.2.1.6279.6001.187108608022306504546286626125
133,6,1,1,3,5,4,1,4,5,"(slice(209, 223, None), slice(404, 420, None), slice(147, 153, None))","[216, 412, 150]",1.3.6.1.4.1.14519.5.2.1.6279.6001.187108608022306504546286626125
134,6,1,1,4,5,4,2,4,5,"(slice(340, 353, None), slice(170, 185, None), slice(153, 160, None))","[346, 177, 156]",1.3.6.1.4.1.14519.5.2.1.6279.6001.187108608022306504546286626125
135,6,1,1,4,5,4,1,4,5,"(slice(213, 233, None), slice(403, 419, None), slice(203, 212, None))","[223, 411, 207]",1.3.6.1.4.1.14519.5.2.1.6279.6001.187108608022306504546286626125
136,6,1,2,4,5,4,2,4,5,"(slice(192, 215, None), slice(147, 167, None), slice(210, 220, None))","[203, 157, 215]",1.3.6.1.4.1.14519.5.2.1.6279.6001.187108608022306504546286626125
137,6,1,1,3,4,5,1,2,5,"(slice(222, 233, None), slice(371, 382, None), slice(79, 81, None))","[227, 376, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.178391668569567816549737454720
138,6,1,3,5,3,3,4,5,4,"(slice(237, 302, None), slice(154, 206, None), slice(246, 269, None))","[269, 180, 257]",1.3.6.1.4.1.14519.5.2.1.6279.6001.153985109349433321657655488650
139,6,1,2,4,4,4,2,4,5,"(slice(248, 289, None), slice(317, 356, None), slice(107, 113, None))","[268, 336, 110]",1.3.6.1.4.1.14519.5.2.1.6279.6001.279300249795483097365868125932
140,6,1,3,4,4,3,2,5,5,"(slice(225, 259, None), slice(161, 202, None), slice(77, 84, None))","[242, 181, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.339546614783708685476232944897
141,6,1,3,5,4,3,5,5,5,"(slice(300, 346, None), slice(156, 206, None), slice(151, 172, None))","[323, 181, 161]",1.3.6.1.4.1.14519.5.2.1.6279.6001.177086402277715068525592995222
142,6,1,2,3,4,3,2,3,2,"(slice(246, 263, None), slice(185, 203, None), slice(216, 224, None))","[254, 194, 220]",1.3.6.1.4.1.14519.5.2.1.6279.6001.177086402277715068525592995222
143,6,1,2,3,4,4,1,4,5,"(slice(278, 292, None), slice(67, 81, None), slice(72, 80, None))","[285, 74, 76]",1.3.6.1.4.1.14519.5.2.1.6279.6001.479402560265137632920333093071
144,6,1,1,3,3,4,1,3,2,"(slice(283, 296, None), slice(84, 93, None), slice(128, 134, None))","[289, 88, 131]",1.3.6.1.4.1.14519.5.2.1.6279.6001.479402560265137632920333093071
145,6,1,2,4,5,4,2,5,5,"(slice(271, 308, None), slice(205, 237, None), slice(67, 77, None))","[289, 221, 72]",1.3.6.1.4.1.14519.5.2.1.6279.6001.669435869708883155232318480131
146,6,1,2,4,5,4,1,5,5,"(slice(279, 318, None), slice(188, 230, None), slice(76, 89, None))","[298, 209, 82]",1.3.6.1.4.1.14519.5.2.1.6279.6001.613212850444255764524630781782
147,6,1,3,5,4,4,4,5,5,"(slice(261, 322, None), slice(185, 241, None), slice(90, 108, None))","[291, 213, 99]",1.3.6.1.4.1.14519.5.2.1.6279.6001.613212850444255764524630781782
148,6,1,1,4,5,4,1,4,5,"(slice(291, 305, None), slice(151, 173, None), slice(66, 70, None))","[298, 162, 68]",1.3.6.1.4.1.14519.5.2.1.6279.6001.172243743899615313644757844726
149,4,1,3,4,5,3,3,5,5,"(slice(313, 343, None), slice(125, 149, None), slice(93, 103, None))","[328, 137, 98]",1.3.6.1.4.1.14519.5.2.1.6279.6001.172243743899615313644757844726
150,6,1,2,5,4,4,5,5,5,"(slice(315, 353, None), slice(90, 131, None), slice(166, 181, None))","[334, 110, 173]",1.3.6.1.4.1.14519.5.2.1.6279.6001.652347820272212119124022644822
151,6,1,1,3,4,5,1,2,5,"(slice(324, 333, None), slice(307, 317, None), slice(50, 53, None))","[328, 312, 51]",1.3.6.1.4.1.14519.5.2.1.6279.6001.204287915902811325371247860532
152,6,1,1,3,4,4,2,2,5,"(slice(347, 357, None), slice(338, 349, None), slice(113, 118, None))","[352, 343, 115]",1.3.6.1.4.1.14519.5.2.1.6279.6001.204287915902811325371247860532
153,6,1,1,2,3,3,2,3,5,"(slice(354, 368, None), slice(76, 86, None), slice(55, 58, None))","[361, 81, 56]",1.3.6.1.4.1.14519.5.2.1.6279.6001.288701997968615460794642979503
154,6,1,1,2,3,4,2,4,5,"(slice(286, 298, None), slice(437, 446, None), slice(55, 58, None))","[292, 441, 56]",1.3.6.1.4.1.14519.5.2.1.6279.6001.288701997968615460794642979503
155,6,1,1,3,4,3,1,4,5,"(slice(335, 344, None), slice(193, 208, None), slice(123, 126, None))","[339, 200, 124]",1.3.6.1.4.1.14519.5.2.1.6279.6001.288701997968615460794642979503
156,6,1,3,5,4,4,5,5,5,"(slice(212, 262, None), slice(160, 209, None), slice(103, 112, None))","[237, 184, 107]",1.3.6.1.4.1.14519.5.2.1.6279.6001.194632613233275988184244485809
157,6,1,2,3,3,4,1,2,2,"(slice(366, 381, None), slice(383, 398, None), slice(72, 76, None))","[373, 390, 74]",1.3.6.1.4.1.14519.5.2.1.6279.6001.113679818447732724990336702075
158,4,1,2,1,4,4,1,4,5,"(slice(265, 278, None), slice(147, 163, None), slice(152, 157, None))","[271, 155, 154]",1.3.6.1.4.1.14519.5.2.1.6279.6001.113679818447732724990336702075
159,6,1,1,2,3,4,2,3,3,"(slice(309, 319, None), slice(395, 407, None), slice(181, 185, None))","[314, 401, 183]",1.3.6.1.4.1.14519.5.2.1.6279.6001.113679818447732724990336702075
160,6,1,2,4,5,4,3,5,5,"(slice(210, 248, None), slice(348, 387, None), slice(45, 56, None))","[229, 367, 50]",1.3.6.1.4.1.14519.5.2.1.6279.6001.146987333806092287055399155268
161,6,1,2,3,4,4,2,4,5,"(slice(276, 294, None), slice(113, 125, None), slice(99, 102, None))","[285, 119, 100]",1.3.6.1.4.1.14519.5.2.1.6279.6001.733642690503782454656013446707
162,6,1,2,3,4,4,1,3,5,"(slice(220, 235, None), slice(387, 399, None), slice(56, 63, None))","[227, 393, 59]",1.3.6.1.4.1.14519.5.2.1.6279.6001.134996872583497382954024478441
163,6,1,2,3,4,4,2,4,5,"(slice(201, 215, None), slice(174, 186, None), slice(98, 105, None))","[208, 180, 101]",1.3.6.1.4.1.14519.5.2.1.6279.6001.134996872583497382954024478441
164,6,1,2,3,4,4,2,4,5,"(slice(314, 331, None), slice(184, 205, None), slice(148, 158, None))","[322, 194, 153]",1.3.6.1.4.1.14519.5.2.1.6279.6001.134996872583497382954024478441
165,6,2,2,3,4,4,1,4,4,"(slice(267, 280, None), slice(169, 185, None), slice(155, 168, None))","[273, 177, 161]",1.3.6.1.4.1.14519.5.2.1.6279.6001.134996872583497382954024478441
166,4,1,1,1,5,4,1,5,5,"(slice(223, 236, None), slice(208, 219, None), slice(158, 165, None))","[229, 213, 161]",1.3.6.1.4.1.14519.5.2.1.6279.6001.286217539434358186648717203667
167,5,1,2,3,4,4,1,5,5,"(slice(296, 311, None), slice(196, 212, None), slice(175, 185, None))","[303, 204, 180]",1.3.6.1.4.1.14519.5.2.1.6279.6001.286217539434358186648717203667
168,4,1,1,1,5,4,1,5,5,"(slice(255, 265, None), slice(346, 352, None), slice(210, 214, None))","[260, 349, 212]",1.3.6.1.4.1.14519.5.2.1.6279.6001.286217539434358186648717203667
169,6,1,1,4,5,5,1,3,5,"(slice(350, 364, None), slice(193, 208, None), slice(55, 58, None))","[357, 200, 56]",1.3.6.1.4.1.14519.5.2.1.6279.6001.227707494413800460340110762069
170,5,1,4,4,4,3,2,5,5,"(slice(266, 296, None), slice(118, 189, None), slice(292, 317, None))","[281, 153, 304]",1.3.6.1.4.1.14519.5.2.1.6279.6001.143622857676008763729469324839
171,6,1,2,4,4,4,2,4,5,"(slice(169, 222, None), slice(392, 438, None), slice(186, 203, None))","[195, 415, 194]",1.3.6.1.4.1.14519.5.2.1.6279.6001.137773550852881583165286615668
172,6,1,1,4,4,3,2,4,5,"(slice(221, 255, None), slice(346, 382, None), slice(202, 213, None))","[238, 364, 207]",1.3.6.1.4.1.14519.5.2.1.6279.6001.137773550852881583165286615668
173,6,1,2,3,3,4,2,4,5,"(slice(330, 351, None), slice(209, 228, None), slice(108, 111, None))","[340, 218, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.310548927038333190233889983845
174,6,1,1,3,5,3,1,4,5,"(slice(273, 287, None), slice(131, 161, None), slice(111, 119, None))","[280, 146, 115]",1.3.6.1.4.1.14519.5.2.1.6279.6001.461155505515403114280165935891
175,6,1,1,2,4,3,1,3,4,"(slice(333, 345, None), slice(288, 303, None), slice(41, 47, None))","[339, 295, 44]",1.3.6.1.4.1.14519.5.2.1.6279.6001.931383239747372227838946053237
176,6,1,1,3,4,3,1,4,5,"(slice(280, 293, None), slice(148, 161, None), slice(77, 83, None))","[286, 154, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.931383239747372227838946053237
177,6,1,3,4,2,4,4,4,4,"(slice(329, 349, None), slice(81, 99, None), slice(63, 67, None))","[339, 90, 65]",1.3.6.1.4.1.14519.5.2.1.6279.6001.712472578497712558367294720243
178,6,1,4,5,4,4,4,5,5,"(slice(167, 195, None), slice(115, 159, None), slice(41, 48, None))","[181, 137, 44]",1.3.6.1.4.1.14519.5.2.1.6279.6001.251215764736737018371915284679
179,6,1,1,3,5,5,1,4,5,"(slice(359, 379, None), slice(328, 346, None), slice(108, 117, None))","[369, 337, 112]",1.3.6.1.4.1.14519.5.2.1.6279.6001.276710697414087561012670296643
180,6,1,2,3,4,4,1,3,5,"(slice(349, 361, None), slice(227, 237, None), slice(68, 71, None))","[355, 232, 69]",1.3.6.1.4.1.14519.5.2.1.6279.6001.257383535269991165447822992959
181,6,1,2,4,4,4,4,5,5,"(slice(315, 359, None), slice(212, 245, None), slice(69, 77, None))","[337, 228, 73]",1.3.6.1.4.1.14519.5.2.1.6279.6001.257383535269991165447822992959
182,6,1,3,5,4,4,4,5,5,"(slice(311, 353, None), slice(166, 200, None), slice(84, 91, None))","[332, 183, 87]",1.3.6.1.4.1.14519.5.2.1.6279.6001.257383535269991165447822992959
183,6,1,1,3,2,4,1,2,1,"(slice(363, 381, None), slice(179, 195, None), slice(92, 96, None))","[372, 187, 94]",1.3.6.1.4.1.14519.5.2.1.6279.6001.257383535269991165447822992959
184,6,1,1,4,4,4,1,4,5,"(slice(275, 299, None), slice(341, 367, None), slice(32, 36, None))","[287, 354, 34]",1.3.6.1.4.1.14519.5.2.1.6279.6001.313334055029671473836954456733
185,6,1,2,3,4,3,1,3,5,"(slice(310, 323, None), slice(339, 352, None), slice(40, 43, None))","[316, 345, 41]",1.3.6.1.4.1.14519.5.2.1.6279.6001.336225579776978874775723463327
186,6,1,2,4,4,4,2,4,5,"(slice(273, 294, None), slice(115, 137, None), slice(85, 93, None))","[283, 126, 89]",1.3.6.1.4.1.14519.5.2.1.6279.6001.105495028985881418176186711228
187,6,1,2,5,4,4,4,5,5,"(slice(213, 249, None), slice(142, 172, None), slice(98, 105, None))","[231, 157, 101]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202811684116768680758082619196
188,4,1,1,1,5,4,1,4,5,"(slice(332, 341, None), slice(129, 138, None), slice(65, 69, None))","[336, 133, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.129982010889624423230394257528
189,3,1,1,1,5,5,1,5,5,"(slice(217, 227, None), slice(200, 209, None), slice(92, 96, None))","[222, 204, 94]",1.3.6.1.4.1.14519.5.2.1.6279.6001.129982010889624423230394257528
190,3,1,1,1,5,4,1,5,5,"(slice(269, 280, None), slice(388, 397, None), slice(140, 146, None))","[274, 392, 143]",1.3.6.1.4.1.14519.5.2.1.6279.6001.129982010889624423230394257528
191,4,1,1,1,5,5,1,4,5,"(slice(310, 318, None), slice(216, 227, None), slice(157, 162, None))","[314, 221, 159]",1.3.6.1.4.1.14519.5.2.1.6279.6001.129982010889624423230394257528
192,2,1,1,1,5,4,2,5,5,"(slice(392, 414, None), slice(159, 186, None), slice(157, 170, None))","[403, 172, 163]",1.3.6.1.4.1.14519.5.2.1.6279.6001.185154482385982570363528682299
193,2,1,1,1,5,4,1,4,5,"(slice(337, 350, None), slice(340, 355, None), slice(189, 200, None))","[343, 347, 194]",1.3.6.1.4.1.14519.5.2.1.6279.6001.185154482385982570363528682299
194,6,1,1,3,2,3,1,3,1,"(slice(188, 222, None), slice(199, 223, None), slice(95, 104, None))","[205, 211, 99]",1.3.6.1.4.1.14519.5.2.1.6279.6001.254254303842550572473665729969
195,6,1,2,4,3,4,2,5,5,"(slice(281, 302, None), slice(227, 245, None), slice(147, 154, None))","[291, 236, 150]",1.3.6.1.4.1.14519.5.2.1.6279.6001.254254303842550572473665729969
196,6,1,2,4,5,4,1,4,5,"(slice(156, 177, None), slice(277, 300, None), slice(155, 167, None))","[166, 288, 161]",1.3.6.1.4.1.14519.5.2.1.6279.6001.225515255547637437801620523312
197,6,1,1,3,5,5,1,4,5,"(slice(209, 219, None), slice(346, 355, None), slice(160, 166, None))","[214, 350, 163]",1.3.6.1.4.1.14519.5.2.1.6279.6001.225515255547637437801620523312
198,6,1,1,3,4,4,1,3,5,"(slice(342, 353, None), slice(105, 117, None), slice(32, 35, None))","[347, 111, 33]",1.3.6.1.4.1.14519.5.2.1.6279.6001.108197895896446896160048741492
199,6,1,1,3,5,5,1,4,5,"(slice(301, 310, None), slice(226, 237, None), slice(57, 59, None))","[305, 231, 58]",1.3.6.1.4.1.14519.5.2.1.6279.6001.112767175295249119452142211437
200,6,1,1,2,5,4,1,3,5,"(slice(226, 232, None), slice(86, 94, None), slice(63, 66, None))","[229, 90, 64]",1.3.6.1.4.1.14519.5.2.1.6279.6001.112767175295249119452142211437
201,6,1,2,3,5,4,1,4,5,"(slice(280, 294, None), slice(384, 401, None), slice(68, 72, None))","[287, 392, 70]",1.3.6.1.4.1.14519.5.2.1.6279.6001.112767175295249119452142211437
202,6,1,1,2,5,5,1,3,5,"(slice(229, 239, None), slice(125, 136, None), slice(82, 84, None))","[234, 130, 83]",1.3.6.1.4.1.14519.5.2.1.6279.6001.112767175295249119452142211437
203,6,1,1,3,5,5,1,3,5,"(slice(306, 314, None), slice(339, 347, None), slice(85, 88, None))","[310, 343, 86]",1.3.6.1.4.1.14519.5.2.1.6279.6001.112767175295249119452142211437
204,6,1,2,3,4,4,1,3,5,"(slice(315, 327, None), slice(176, 188, None), slice(87, 90, None))","[321, 182, 88]",1.3.6.1.4.1.14519.5.2.1.6279.6001.112767175295249119452142211437
205,6,1,1,3,5,5,1,4,5,"(slice(266, 275, None), slice(352, 362, None), slice(89, 92, None))","[270, 357, 90]",1.3.6.1.4.1.14519.5.2.1.6279.6001.112767175295249119452142211437
206,6,1,1,2,5,5,1,4,5,"(slice(323, 334, None), slice(316, 325, None), slice(89, 93, None))","[328, 320, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.112767175295249119452142211437
207,6,1,1,2,4,4,1,3,4,"(slice(344, 355, None), slice(271, 280, None), slice(87, 90, None))","[349, 275, 88]",1.3.6.1.4.1.14519.5.2.1.6279.6001.454273545863197752384437758130
208,6,1,2,2,4,4,1,4,5,"(slice(208, 218, None), slice(355, 366, None), slice(96, 99, None))","[213, 360, 97]",1.3.6.1.4.1.14519.5.2.1.6279.6001.454273545863197752384437758130
209,6,1,1,3,3,5,1,1,1,"(slice(316, 325, None), slice(362, 369, None), slice(74, 78, None))","[320, 365, 76]",1.3.6.1.4.1.14519.5.2.1.6279.6001.241717018262666382493757419144
210,6,1,1,4,3,4,2,3,3,"(slice(303, 325, None), slice(289, 312, None), slice(170, 178, None))","[314, 300, 174]",1.3.6.1.4.1.14519.5.2.1.6279.6001.241717018262666382493757419144
211,6,1,1,3,5,5,1,3,5,"(slice(332, 344, None), slice(352, 365, None), slice(48, 54, None))","[338, 358, 51]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176030616406569931557298712518
212,6,1,2,4,5,4,2,5,5,"(slice(265, 292, None), slice(317, 338, None), slice(49, 59, None))","[278, 327, 54]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176030616406569931557298712518
213,6,1,1,4,5,5,2,4,5,"(slice(340, 364, None), slice(180, 198, None), slice(60, 70, None))","[352, 189, 65]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176030616406569931557298712518
214,6,1,1,3,5,5,1,3,5,"(slice(166, 179, None), slice(172, 184, None), slice(80, 83, None))","[172, 178, 81]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176030616406569931557298712518
215,6,1,1,3,5,4,1,4,5,"(slice(358, 370, None), slice(210, 222, None), slice(89, 96, None))","[364, 216, 92]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176030616406569931557298712518
216,6,1,1,2,5,3,1,3,5,"(slice(174, 184, None), slice(403, 412, None), slice(94, 97, None))","[179, 407, 95]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176030616406569931557298712518
217,6,1,1,3,5,4,1,3,5,"(slice(300, 312, None), slice(242, 252, None), slice(111, 115, None))","[306, 247, 113]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176030616406569931557298712518
218,6,1,1,3,5,4,1,4,5,"(slice(208, 223, None), slice(198, 212, None), slice(116, 122, None))","[215, 205, 119]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176030616406569931557298712518
219,6,1,1,3,4,5,1,4,5,"(slice(331, 345, None), slice(206, 219, None), slice(123, 129, None))","[338, 212, 126]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176030616406569931557298712518
220,6,1,2,3,5,4,2,4,5,"(slice(355, 372, None), slice(349, 363, None), slice(124, 131, None))","[363, 356, 127]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176030616406569931557298712518
221,6,1,2,2,4,4,2,3,5,"(slice(214, 224, None), slice(401, 411, None), slice(145, 149, None))","[219, 406, 147]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176030616406569931557298712518
222,6,1,1,3,5,4,1,3,5,"(slice(198, 206, None), slice(211, 220, None), slice(162, 166, None))","[202, 215, 164]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176030616406569931557298712518
223,6,1,2,4,5,5,2,4,5,"(slice(257, 293, None), slice(175, 201, None), slice(106, 121, None))","[275, 188, 113]",1.3.6.1.4.1.14519.5.2.1.6279.6001.257515388956260258681136624817
224,4,1,2,2,4,4,2,4,5,"(slice(301, 310, None), slice(358, 370, None), slice(138, 146, None))","[305, 364, 142]",1.3.6.1.4.1.14519.5.2.1.6279.6001.257515388956260258681136624817
225,6,1,2,3,3,3,2,4,1,"(slice(367, 387, None), slice(336, 356, None), slice(120, 124, None))","[377, 346, 122]",1.3.6.1.4.1.14519.5.2.1.6279.6001.975426625618184773401026809852
226,6,1,1,3,5,3,1,3,5,"(slice(312, 338, None), slice(200, 224, None), slice(35, 44, None))","[325, 212, 39]",1.3.6.1.4.1.14519.5.2.1.6279.6001.133378195429627807109985347209
227,6,1,1,3,5,4,1,4,5,"(slice(351, 373, None), slice(167, 191, None), slice(130, 143, None))","[362, 179, 136]",1.3.6.1.4.1.14519.5.2.1.6279.6001.133378195429627807109985347209
228,6,1,1,3,4,4,2,4,5,"(slice(297, 313, None), slice(108, 123, None), slice(49, 53, None))","[305, 115, 51]",1.3.6.1.4.1.14519.5.2.1.6279.6001.294120933998772507043263238704
229,6,1,1,2,4,4,1,4,5,"(slice(334, 345, None), slice(187, 198, None), slice(65, 69, None))","[339, 192, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.294120933998772507043263238704
230,6,1,1,2,4,4,1,4,5,"(slice(271, 280, None), slice(369, 378, None), slice(90, 92, None))","[275, 373, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.294120933998772507043263238704
231,6,1,3,2,3,3,1,3,5,"(slice(203, 212, None), slice(319, 330, None), slice(104, 107, None))","[207, 324, 105]",1.3.6.1.4.1.14519.5.2.1.6279.6001.294120933998772507043263238704
232,6,1,2,4,3,3,3,5,5,"(slice(344, 386, None), slice(95, 142, None), slice(98, 109, None))","[365, 118, 103]",1.3.6.1.4.1.14519.5.2.1.6279.6001.970428941353693253759289796610
233,6,1,3,4,4,3,2,5,5,"(slice(254, 289, None), slice(109, 126, None), slice(107, 111, None))","[271, 117, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.970428941353693253759289796610
234,6,1,3,4,4,3,2,5,5,"(slice(271, 336, None), slice(88, 136, None), slice(27, 37, None))","[303, 112, 32]",1.3.6.1.4.1.14519.5.2.1.6279.6001.481278873893653517789960724156
235,6,1,3,4,4,3,2,5,5,"(slice(353, 388, None), slice(291, 317, None), slice(39, 47, None))","[370, 304, 43]",1.3.6.1.4.1.14519.5.2.1.6279.6001.481278873893653517789960724156
236,6,1,2,3,5,4,1,5,5,"(slice(187, 215, None), slice(86, 106, None), slice(278, 295, None))","[201, 96, 286]",1.3.6.1.4.1.14519.5.2.1.6279.6001.139258777898746693365877042411
237,3,1,2,3,4,3,2,5,5,"(slice(157, 221, None), slice(83, 148, None), slice(148, 191, None))","[189, 115, 169]",1.3.6.1.4.1.14519.5.2.1.6279.6001.267957701183569638795986183786
238,5,1,2,3,4,2,2,5,5,"(slice(184, 206, None), slice(148, 165, None), slice(200, 209, None))","[195, 156, 204]",1.3.6.1.4.1.14519.5.2.1.6279.6001.267957701183569638795986183786
239,6,1,2,2,4,3,2,4,4,"(slice(178, 188, None), slice(109, 118, None), slice(210, 215, None))","[183, 113, 212]",1.3.6.1.4.1.14519.5.2.1.6279.6001.267957701183569638795986183786
240,6,1,1,2,4,4,1,4,5,"(slice(349, 358, None), slice(158, 170, None), slice(315, 320, None))","[353, 164, 317]",1.3.6.1.4.1.14519.5.2.1.6279.6001.267957701183569638795986183786
241,4,1,1,1,5,4,1,4,5,"(slice(347, 357, None), slice(156, 166, None), slice(95, 103, None))","[352, 161, 99]",1.3.6.1.4.1.14519.5.2.1.6279.6001.233433352108264931671753343044
242,6,1,3,2,4,3,2,3,5,"(slice(326, 339, None), slice(124, 135, None), slice(190, 197, None))","[332, 129, 193]",1.3.6.1.4.1.14519.5.2.1.6279.6001.233433352108264931671753343044
243,6,1,2,3,4,5,2,4,5,"(slice(210, 225, None), slice(88, 107, None), slice(198, 207, None))","[217, 97, 202]",1.3.6.1.4.1.14519.5.2.1.6279.6001.233433352108264931671753343044
244,4,1,1,2,5,4,1,4,5,"(slice(353, 365, None), slice(110, 124, None), slice(22, 25, None))","[359, 117, 23]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249404938669582150398726875826
245,4,1,2,2,5,4,1,5,5,"(slice(337, 353, None), slice(302, 321, None), slice(22, 27, None))","[345, 311, 24]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249404938669582150398726875826
246,4,1,2,2,5,4,1,4,5,"(slice(363, 378, None), slice(325, 343, None), slice(63, 68, None))","[370, 334, 65]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249404938669582150398726875826
247,6,1,1,4,5,4,1,4,5,"(slice(292, 309, None), slice(394, 414, None), slice(66, 70, None))","[300, 404, 68]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249404938669582150398726875826
248,6,1,2,4,4,5,3,4,5,"(slice(355, 369, None), slice(180, 193, None), slice(59, 63, None))","[362, 186, 61]",1.3.6.1.4.1.14519.5.2.1.6279.6001.333145094436144085379032922488
249,6,1,2,3,4,4,1,4,5,"(slice(374, 387, None), slice(182, 195, None), slice(15, 20, None))","[380, 188, 17]",1.3.6.1.4.1.14519.5.2.1.6279.6001.188059920088313909273628445208
250,6,1,1,3,4,5,1,5,5,"(slice(362, 372, None), slice(88, 100, None), slice(30, 33, None))","[367, 94, 31]",1.3.6.1.4.1.14519.5.2.1.6279.6001.188059920088313909273628445208
251,6,1,4,4,4,4,2,5,5,"(slice(295, 316, None), slice(130, 149, None), slice(80, 86, None))","[305, 139, 83]",1.3.6.1.4.1.14519.5.2.1.6279.6001.128881800399702510818644205032
252,6,1,3,4,4,3,4,5,5,"(slice(330, 352, None), slice(154, 181, None), slice(81, 87, None))","[341, 167, 84]",1.3.6.1.4.1.14519.5.2.1.6279.6001.128881800399702510818644205032
253,5,1,2,2,4,3,2,4,5,"(slice(400, 416, None), slice(323, 336, None), slice(52, 56, None))","[408, 329, 54]",1.3.6.1.4.1.14519.5.2.1.6279.6001.225154811831720426832024114593
254,6,1,1,2,5,5,1,4,5,"(slice(177, 186, None), slice(393, 402, None), slice(70, 73, None))","[181, 397, 71]",1.3.6.1.4.1.14519.5.2.1.6279.6001.225154811831720426832024114593
255,5,1,1,2,5,5,1,4,5,"(slice(394, 404, None), slice(358, 369, None), slice(79, 82, None))","[399, 363, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.225154811831720426832024114593
256,6,1,1,3,5,5,1,4,5,"(slice(192, 206, None), slice(208, 224, None), slice(58, 62, None))","[199, 216, 60]",1.3.6.1.4.1.14519.5.2.1.6279.6001.724562063158320418413995627171
257,6,1,2,4,4,3,1,5,5,"(slice(280, 302, None), slice(151, 176, None), slice(53, 60, None))","[291, 163, 56]",1.3.6.1.4.1.14519.5.2.1.6279.6001.183924380327950237519832859527
258,6,1,3,4,5,4,2,5,5,"(slice(196, 226, None), slice(202, 230, None), slice(79, 86, None))","[211, 216, 82]",1.3.6.1.4.1.14519.5.2.1.6279.6001.183924380327950237519832859527
259,6,1,2,3,4,4,2,3,5,"(slice(288, 299, None), slice(279, 288, None), slice(134, 143, None))","[293, 283, 138]",1.3.6.1.4.1.14519.5.2.1.6279.6001.244204120220889433826451158706
260,6,1,2,3,3,3,2,4,5,"(slice(278, 295, None), slice(111, 128, None), slice(139, 146, None))","[286, 119, 142]",1.3.6.1.4.1.14519.5.2.1.6279.6001.244204120220889433826451158706
261,6,1,2,3,5,4,1,4,5,"(slice(186, 208, None), slice(111, 128, None), slice(71, 77, None))","[197, 119, 74]",1.3.6.1.4.1.14519.5.2.1.6279.6001.255999614855292116767517149228
262,4,1,2,2,5,4,2,4,5,"(slice(311, 321, None), slice(113, 126, None), slice(48, 54, None))","[316, 119, 51]",1.3.6.1.4.1.14519.5.2.1.6279.6001.603126300703296693942875967838
263,6,1,1,4,5,5,2,4,5,"(slice(283, 299, None), slice(233, 248, None), slice(63, 68, None))","[291, 240, 65]",1.3.6.1.4.1.14519.5.2.1.6279.6001.603126300703296693942875967838
264,6,1,1,3,5,4,1,4,5,"(slice(389, 404, None), slice(358, 374, None), slice(63, 68, None))","[396, 366, 65]",1.3.6.1.4.1.14519.5.2.1.6279.6001.603126300703296693942875967838
265,6,1,1,3,4,3,1,4,5,"(slice(332, 343, None), slice(409, 418, None), slice(71, 74, None))","[337, 413, 72]",1.3.6.1.4.1.14519.5.2.1.6279.6001.603126300703296693942875967838
266,6,1,2,3,4,4,2,3,5,"(slice(359, 375, None), slice(158, 173, None), slice(36, 43, None))","[367, 165, 39]",1.3.6.1.4.1.14519.5.2.1.6279.6001.189483585244687808087477024767
267,6,1,3,3,4,4,1,3,5,"(slice(339, 349, None), slice(392, 402, None), slice(71, 75, None))","[344, 397, 73]",1.3.6.1.4.1.14519.5.2.1.6279.6001.189483585244687808087477024767
268,6,1,2,3,4,3,2,3,4,"(slice(203, 222, None), slice(140, 159, None), slice(76, 80, None))","[212, 149, 78]",1.3.6.1.4.1.14519.5.2.1.6279.6001.837810280808122125183730411210
269,6,1,2,4,5,4,2,5,5,"(slice(216, 254, None), slice(405, 450, None), slice(35, 45, None))","[235, 427, 40]",1.3.6.1.4.1.14519.5.2.1.6279.6001.271307051432838466826189754230
270,6,1,1,3,5,5,1,4,5,"(slice(299, 310, None), slice(233, 244, None), slice(83, 88, None))","[304, 238, 85]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219428004988664846407984058588
271,6,1,1,3,4,5,2,4,5,"(slice(335, 346, None), slice(349, 365, None), slice(95, 103, None))","[340, 357, 99]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219428004988664846407984058588
272,6,1,2,4,4,4,2,4,5,"(slice(324, 342, None), slice(380, 400, None), slice(101, 113, None))","[333, 390, 107]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219428004988664846407984058588
273,6,1,1,3,4,4,1,4,5,"(slice(359, 371, None), slice(200, 213, None), slice(118, 127, None))","[365, 206, 122]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219428004988664846407984058588
274,6,1,1,2,5,5,1,4,5,"(slice(280, 290, None), slice(390, 400, None), slice(124, 130, None))","[285, 395, 127]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219428004988664846407984058588
275,6,1,1,3,5,5,1,3,5,"(slice(338, 347, None), slice(324, 335, None), slice(131, 137, None))","[342, 329, 134]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219428004988664846407984058588
276,6,1,2,3,5,4,1,3,5,"(slice(313, 323, None), slice(334, 344, None), slice(141, 146, None))","[318, 339, 143]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219428004988664846407984058588
277,6,1,2,3,5,4,1,4,5,"(slice(209, 224, None), slice(183, 195, None), slice(166, 173, None))","[216, 189, 169]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219428004988664846407984058588
278,6,1,3,4,4,4,1,5,5,"(slice(266, 284, None), slice(316, 331, None), slice(214, 227, None))","[275, 323, 220]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219428004988664846407984058588
279,4,1,1,1,5,4,1,4,5,"(slice(223, 235, None), slice(114, 124, None), slice(64, 69, None))","[229, 119, 66]",1.3.6.1.4.1.14519.5.2.1.6279.6001.295298571102631191572192562523
280,6,1,2,3,5,3,1,3,5,"(slice(237, 250, None), slice(386, 398, None), slice(107, 114, None))","[243, 392, 110]",1.3.6.1.4.1.14519.5.2.1.6279.6001.295298571102631191572192562523
281,6,1,3,4,4,4,2,4,5,"(slice(362, 380, None), slice(360, 380, None), slice(115, 123, None))","[371, 370, 119]",1.3.6.1.4.1.14519.5.2.1.6279.6001.935683764293840351008008793409
282,6,1,2,2,5,4,2,4,5,"(slice(353, 366, None), slice(142, 154, None), slice(126, 133, None))","[359, 148, 129]",1.3.6.1.4.1.14519.5.2.1.6279.6001.935683764293840351008008793409
283,6,1,2,4,4,4,3,5,5,"(slice(264, 298, None), slice(133, 180, None), slice(57, 77, None))","[281, 156, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.948414623428298219623354433437
284,6,1,3,4,4,3,3,5,4,"(slice(197, 239, None), slice(185, 219, None), slice(107, 121, None))","[218, 202, 114]",1.3.6.1.4.1.14519.5.2.1.6279.6001.948414623428298219623354433437
285,6,1,2,4,4,4,2,4,5,"(slice(320, 347, None), slice(308, 332, None), slice(16, 25, None))","[333, 320, 20]",1.3.6.1.4.1.14519.5.2.1.6279.6001.283569726884265181140892667131
286,6,1,1,2,4,4,1,3,5,"(slice(272, 282, None), slice(381, 393, None), slice(37, 40, None))","[277, 387, 38]",1.3.6.1.4.1.14519.5.2.1.6279.6001.283569726884265181140892667131
287,6,1,1,2,4,4,1,3,5,"(slice(279, 288, None), slice(366, 377, None), slice(41, 44, None))","[283, 371, 42]",1.3.6.1.4.1.14519.5.2.1.6279.6001.283569726884265181140892667131
288,6,1,1,2,4,4,1,3,5,"(slice(229, 239, None), slice(369, 378, None), slice(47, 51, None))","[234, 373, 49]",1.3.6.1.4.1.14519.5.2.1.6279.6001.283569726884265181140892667131
289,6,1,2,4,5,4,1,5,5,"(slice(313, 345, None), slice(327, 357, None), slice(105, 113, None))","[329, 342, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.752756872840730509471096155114
290,6,1,1,3,4,4,1,4,5,"(slice(235, 253, None), slice(402, 418, None), slice(81, 85, None))","[244, 410, 83]",1.3.6.1.4.1.14519.5.2.1.6279.6001.339142594937666268384335506819
291,6,1,3,3,4,4,1,4,5,"(slice(330, 347, None), slice(127, 141, None), slice(87, 91, None))","[338, 134, 89]",1.3.6.1.4.1.14519.5.2.1.6279.6001.367204840301639918160517361062
292,6,1,2,4,2,4,2,3,1,"(slice(333, 361, None), slice(170, 196, None), slice(61, 67, None))","[347, 183, 64]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202283133206014258077705539227
293,6,1,1,3,2,3,2,2,1,"(slice(332, 344, None), slice(298, 311, None), slice(85, 88, None))","[338, 304, 86]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202283133206014258077705539227
294,6,1,1,3,2,4,1,1,1,"(slice(269, 282, None), slice(96, 109, None), slice(86, 90, None))","[275, 102, 88]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202283133206014258077705539227
295,6,1,2,3,2,3,2,2,1,"(slice(279, 304, None), slice(220, 248, None), slice(98, 104, None))","[291, 234, 101]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202283133206014258077705539227
296,6,1,1,3,5,3,1,4,5,"(slice(340, 360, None), slice(337, 357, None), slice(61, 67, None))","[350, 347, 64]",1.3.6.1.4.1.14519.5.2.1.6279.6001.243094273518213382155770295147
297,5,1,1,3,5,5,1,5,5,"(slice(355, 368, None), slice(372, 386, None), slice(70, 75, None))","[361, 379, 72]",1.3.6.1.4.1.14519.5.2.1.6279.6001.243094273518213382155770295147
298,6,1,2,4,4,4,1,4,5,"(slice(319, 339, None), slice(301, 326, None), slice(72, 80, None))","[329, 313, 76]",1.3.6.1.4.1.14519.5.2.1.6279.6001.243094273518213382155770295147
299,6,1,1,3,4,4,2,4,5,"(slice(353, 367, None), slice(359, 372, None), slice(82, 86, None))","[360, 365, 84]",1.3.6.1.4.1.14519.5.2.1.6279.6001.243094273518213382155770295147
300,6,1,2,3,4,4,2,4,5,"(slice(290, 310, None), slice(313, 335, None), slice(107, 113, None))","[300, 324, 110]",1.3.6.1.4.1.14519.5.2.1.6279.6001.243094273518213382155770295147
301,6,1,2,2,4,4,2,4,5,"(slice(280, 300, None), slice(345, 366, None), slice(75, 79, None))","[290, 355, 77]",1.3.6.1.4.1.14519.5.2.1.6279.6001.100953483028192176989979435275
302,6,1,2,3,5,4,1,3,5,"(slice(270, 281, None), slice(376, 385, None), slice(93, 98, None))","[275, 380, 95]",1.3.6.1.4.1.14519.5.2.1.6279.6001.153732973534937692357111055819
303,6,1,3,4,4,2,4,5,5,"(slice(325, 375, None), slice(153, 186, None), slice(108, 118, None))","[350, 169, 113]",1.3.6.1.4.1.14519.5.2.1.6279.6001.153732973534937692357111055819
304,4,1,1,2,5,4,1,4,5,"(slice(265, 279, None), slice(132, 145, None), slice(68, 72, None))","[272, 138, 70]",1.3.6.1.4.1.14519.5.2.1.6279.6001.106630482085576298661469304872
305,6,1,2,3,4,3,2,4,5,"(slice(295, 309, None), slice(103, 118, None), slice(32, 37, None))","[302, 110, 34]",1.3.6.1.4.1.14519.5.2.1.6279.6001.197063290812663596858124411210
306,5,1,2,2,4,4,2,4,5,"(slice(174, 183, None), slice(127, 138, None), slice(64, 66, None))","[178, 132, 65]",1.3.6.1.4.1.14519.5.2.1.6279.6001.197063290812663596858124411210
307,6,1,1,2,4,4,1,4,5,"(slice(186, 197, None), slice(142, 153, None), slice(75, 77, None))","[191, 147, 76]",1.3.6.1.4.1.14519.5.2.1.6279.6001.197063290812663596858124411210
308,6,1,1,3,4,4,2,3,5,"(slice(204, 212, None), slice(161, 169, None), slice(92, 95, None))","[208, 165, 93]",1.3.6.1.4.1.14519.5.2.1.6279.6001.197063290812663596858124411210
309,4,1,1,1,4,4,1,5,5,"(slice(272, 287, None), slice(367, 383, None), slice(111, 116, None))","[279, 375, 113]",1.3.6.1.4.1.14519.5.2.1.6279.6001.197063290812663596858124411210
310,6,1,3,5,3,4,4,5,5,"(slice(293, 328, None), slice(329, 369, None), slice(117, 123, None))","[310, 349, 120]",1.3.6.1.4.1.14519.5.2.1.6279.6001.204566802718283633558802774757
311,6,1,3,4,4,4,3,4,5,"(slice(262, 296, None), slice(340, 369, None), slice(118, 126, None))","[279, 354, 122]",1.3.6.1.4.1.14519.5.2.1.6279.6001.102681962408431413578140925249
312,6,1,2,4,4,4,1,5,5,"(slice(417, 449, None), slice(172, 206, None), slice(54, 65, None))","[433, 189, 59]",1.3.6.1.4.1.14519.5.2.1.6279.6001.616033753016904899083676284739
313,6,1,2,2,5,4,1,4,5,"(slice(197, 209, None), slice(155, 166, None), slice(59, 62, None))","[203, 160, 60]",1.3.6.1.4.1.14519.5.2.1.6279.6001.616033753016904899083676284739
314,6,1,2,3,4,4,1,4,5,"(slice(347, 363, None), slice(351, 366, None), slice(66, 69, None))","[355, 358, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.616033753016904899083676284739
315,6,1,2,2,5,4,1,4,5,"(slice(383, 396, None), slice(222, 235, None), slice(69, 72, None))","[389, 228, 70]",1.3.6.1.4.1.14519.5.2.1.6279.6001.616033753016904899083676284739
316,6,1,3,4,5,4,1,5,5,"(slice(219, 264, None), slice(408, 435, None), slice(45, 56, None))","[241, 421, 50]",1.3.6.1.4.1.14519.5.2.1.6279.6001.306948744223170422945185006551
317,6,1,2,4,5,4,2,5,5,"(slice(244, 284, None), slice(389, 411, None), slice(88, 95, None))","[264, 400, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.306948744223170422945185006551
318,6,1,3,5,4,4,5,5,5,"(slice(239, 306, None), slice(128, 193, None), slice(84, 95, None))","[272, 160, 89]",1.3.6.1.4.1.14519.5.2.1.6279.6001.194766721609772924944646251928
319,6,1,2,3,4,4,1,4,5,"(slice(342, 360, None), slice(137, 161, None), slice(115, 121, None))","[351, 149, 118]",1.3.6.1.4.1.14519.5.2.1.6279.6001.868211851413924881662621747734
320,6,1,1,3,5,4,2,4,5,"(slice(343, 354, None), slice(209, 220, None), slice(142, 146, None))","[348, 214, 144]",1.3.6.1.4.1.14519.5.2.1.6279.6001.868211851413924881662621747734
321,6,1,1,3,5,5,1,3,5,"(slice(320, 329, None), slice(228, 237, None), slice(163, 167, None))","[324, 232, 165]",1.3.6.1.4.1.14519.5.2.1.6279.6001.868211851413924881662621747734
322,6,1,2,2,4,3,1,4,5,"(slice(153, 172, None), slice(167, 179, None), slice(74, 77, None))","[162, 173, 75]",1.3.6.1.4.1.14519.5.2.1.6279.6001.287560874054243719452635194040
323,6,1,3,3,2,3,4,4,3,"(slice(322, 371, None), slice(314, 368, None), slice(70, 78, None))","[346, 341, 74]",1.3.6.1.4.1.14519.5.2.1.6279.6001.198698492013538481395497694975
324,6,1,3,4,3,4,5,5,5,"(slice(203, 262, None), slice(330, 384, None), slice(91, 99, None))","[232, 357, 95]",1.3.6.1.4.1.14519.5.2.1.6279.6001.198698492013538481395497694975
325,6,1,3,4,3,4,4,5,4,"(slice(198, 228, None), slice(321, 342, None), slice(103, 109, None))","[213, 331, 106]",1.3.6.1.4.1.14519.5.2.1.6279.6001.198698492013538481395497694975
326,6,1,2,4,4,3,1,3,5,"(slice(257, 282, None), slice(288, 309, None), slice(37, 41, None))","[269, 298, 39]",1.3.6.1.4.1.14519.5.2.1.6279.6001.826829446346820089862659555750
327,6,1,2,4,4,4,2,5,5,"(slice(364, 385, None), slice(295, 321, None), slice(41, 47, None))","[374, 308, 44]",1.3.6.1.4.1.14519.5.2.1.6279.6001.826829446346820089862659555750
328,6,1,2,4,4,3,2,5,5,"(slice(361, 382, None), slice(327, 345, None), slice(46, 51, None))","[371, 336, 48]",1.3.6.1.4.1.14519.5.2.1.6279.6001.826829446346820089862659555750
329,6,1,2,4,4,4,2,5,5,"(slice(183, 212, None), slice(342, 374, None), slice(51, 59, None))","[197, 358, 55]",1.3.6.1.4.1.14519.5.2.1.6279.6001.826829446346820089862659555750
330,5,1,2,4,5,2,2,5,5,"(slice(195, 218, None), slice(326, 353, None), slice(81, 87, None))","[206, 339, 84]",1.3.6.1.4.1.14519.5.2.1.6279.6001.826829446346820089862659555750
331,6,1,2,4,4,4,2,5,5,"(slice(242, 257, None), slice(192, 208, None), slice(106, 110, None))","[249, 200, 108]",1.3.6.1.4.1.14519.5.2.1.6279.6001.826829446346820089862659555750
332,6,1,2,4,3,2,2,4,5,"(slice(293, 313, None), slice(136, 158, None), slice(97, 103, None))","[303, 147, 100]",1.3.6.1.4.1.14519.5.2.1.6279.6001.160586340600816116143631200450
333,6,1,1,3,5,4,1,5,5,"(slice(320, 340, None), slice(186, 211, None), slice(161, 174, None))","[330, 198, 167]",1.3.6.1.4.1.14519.5.2.1.6279.6001.302557165094691896097534021075
334,6,1,1,3,5,3,1,3,5,"(slice(188, 198, None), slice(123, 130, None), slice(59, 62, None))","[193, 126, 60]",1.3.6.1.4.1.14519.5.2.1.6279.6001.195913706607582347421429908613
335,3,1,1,1,5,4,1,5,5,"(slice(328, 339, None), slice(303, 315, None), slice(40, 43, None))","[333, 309, 41]",1.3.6.1.4.1.14519.5.2.1.6279.6001.329404588567903628160652715124
336,6,1,3,4,4,3,3,5,4,"(slice(195, 253, None), slice(298, 347, None), slice(109, 124, None))","[224, 322, 116]",1.3.6.1.4.1.14519.5.2.1.6279.6001.329404588567903628160652715124
337,6,1,3,4,4,2,3,4,5,"(slice(347, 380, None), slice(342, 381, None), slice(22, 36, None))","[363, 361, 29]",1.3.6.1.4.1.14519.5.2.1.6279.6001.149041668385192796520281592139
338,6,1,2,2,4,4,1,4,5,"(slice(245, 254, None), slice(430, 441, None), slice(103, 108, None))","[249, 435, 105]",1.3.6.1.4.1.14519.5.2.1.6279.6001.324567010179873305471925391582
339,6,2,3,4,2,4,3,5,4,"(slice(229, 248, None), slice(118, 141, None), slice(209, 218, None))","[238, 129, 213]",1.3.6.1.4.1.14519.5.2.1.6279.6001.324567010179873305471925391582
340,6,1,2,3,4,3,1,3,5,"(slice(268, 277, None), slice(193, 204, None), slice(264, 269, None))","[272, 198, 266]",1.3.6.1.4.1.14519.5.2.1.6279.6001.324567010179873305471925391582
341,6,1,3,3,3,3,2,4,5,"(slice(243, 274, None), slice(145, 191, None), slice(65, 70, None))","[258, 168, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.122621219961396951727742490470
342,6,1,2,4,4,4,2,5,5,"(slice(141, 158, None), slice(197, 218, None), slice(39, 42, None))","[149, 207, 40]",1.3.6.1.4.1.14519.5.2.1.6279.6001.310395752124284049604069960014
343,6,1,2,3,4,4,2,3,3,"(slice(268, 278, None), slice(126, 136, None), slice(49, 51, None))","[273, 131, 50]",1.3.6.1.4.1.14519.5.2.1.6279.6001.310395752124284049604069960014
344,6,1,2,3,5,4,1,4,5,"(slice(219, 229, None), slice(98, 109, None), slice(71, 74, None))","[224, 103, 72]",1.3.6.1.4.1.14519.5.2.1.6279.6001.310395752124284049604069960014
345,6,1,3,4,5,4,2,5,5,"(slice(362, 388, None), slice(166, 191, None), slice(43, 51, None))","[375, 178, 47]",1.3.6.1.4.1.14519.5.2.1.6279.6001.805925269324902055566754756843
346,6,1,2,3,4,4,2,4,5,"(slice(202, 215, None), slice(183, 194, None), slice(93, 96, None))","[208, 188, 94]",1.3.6.1.4.1.14519.5.2.1.6279.6001.193808128386712859512130599234
347,6,1,4,4,4,4,3,5,5,"(slice(343, 390, None), slice(351, 387, None), slice(67, 88, None))","[366, 369, 77]",1.3.6.1.4.1.14519.5.2.1.6279.6001.173931884906244951746140865701
348,4,1,1,1,5,4,1,5,5,"(slice(256, 265, None), slice(296, 305, None), slice(113, 115, None))","[260, 300, 114]",1.3.6.1.4.1.14519.5.2.1.6279.6001.261700367741314729940340271960
349,4,1,1,1,5,4,1,4,5,"(slice(326, 339, None), slice(118, 134, None), slice(89, 94, None))","[332, 126, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.200841000324240313648595016964
350,6,1,1,3,4,3,1,2,5,"(slice(338, 354, None), slice(389, 402, None), slice(108, 111, None))","[346, 395, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.312704771348460502013249647868
351,6,1,2,2,3,4,1,3,4,"(slice(387, 399, None), slice(289, 300, None), slice(145, 148, None))","[393, 294, 146]",1.3.6.1.4.1.14519.5.2.1.6279.6001.312704771348460502013249647868
352,3,1,2,1,5,4,1,4,5,"(slice(319, 326, None), slice(381, 390, None), slice(175, 179, None))","[322, 385, 177]",1.3.6.1.4.1.14519.5.2.1.6279.6001.312704771348460502013249647868
353,5,1,1,2,5,5,1,4,5,"(slice(279, 291, None), slice(106, 117, None), slice(78, 81, None))","[285, 111, 79]",1.3.6.1.4.1.14519.5.2.1.6279.6001.566816709786169715745131047975
354,6,1,2,4,4,4,2,5,5,"(slice(307, 334, None), slice(201, 225, None), slice(31, 38, None))","[320, 213, 34]",1.3.6.1.4.1.14519.5.2.1.6279.6001.313283554967554803238484128406
355,6,1,2,4,5,4,2,5,5,"(slice(264, 282, None), slice(109, 124, None), slice(81, 85, None))","[273, 116, 83]",1.3.6.1.4.1.14519.5.2.1.6279.6001.313283554967554803238484128406
356,6,1,2,4,5,4,2,5,5,"(slice(274, 289, None), slice(151, 167, None), slice(96, 101, None))","[281, 159, 98]",1.3.6.1.4.1.14519.5.2.1.6279.6001.313283554967554803238484128406
357,6,1,1,4,4,4,2,4,5,"(slice(269, 289, None), slice(174, 193, None), slice(50, 54, None))","[279, 183, 52]",1.3.6.1.4.1.14519.5.2.1.6279.6001.309901913847714156367981722205
358,6,1,3,3,3,3,3,4,1,"(slice(296, 325, None), slice(211, 242, None), slice(134, 143, None))","[310, 226, 138]",1.3.6.1.4.1.14519.5.2.1.6279.6001.208737629504245244513001631764
359,6,1,2,3,4,3,2,4,4,"(slice(182, 201, None), slice(129, 157, None), slice(140, 147, None))","[191, 143, 143]",1.3.6.1.4.1.14519.5.2.1.6279.6001.208737629504245244513001631764
360,6,1,2,4,4,3,2,4,5,"(slice(288, 321, None), slice(351, 375, None), slice(154, 169, None))","[304, 363, 161]",1.3.6.1.4.1.14519.5.2.1.6279.6001.208737629504245244513001631764
361,6,1,3,5,4,4,4,5,5,"(slice(236, 279, None), slice(362, 397, None), slice(99, 108, None))","[257, 379, 103]",1.3.6.1.4.1.14519.5.2.1.6279.6001.272344603176687884771013620823
362,4,1,1,2,5,4,1,5,5,"(slice(321, 333, None), slice(131, 146, None), slice(74, 77, None))","[327, 138, 75]",1.3.6.1.4.1.14519.5.2.1.6279.6001.233652865358649579816568545171
363,6,1,1,4,2,3,3,4,3,"(slice(343, 374, None), slice(279, 314, None), slice(39, 55, None))","[358, 296, 47]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249314567767437206995861966896
364,6,1,3,4,4,3,3,5,5,"(slice(249, 278, None), slice(328, 355, None), slice(191, 203, None))","[263, 341, 197]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249314567767437206995861966896
365,6,1,2,3,5,4,2,4,5,"(slice(169, 181, None), slice(373, 385, None), slice(75, 79, None))","[175, 379, 77]",1.3.6.1.4.1.14519.5.2.1.6279.6001.518487185634324801733841260431
366,6,1,2,4,4,3,2,5,5,"(slice(263, 277, None), slice(372, 392, None), slice(107, 112, None))","[270, 382, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.337845202462615014431060697507
367,5,1,1,2,5,3,1,4,5,"(slice(195, 205, None), slice(131, 140, None), slice(92, 94, None))","[200, 135, 93]",1.3.6.1.4.1.14519.5.2.1.6279.6001.173106154739244262091404659845
368,6,1,2,4,4,3,2,5,5,"(slice(242, 266, None), slice(96, 132, None), slice(92, 100, None))","[254, 114, 96]",1.3.6.1.4.1.14519.5.2.1.6279.6001.173106154739244262091404659845
369,6,1,1,4,4,4,2,5,5,"(slice(275, 292, None), slice(94, 113, None), slice(100, 104, None))","[283, 103, 102]",1.3.6.1.4.1.14519.5.2.1.6279.6001.173106154739244262091404659845
370,6,1,2,4,4,4,2,4,5,"(slice(294, 316, None), slice(342, 364, None), slice(102, 106, None))","[305, 353, 104]",1.3.6.1.4.1.14519.5.2.1.6279.6001.173106154739244262091404659845
371,6,1,2,3,4,4,1,4,5,"(slice(351, 363, None), slice(146, 158, None), slice(107, 110, None))","[357, 152, 108]",1.3.6.1.4.1.14519.5.2.1.6279.6001.173106154739244262091404659845
372,6,1,2,4,4,4,2,5,4,"(slice(343, 367, None), slice(179, 201, None), slice(108, 116, None))","[355, 190, 112]",1.3.6.1.4.1.14519.5.2.1.6279.6001.173106154739244262091404659845
373,6,1,1,3,4,4,1,4,5,"(slice(244, 254, None), slice(109, 119, None), slice(55, 58, None))","[249, 114, 56]",1.3.6.1.4.1.14519.5.2.1.6279.6001.328944769569002417592093467626
374,6,1,2,3,4,5,2,2,5,"(slice(330, 343, None), slice(307, 318, None), slice(96, 98, None))","[336, 312, 97]",1.3.6.1.4.1.14519.5.2.1.6279.6001.188209889686363159853715266493
375,6,1,2,3,4,4,1,3,5,"(slice(354, 364, None), slice(158, 168, None), slice(105, 107, None))","[359, 163, 106]",1.3.6.1.4.1.14519.5.2.1.6279.6001.188209889686363159853715266493
376,6,1,2,3,4,4,2,4,5,"(slice(303, 321, None), slice(360, 376, None), slice(41, 44, None))","[312, 368, 42]",1.3.6.1.4.1.14519.5.2.1.6279.6001.178680586845223339579041794709
377,5,1,2,2,4,4,2,4,5,"(slice(147, 166, None), slice(219, 238, None), slice(74, 78, None))","[156, 228, 76]",1.3.6.1.4.1.14519.5.2.1.6279.6001.162718361851587451505896742103
378,6,1,1,3,5,4,1,4,5,"(slice(303, 320, None), slice(244, 258, None), slice(62, 66, None))","[311, 251, 64]",1.3.6.1.4.1.14519.5.2.1.6279.6001.106164978370116976238911317774
379,6,1,1,2,5,5,1,4,5,"(slice(293, 305, None), slice(117, 130, None), slice(68, 71, None))","[299, 123, 69]",1.3.6.1.4.1.14519.5.2.1.6279.6001.438308540025607517017949816111
380,6,1,3,4,4,4,2,5,5,"(slice(203, 221, None), slice(162, 179, None), slice(78, 82, None))","[212, 170, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.438308540025607517017949816111
381,6,1,2,3,2,3,2,3,1,"(slice(319, 345, None), slice(337, 362, None), slice(44, 48, None))","[332, 349, 46]",1.3.6.1.4.1.14519.5.2.1.6279.6001.187451715205085403623595258748
382,6,1,4,4,2,3,4,5,4,"(slice(193, 233, None), slice(365, 406, None), slice(59, 66, None))","[213, 385, 62]",1.3.6.1.4.1.14519.5.2.1.6279.6001.187451715205085403623595258748
383,6,1,2,3,5,4,1,4,5,"(slice(338, 355, None), slice(77, 87, None), slice(102, 105, None))","[346, 82, 103]",1.3.6.1.4.1.14519.5.2.1.6279.6001.154703816225841204080664115280
384,6,1,2,2,4,2,2,4,4,"(slice(201, 213, None), slice(72, 87, None), slice(107, 111, None))","[207, 79, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.154703816225841204080664115280
385,6,1,2,2,4,2,1,3,4,"(slice(391, 401, None), slice(127, 139, None), slice(112, 115, None))","[396, 133, 113]",1.3.6.1.4.1.14519.5.2.1.6279.6001.154703816225841204080664115280
386,6,1,3,5,4,4,5,5,5,"(slice(302, 341, None), slice(371, 407, None), slice(107, 116, None))","[321, 389, 111]",1.3.6.1.4.1.14519.5.2.1.6279.6001.905371958588660410240398317235
387,6,1,1,2,4,4,2,4,5,"(slice(166, 182, None), slice(146, 160, None), slice(60, 63, None))","[174, 153, 61]",1.3.6.1.4.1.14519.5.2.1.6279.6001.270390050141765094612147226290
388,4,1,1,1,5,4,1,4,5,"(slice(201, 210, None), slice(387, 395, None), slice(80, 83, None))","[205, 391, 81]",1.3.6.1.4.1.14519.5.2.1.6279.6001.270390050141765094612147226290
389,5,1,2,4,4,3,2,4,5,"(slice(217, 245, None), slice(169, 198, None), slice(82, 88, None))","[231, 183, 85]",1.3.6.1.4.1.14519.5.2.1.6279.6001.270390050141765094612147226290
390,6,1,1,3,4,4,1,3,5,"(slice(380, 389, None), slice(128, 138, None), slice(59, 61, None))","[384, 133, 60]",1.3.6.1.4.1.14519.5.2.1.6279.6001.320111824803959660037459294083
391,4,1,1,2,4,4,1,5,5,"(slice(168, 183, None), slice(301, 316, None), slice(93, 96, None))","[175, 308, 94]",1.3.6.1.4.1.14519.5.2.1.6279.6001.944888107209008719031293531091
392,4,1,1,1,5,3,1,3,5,"(slice(348, 357, None), slice(363, 373, None), slice(16, 18, None))","[352, 368, 17]",1.3.6.1.4.1.14519.5.2.1.6279.6001.174168737938619557573021395302
393,6,1,1,3,5,4,1,4,5,"(slice(213, 232, None), slice(338, 355, None), slice(27, 34, None))","[222, 346, 30]",1.3.6.1.4.1.14519.5.2.1.6279.6001.321935195060268166151738328001
394,6,1,3,4,3,2,2,4,5,"(slice(349, 392, None), slice(183, 211, None), slice(79, 96, None))","[370, 197, 87]",1.3.6.1.4.1.14519.5.2.1.6279.6001.321935195060268166151738328001
395,6,1,2,4,4,4,2,4,5,"(slice(344, 360, None), slice(353, 375, None), slice(84, 89, None))","[352, 364, 86]",1.3.6.1.4.1.14519.5.2.1.6279.6001.230491296081537726468075344411
396,6,1,1,4,5,4,2,4,5,"(slice(315, 334, None), slice(357, 378, None), slice(86, 92, None))","[324, 367, 89]",1.3.6.1.4.1.14519.5.2.1.6279.6001.230491296081537726468075344411
397,6,1,1,3,3,4,2,4,4,"(slice(302, 314, None), slice(158, 181, None), slice(76, 81, None))","[308, 169, 78]",1.3.6.1.4.1.14519.5.2.1.6279.6001.307835307280028057486413359377
398,6,1,3,4,4,3,3,5,5,"(slice(207, 250, None), slice(187, 215, None), slice(87, 95, None))","[228, 201, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.330544495001617450666819906758
399,5,1,1,2,5,4,1,4,5,"(slice(260, 274, None), slice(120, 143, None), slice(66, 69, None))","[267, 131, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.866845763956586959109892274084
400,6,1,1,2,4,4,1,4,4,"(slice(346, 357, None), slice(180, 191, None), slice(55, 58, None))","[351, 185, 56]",1.3.6.1.4.1.14519.5.2.1.6279.6001.240969450540588211676803094518
401,6,1,2,2,5,4,1,4,5,"(slice(204, 215, None), slice(389, 399, None), slice(89, 91, None))","[209, 394, 90]",1.3.6.1.4.1.14519.5.2.1.6279.6001.240969450540588211676803094518
402,5,1,2,1,5,3,1,4,5,"(slice(389, 400, None), slice(200, 211, None), slice(46, 49, None))","[394, 205, 47]",1.3.6.1.4.1.14519.5.2.1.6279.6001.822128649427327893802314908658
403,6,1,3,4,3,3,2,4,4,"(slice(331, 352, None), slice(190, 213, None), slice(66, 72, None))","[341, 201, 69]",1.3.6.1.4.1.14519.5.2.1.6279.6001.614147706162329660656328811671
404,6,1,2,3,4,4,1,4,5,"(slice(348, 363, None), slice(343, 362, None), slice(85, 90, None))","[355, 352, 87]",1.3.6.1.4.1.14519.5.2.1.6279.6001.221017801605543296514746423389
405,6,1,2,3,4,4,1,4,5,"(slice(352, 366, None), slice(373, 386, None), slice(91, 94, None))","[359, 379, 92]",1.3.6.1.4.1.14519.5.2.1.6279.6001.221017801605543296514746423389
406,6,1,2,4,5,3,1,4,5,"(slice(347, 364, None), slice(100, 120, None), slice(29, 35, None))","[355, 110, 32]",1.3.6.1.4.1.14519.5.2.1.6279.6001.242761658169703141430370511586
407,6,1,4,4,4,4,2,5,5,"(slice(304, 331, None), slice(114, 139, None), slice(45, 51, None))","[317, 126, 48]",1.3.6.1.4.1.14519.5.2.1.6279.6001.121993590721161347818774929286
408,6,1,2,4,4,4,2,5,5,"(slice(301, 328, None), slice(171, 200, None), slice(55, 61, None))","[314, 185, 58]",1.3.6.1.4.1.14519.5.2.1.6279.6001.121993590721161347818774929286
409,6,1,2,4,3,4,2,4,5,"(slice(250, 270, None), slice(94, 117, None), slice(107, 115, None))","[260, 105, 111]",1.3.6.1.4.1.14519.5.2.1.6279.6001.127965161564033605177803085629
410,4,1,1,2,4,4,1,4,5,"(slice(152, 163, None), slice(167, 178, None), slice(87, 89, None))","[157, 172, 88]",1.3.6.1.4.1.14519.5.2.1.6279.6001.200513183558872708878454294671
411,5,1,1,3,5,4,1,5,5,"(slice(358, 372, None), slice(292, 308, None), slice(69, 77, None))","[365, 300, 73]",1.3.6.1.4.1.14519.5.2.1.6279.6001.262873069163227096134627700599
412,5,1,2,3,4,4,2,5,5,"(slice(349, 375, None), slice(156, 181, None), slice(118, 132, None))","[362, 168, 125]",1.3.6.1.4.1.14519.5.2.1.6279.6001.262873069163227096134627700599
413,6,1,3,4,2,3,4,5,4,"(slice(298, 342, None), slice(188, 239, None), slice(99, 115, None))","[320, 213, 107]",1.3.6.1.4.1.14519.5.2.1.6279.6001.217955041973656886482758642958
414,6,1,2,3,2,4,1,2,2,"(slice(370, 381, None), slice(169, 180, None), slice(188, 191, None))","[375, 174, 189]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249032660919473722154870746474
415,6,1,1,2,4,4,1,3,4,"(slice(237, 248, None), slice(337, 348, None), slice(87, 90, None))","[242, 342, 88]",1.3.6.1.4.1.14519.5.2.1.6279.6001.277452631455527999380186898011
416,5,1,2,2,4,4,1,4,5,"(slice(296, 309, None), slice(387, 400, None), slice(52, 54, None))","[302, 393, 53]",1.3.6.1.4.1.14519.5.2.1.6279.6001.248357157975955379661896491341
417,6,1,2,2,4,3,1,2,5,"(slice(387, 397, None), slice(205, 218, None), slice(81, 83, None))","[392, 211, 82]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176869045992276345870480098568
418,6,1,1,2,5,4,1,4,5,"(slice(252, 264, None), slice(86, 96, None), slice(58, 61, None))","[258, 91, 59]",1.3.6.1.4.1.14519.5.2.1.6279.6001.268992195564407418480563388746
419,6,1,2,3,5,4,1,5,5,"(slice(298, 315, None), slice(141, 155, None), slice(78, 84, None))","[306, 148, 81]",1.3.6.1.4.1.14519.5.2.1.6279.6001.621916089407825046337959219998
420,6,1,2,4,4,4,3,4,5,"(slice(348, 368, None), slice(343, 360, None), slice(51, 56, None))","[358, 351, 53]",1.3.6.1.4.1.14519.5.2.1.6279.6001.212346425055214308006918165305
421,5,1,2,3,4,4,2,5,5,"(slice(271, 291, None), slice(123, 145, None), slice(91, 96, None))","[281, 134, 93]",1.3.6.1.4.1.14519.5.2.1.6279.6001.141149610914910880857802344415
422,6,1,2,3,5,4,1,4,5,"(slice(335, 349, None), slice(159, 174, None), slice(88, 92, None))","[342, 166, 90]",1.3.6.1.4.1.14519.5.2.1.6279.6001.106379658920626694402549886949
423,6,1,1,3,5,4,1,4,5,"(slice(214, 223, None), slice(161, 171, None), slice(131, 135, None))","[218, 166, 133]",1.3.6.1.4.1.14519.5.2.1.6279.6001.106379658920626694402549886949
424,4,1,2,2,5,4,1,4,5,"(slice(411, 424, None), slice(315, 330, None), slice(49, 52, None))","[417, 322, 50]",1.3.6.1.4.1.14519.5.2.1.6279.6001.861997885565255340442123234170
425,6,1,2,3,4,4,1,4,5,"(slice(320, 333, None), slice(106, 118, None), slice(57, 60, None))","[326, 112, 58]",1.3.6.1.4.1.14519.5.2.1.6279.6001.257840703452266097926250569223
426,6,1,2,3,5,4,1,4,5,"(slice(337, 348, None), slice(384, 395, None), slice(69, 71, None))","[342, 389, 70]",1.3.6.1.4.1.14519.5.2.1.6279.6001.257840703452266097926250569223
427,6,1,2,3,4,4,1,3,5,"(slice(262, 273, None), slice(305, 315, None), slice(77, 82, None))","[267, 310, 79]",1.3.6.1.4.1.14519.5.2.1.6279.6001.150097650621090951325113116280
428,6,1,1,3,5,4,1,4,5,"(slice(332, 344, None), slice(103, 112, None), slice(56, 59, None))","[338, 107, 57]",1.3.6.1.4.1.14519.5.2.1.6279.6001.832260670372728970918746541371
429,3,1,2,1,5,3,1,4,5,"(slice(278, 288, None), slice(284, 297, None), slice(110, 113, None))","[283, 290, 111]",1.3.6.1.4.1.14519.5.2.1.6279.6001.832260670372728970918746541371
430,5,1,1,2,4,4,1,4,5,"(slice(364, 378, None), slice(374, 390, None), slice(79, 83, None))","[371, 382, 81]",1.3.6.1.4.1.14519.5.2.1.6279.6001.486999111981013268988489262668
431,4,1,1,1,5,5,1,4,5,"(slice(275, 285, None), slice(371, 382, None), slice(41, 44, None))","[280, 376, 42]",1.3.6.1.4.1.14519.5.2.1.6279.6001.128059192202504367870633619224
432,6,1,1,2,5,4,1,3,5,"(slice(216, 228, None), slice(346, 358, None), slice(95, 99, None))","[222, 352, 97]",1.3.6.1.4.1.14519.5.2.1.6279.6001.128059192202504367870633619224
433,6,1,2,2,4,4,1,3,5,"(slice(245, 255, None), slice(206, 215, None), slice(77, 79, None))","[250, 210, 78]",1.3.6.1.4.1.14519.5.2.1.6279.6001.395623571499047043765181005112
434,6,1,1,3,3,4,1,2,1,"(slice(361, 373, None), slice(320, 333, None), slice(77, 81, None))","[367, 326, 79]",1.3.6.1.4.1.14519.5.2.1.6279.6001.171667800241622018839592854574
435,6,1,2,2,4,4,1,3,5,"(slice(308, 319, None), slice(354, 362, None), slice(28, 29, None))","[313, 358, 28]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219618492426142913407827034169
436,6,1,1,3,5,4,1,4,5,"(slice(132, 165, None), slice(161, 203, None), slice(51, 60, None))","[148, 182, 55]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219618492426142913407827034169
437,6,1,1,3,4,4,1,4,5,"(slice(342, 354, None), slice(407, 419, None), slice(194, 205, None))","[348, 413, 199]",1.3.6.1.4.1.14519.5.2.1.6279.6001.171177995014336749670107905732
438,5,1,3,4,4,4,2,5,5,"(slice(189, 225, None), slice(293, 334, None), slice(100, 109, None))","[207, 313, 104]",1.3.6.1.4.1.14519.5.2.1.6279.6001.211051626197585058967163339846
439,6,1,1,3,2,4,1,2,1,"(slice(344, 368, None), slice(158, 188, None), slice(206, 215, None))","[356, 173, 210]",1.3.6.1.4.1.14519.5.2.1.6279.6001.326057189095429101398977448288
440,6,1,1,2,4,3,1,4,5,"(slice(314, 332, None), slice(283, 303, None), slice(244, 252, None))","[323, 293, 248]",1.3.6.1.4.1.14519.5.2.1.6279.6001.326057189095429101398977448288
441,6,1,1,2,3,4,1,2,2,"(slice(280, 295, None), slice(221, 237, None), slice(266, 273, None))","[287, 229, 269]",1.3.6.1.4.1.14519.5.2.1.6279.6001.326057189095429101398977448288
442,6,1,1,3,3,4,1,2,2,"(slice(334, 346, None), slice(173, 185, None), slice(274, 280, None))","[340, 179, 277]",1.3.6.1.4.1.14519.5.2.1.6279.6001.326057189095429101398977448288
443,6,1,1,2,3,4,2,3,4,"(slice(234, 248, None), slice(193, 203, None), slice(285, 290, None))","[241, 198, 287]",1.3.6.1.4.1.14519.5.2.1.6279.6001.326057189095429101398977448288
444,6,1,1,2,2,4,1,2,2,"(slice(279, 299, None), slice(364, 386, None), slice(286, 294, None))","[289, 375, 290]",1.3.6.1.4.1.14519.5.2.1.6279.6001.326057189095429101398977448288
445,6,1,1,3,4,3,1,4,5,"(slice(312, 321, None), slice(221, 232, None), slice(388, 395, None))","[316, 226, 391]",1.3.6.1.4.1.14519.5.2.1.6279.6001.170921541362033046216100409521
446,6,1,2,3,4,4,2,4,5,"(slice(292, 306, None), slice(215, 229, None), slice(437, 446, None))","[299, 222, 441]",1.3.6.1.4.1.14519.5.2.1.6279.6001.170921541362033046216100409521
447,6,1,1,2,5,4,1,3,5,"(slice(150, 159, None), slice(77, 85, None), slice(183, 187, None))","[154, 81, 185]",1.3.6.1.4.1.14519.5.2.1.6279.6001.324290109423920971676288828329
448,5,1,2,2,5,4,1,4,5,"(slice(104, 122, None), slice(181, 201, None), slice(192, 209, None))","[113, 191, 200]",1.3.6.1.4.1.14519.5.2.1.6279.6001.324290109423920971676288828329
449,5,1,2,3,5,4,1,5,5,"(slice(223, 250, None), slice(97, 116, None), slice(203, 216, None))","[236, 106, 209]",1.3.6.1.4.1.14519.5.2.1.6279.6001.324290109423920971676288828329
450,6,1,1,3,5,4,2,5,5,"(slice(113, 137, None), slice(356, 374, None), slice(210, 225, None))","[125, 365, 217]",1.3.6.1.4.1.14519.5.2.1.6279.6001.324290109423920971676288828329
451,6,1,2,3,4,3,2,4,5,"(slice(170, 184, None), slice(167, 181, None), slice(213, 223, None))","[177, 174, 218]",1.3.6.1.4.1.14519.5.2.1.6279.6001.238019241099704094018548301753
452,6,2,3,4,2,2,3,4,2,"(slice(343, 367, None), slice(386, 413, None), slice(154, 161, None))","[355, 399, 157]",1.3.6.1.4.1.14519.5.2.1.6279.6001.316911475886263032009840828684
453,6,1,2,3,2,4,2,2,1,"(slice(350, 366, None), slice(102, 117, None), slice(143, 147, None))","[358, 109, 145]",1.3.6.1.4.1.14519.5.2.1.6279.6001.111258527162678142285870245028
454,6,1,2,4,3,3,2,4,2,"(slice(299, 323, None), slice(154, 185, None), slice(226, 235, None))","[311, 169, 230]",1.3.6.1.4.1.14519.5.2.1.6279.6001.111258527162678142285870245028
455,6,1,2,3,4,4,1,3,5,"(slice(211, 222, None), slice(405, 415, None), slice(276, 287, None))","[216, 410, 281]",1.3.6.1.4.1.14519.5.2.1.6279.6001.270215889102603268207599305185
456,6,1,1,4,4,4,2,5,4,"(slice(200, 216, None), slice(119, 142, None), slice(188, 192, None))","[208, 130, 190]",1.3.6.1.4.1.14519.5.2.1.6279.6001.199261544234308780356714831537
457,6,1,2,4,2,4,3,4,4,"(slice(301, 321, None), slice(150, 169, None), slice(188, 196, None))","[311, 159, 192]",1.3.6.1.4.1.14519.5.2.1.6279.6001.177252583002664900748714851615
458,6,1,2,4,4,3,1,5,5,"(slice(336, 351, None), slice(444, 464, None), slice(47, 56, None))","[343, 454, 51]",1.3.6.1.4.1.14519.5.2.1.6279.6001.294188507421106424248264912111
459,6,1,1,4,4,4,1,5,5,"(slice(383, 395, None), slice(96, 111, None), slice(51, 55, None))","[389, 103, 53]",1.3.6.1.4.1.14519.5.2.1.6279.6001.294188507421106424248264912111
460,6,1,1,4,4,3,1,5,5,"(slice(384, 397, None), slice(414, 428, None), slice(70, 76, None))","[390, 421, 73]",1.3.6.1.4.1.14519.5.2.1.6279.6001.294188507421106424248264912111
461,6,1,3,5,2,4,4,4,4,"(slice(203, 248, None), slice(349, 404, None), slice(163, 183, None))","[225, 376, 173]",1.3.6.1.4.1.14519.5.2.1.6279.6001.123697637451437522065941162930
462,6,1,2,3,4,3,2,5,5,"(slice(227, 259, None), slice(170, 201, None), slice(151, 170, None))","[243, 185, 160]",1.3.6.1.4.1.14519.5.2.1.6279.6001.154677396354641150280013275227
463,5,1,2,2,4,4,2,4,5,"(slice(254, 275, None), slice(44, 65, None), slice(225, 236, None))","[264, 54, 230]",1.3.6.1.4.1.14519.5.2.1.6279.6001.439153572396640163898529626096
464,6,1,2,3,5,4,1,4,5,"(slice(223, 236, None), slice(453, 464, None), slice(38, 44, None))","[229, 458, 41]",1.3.6.1.4.1.14519.5.2.1.6279.6001.124154461048929153767743874565
465,6,1,1,3,5,3,1,4,5,"(slice(390, 400, None), slice(152, 168, None), slice(130, 135, None))","[395, 160, 132]",1.3.6.1.4.1.14519.5.2.1.6279.6001.231002159523969307155990628066
466,3,1,1,1,5,4,1,4,5,"(slice(294, 302, None), slice(378, 389, None), slice(170, 172, None))","[298, 383, 171]",1.3.6.1.4.1.14519.5.2.1.6279.6001.231002159523969307155990628066
467,4,1,1,2,5,4,2,4,5,"(slice(284, 303, None), slice(397, 413, None), slice(175, 183, None))","[293, 405, 179]",1.3.6.1.4.1.14519.5.2.1.6279.6001.231002159523969307155990628066
468,6,1,3,4,3,3,4,4,5,"(slice(346, 384, None), slice(137, 195, None), slice(134, 161, None))","[365, 166, 147]",1.3.6.1.4.1.14519.5.2.1.6279.6001.725023183844147505748475581290
469,6,1,2,4,4,4,2,4,4,"(slice(289, 303, None), slice(414, 427, None), slice(88, 91, None))","[296, 420, 89]",1.3.6.1.4.1.14519.5.2.1.6279.6001.302403227435841351528721627052
470,6,1,2,3,4,4,2,3,4,"(slice(296, 307, None), slice(411, 423, None), slice(91, 94, None))","[301, 417, 92]",1.3.6.1.4.1.14519.5.2.1.6279.6001.302403227435841351528721627052
471,6,1,3,5,3,4,2,5,4,"(slice(165, 237, None), slice(83, 146, None), slice(92, 105, None))","[201, 114, 98]",1.3.6.1.4.1.14519.5.2.1.6279.6001.316900421002460665752357657094
472,6,1,4,3,4,2,4,4,5,"(slice(255, 275, None), slice(315, 339, None), slice(160, 168, None))","[265, 327, 164]",1.3.6.1.4.1.14519.5.2.1.6279.6001.316900421002460665752357657094
473,6,1,4,4,3,3,3,4,5,"(slice(214, 265, None), slice(101, 134, None), slice(149, 171, None))","[239, 117, 160]",1.3.6.1.4.1.14519.5.2.1.6279.6001.159996104466052855396410079250
474,6,1,1,3,4,4,1,4,5,"(slice(347, 356, None), slice(103, 113, None), slice(81, 85, None))","[351, 108, 83]",1.3.6.1.4.1.14519.5.2.1.6279.6001.315187221221054114974341475212
475,6,1,2,3,4,5,1,4,5,"(slice(372, 381, None), slice(126, 134, None), slice(102, 106, None))","[376, 130, 104]",1.3.6.1.4.1.14519.5.2.1.6279.6001.315187221221054114974341475212
476,6,1,1,3,5,4,1,4,5,"(slice(332, 343, None), slice(107, 118, None), slice(145, 151, None))","[337, 112, 148]",1.3.6.1.4.1.14519.5.2.1.6279.6001.199220738144407033276946096708
477,6,1,1,3,3,4,1,3,3,"(slice(265, 277, None), slice(384, 396, None), slice(221, 225, None))","[271, 390, 223]",1.3.6.1.4.1.14519.5.2.1.6279.6001.130438550890816550994739120843
478,6,1,2,3,4,3,2,4,4,"(slice(298, 370, None), slice(369, 425, None), slice(31, 88, None))","[334, 397, 59]",1.3.6.1.4.1.14519.5.2.1.6279.6001.323753921818102744511069914832
479,6,1,1,2,4,4,2,3,5,"(slice(299, 309, None), slice(421, 434, None), slice(77, 83, None))","[304, 427, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.111496024928645603833332252962
480,6,1,2,3,4,4,2,3,5,"(slice(265, 277, None), slice(420, 430, None), slice(103, 105, None))","[271, 425, 104]",1.3.6.1.4.1.14519.5.2.1.6279.6001.111496024928645603833332252962
481,6,1,1,3,4,3,2,4,5,"(slice(386, 400, None), slice(283, 298, None), slice(132, 136, None))","[393, 290, 134]",1.3.6.1.4.1.14519.5.2.1.6279.6001.111496024928645603833332252962
482,6,1,1,2,5,5,1,4,5,"(slice(271, 286, None), slice(189, 204, None), slice(99, 107, None))","[278, 196, 103]",1.3.6.1.4.1.14519.5.2.1.6279.6001.160124400349792614505500125883
483,6,1,4,3,2,2,2,4,5,"(slice(320, 356, None), slice(92, 121, None), slice(291, 313, None))","[338, 106, 302]",1.3.6.1.4.1.14519.5.2.1.6279.6001.271220641987745483198036913951
484,6,1,1,3,4,4,1,2,5,"(slice(257, 270, None), slice(318, 333, None), slice(106, 110, None))","[263, 325, 108]",1.3.6.1.4.1.14519.5.2.1.6279.6001.387954549120924524005910602207
485,6,1,2,2,3,4,1,3,5,"(slice(275, 287, None), slice(71, 87, None), slice(167, 172, None))","[281, 79, 169]",1.3.6.1.4.1.14519.5.2.1.6279.6001.387954549120924524005910602207
486,6,1,3,4,5,5,3,5,5,"(slice(225, 251, None), slice(338, 363, None), slice(114, 123, None))","[238, 350, 118]",1.3.6.1.4.1.14519.5.2.1.6279.6001.690929968028676628605553365896
487,6,1,1,3,4,4,1,4,5,"(slice(170, 182, None), slice(88, 100, None), slice(185, 189, None))","[176, 94, 187]",1.3.6.1.4.1.14519.5.2.1.6279.6001.121108220866971173712229588402
488,6,1,1,3,3,4,1,2,2,"(slice(297, 309, None), slice(281, 292, None), slice(212, 218, None))","[303, 286, 215]",1.3.6.1.4.1.14519.5.2.1.6279.6001.272042302501586336192628818865
489,3,1,1,1,5,4,1,5,5,"(slice(334, 343, None), slice(135, 146, None), slice(83, 86, None))","[338, 140, 84]",1.3.6.1.4.1.14519.5.2.1.6279.6001.908250781706513856628130123235
490,6,1,1,3,4,4,1,5,5,"(slice(170, 183, None), slice(138, 151, None), slice(105, 110, None))","[176, 144, 107]",1.3.6.1.4.1.14519.5.2.1.6279.6001.908250781706513856628130123235
491,6,1,3,4,2,3,3,5,4,"(slice(250, 275, None), slice(380, 411, None), slice(198, 203, None))","[262, 395, 200]",1.3.6.1.4.1.14519.5.2.1.6279.6001.908250781706513856628130123235
492,6,1,1,3,4,4,1,3,4,"(slice(333, 346, None), slice(445, 457, None), slice(59, 63, None))","[339, 451, 61]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249450003033735700817635168066
493,6,1,1,2,5,4,1,3,5,"(slice(228, 240, None), slice(445, 456, None), slice(65, 68, None))","[234, 450, 66]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249450003033735700817635168066
494,6,1,1,3,5,4,1,3,5,"(slice(377, 391, None), slice(427, 442, None), slice(73, 76, None))","[384, 434, 74]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249450003033735700817635168066
495,6,1,1,3,5,4,1,4,5,"(slice(253, 270, None), slice(58, 72, None), slice(74, 78, None))","[261, 65, 76]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249450003033735700817635168066
496,6,1,2,2,4,3,1,4,5,"(slice(117, 133, None), slice(349, 367, None), slice(169, 176, None))","[125, 358, 172]",1.3.6.1.4.1.14519.5.2.1.6279.6001.179209990684978588019929720099
497,6,1,2,3,5,4,2,5,4,"(slice(201, 214, None), slice(101, 117, None), slice(254, 263, None))","[207, 109, 258]",1.3.6.1.4.1.14519.5.2.1.6279.6001.179209990684978588019929720099
498,6,1,4,4,4,4,2,5,4,"(slice(284, 309, None), slice(102, 125, None), slice(145, 152, None))","[296, 113, 148]",1.3.6.1.4.1.14519.5.2.1.6279.6001.440226700369921575481834344455
499,4,1,3,2,4,2,1,4,5,"(slice(188, 200, None), slice(101, 118, None), slice(118, 124, None))","[194, 109, 121]",1.3.6.1.4.1.14519.5.2.1.6279.6001.184412674007117333405073397832
500,6,1,2,3,4,2,2,3,5,"(slice(286, 301, None), slice(263, 283, None), slice(225, 229, None))","[293, 273, 227]",1.3.6.1.4.1.14519.5.2.1.6279.6001.618434772073433276874225174904
501,6,1,2,2,4,3,1,4,4,"(slice(294, 310, None), slice(458, 469, None), slice(111, 120, None))","[302, 463, 115]",1.3.6.1.4.1.14519.5.2.1.6279.6001.483655032093002252444764787700
502,6,1,2,3,4,4,2,4,5,"(slice(255, 266, None), slice(414, 427, None), slice(100, 104, None))","[260, 420, 102]",1.3.6.1.4.1.14519.5.2.1.6279.6001.292994770358625142596171316474
503,6,1,2,3,4,4,2,4,4,"(slice(330, 343, None), slice(67, 81, None), slice(240, 251, None))","[336, 74, 245]",1.3.6.1.4.1.14519.5.2.1.6279.6001.799582546798528864710752164515
504,3,1,1,1,5,4,1,4,5,"(slice(178, 190, None), slice(152, 164, None), slice(163, 172, None))","[184, 158, 167]",1.3.6.1.4.1.14519.5.2.1.6279.6001.826812708000318290301835871780
505,6,1,2,3,3,4,3,4,4,"(slice(170, 191, None), slice(165, 185, None), slice(176, 192, None))","[180, 175, 184]",1.3.6.1.4.1.14519.5.2.1.6279.6001.826812708000318290301835871780
506,6,1,1,2,5,4,1,3,5,"(slice(136, 148, None), slice(124, 138, None), slice(173, 189, None))","[142, 131, 181]",1.3.6.1.4.1.14519.5.2.1.6279.6001.119806527488108718706404165837
507,6,1,3,4,4,3,2,5,5,"(slice(294, 326, None), slice(364, 398, None), slice(168, 179, None))","[310, 381, 173]",1.3.6.1.4.1.14519.5.2.1.6279.6001.295462530340364058116953738925
508,6,1,2,3,3,2,1,3,5,"(slice(318, 329, None), slice(349, 360, None), slice(174, 179, None))","[323, 354, 176]",1.3.6.1.4.1.14519.5.2.1.6279.6001.295462530340364058116953738925
509,6,1,2,3,4,4,2,4,4,"(slice(280, 291, None), slice(89, 99, None), slice(97, 100, None))","[285, 94, 98]",1.3.6.1.4.1.14519.5.2.1.6279.6001.262736997975960398949912434623
510,6,1,2,2,3,3,2,2,2,"(slice(186, 195, None), slice(127, 137, None), slice(122, 125, None))","[190, 132, 123]",1.3.6.1.4.1.14519.5.2.1.6279.6001.262736997975960398949912434623
511,6,1,1,4,4,4,1,4,2,"(slice(334, 352, None), slice(369, 390, None), slice(333, 351, None))","[343, 379, 342]",1.3.6.1.4.1.14519.5.2.1.6279.6001.268030488196493755113553009785
512,6,1,2,3,3,4,1,2,2,"(slice(292, 306, None), slice(121, 135, None), slice(257, 263, None))","[299, 128, 260]",1.3.6.1.4.1.14519.5.2.1.6279.6001.264251211689085893915477907261
513,3,1,1,1,5,4,1,4,5,"(slice(323, 335, None), slice(183, 195, None), slice(300, 310, None))","[329, 189, 305]",1.3.6.1.4.1.14519.5.2.1.6279.6001.264251211689085893915477907261
514,6,1,1,3,4,4,2,2,2,"(slice(214, 227, None), slice(153, 165, None), slice(353, 361, None))","[220, 159, 357]",1.3.6.1.4.1.14519.5.2.1.6279.6001.264251211689085893915477907261
515,6,1,1,3,2,4,1,2,2,"(slice(199, 217, None), slice(90, 103, None), slice(247, 252, None))","[208, 96, 249]",1.3.6.1.4.1.14519.5.2.1.6279.6001.207341668080525761926965850679
516,6,1,1,3,3,4,1,2,1,"(slice(211, 222, None), slice(354, 364, None), slice(153, 157, None))","[216, 359, 155]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176638348958425792989125209419
517,6,1,1,3,3,3,1,2,1,"(slice(322, 333, None), slice(400, 410, None), slice(204, 206, None))","[327, 405, 205]",1.3.6.1.4.1.14519.5.2.1.6279.6001.176638348958425792989125209419
518,6,1,2,3,4,3,1,4,4,"(slice(368, 381, None), slice(90, 103, None), slice(119, 122, None))","[374, 96, 120]",1.3.6.1.4.1.14519.5.2.1.6279.6001.295420274214095686326263147663
519,6,1,1,3,3,3,1,3,3,"(slice(358, 370, None), slice(72, 82, None), slice(128, 130, None))","[364, 77, 129]",1.3.6.1.4.1.14519.5.2.1.6279.6001.295420274214095686326263147663
520,6,1,1,3,2,3,1,2,1,"(slice(354, 365, None), slice(94, 108, None), slice(128, 131, None))","[359, 101, 129]",1.3.6.1.4.1.14519.5.2.1.6279.6001.295420274214095686326263147663
521,6,1,2,4,2,3,2,4,3,"(slice(280, 296, None), slice(82, 101, None), slice(142, 145, None))","[288, 91, 143]",1.3.6.1.4.1.14519.5.2.1.6279.6001.295420274214095686326263147663
522,6,1,2,4,3,4,2,4,2,"(slice(340, 360, None), slice(127, 147, None), slice(141, 148, None))","[350, 137, 144]",1.3.6.1.4.1.14519.5.2.1.6279.6001.295420274214095686326263147663
523,6,1,1,3,4,4,2,3,5,"(slice(332, 342, None), slice(123, 134, None), slice(86, 94, None))","[337, 128, 90]",1.3.6.1.4.1.14519.5.2.1.6279.6001.188376349804761988217597754952
524,6,1,1,3,5,5,1,4,5,"(slice(370, 381, None), slice(354, 364, None), slice(121, 127, None))","[375, 359, 124]",1.3.6.1.4.1.14519.5.2.1.6279.6001.188376349804761988217597754952
525,6,1,1,3,5,4,1,4,5,"(slice(310, 320, None), slice(141, 150, None), slice(128, 137, None))","[315, 145, 132]",1.3.6.1.4.1.14519.5.2.1.6279.6001.188376349804761988217597754952
526,6,1,2,4,3,3,2,4,5,"(slice(320, 340, None), slice(176, 198, None), slice(177, 190, None))","[330, 187, 183]",1.3.6.1.4.1.14519.5.2.1.6279.6001.188376349804761988217597754952
527,6,1,2,2,5,4,1,4,5,"(slice(151, 164, None), slice(171, 181, None), slice(113, 118, None))","[157, 176, 115]",1.3.6.1.4.1.14519.5.2.1.6279.6001.237428977311365557972720635401
528,6,1,3,3,5,4,2,3,5,"(slice(214, 226, None), slice(52, 62, None), slice(83, 86, None))","[220, 57, 84]",1.3.6.1.4.1.14519.5.2.1.6279.6001.141430002307216644912805017227
529,6,1,2,3,5,4,1,5,5,"(slice(233, 247, None), slice(463, 474, None), slice(88, 91, None))","[240, 468, 89]",1.3.6.1.4.1.14519.5.2.1.6279.6001.141430002307216644912805017227
530,6,1,2,4,4,4,2,4,5,"(slice(152, 165, None), slice(395, 405, None), slice(107, 110, None))","[158, 400, 108]",1.3.6.1.4.1.14519.5.2.1.6279.6001.141430002307216644912805017227
531,6,1,1,3,4,4,2,4,5,"(slice(318, 333, None), slice(119, 133, None), slice(75, 80, None))","[325, 126, 77]",1.3.6.1.4.1.14519.5.2.1.6279.6001.169128136262002764211589185953
532,6,1,1,3,4,4,1,4,5,"(slice(259, 269, None), slice(442, 452, None), slice(98, 103, None))","[264, 447, 100]",1.3.6.1.4.1.14519.5.2.1.6279.6001.169128136262002764211589185953
533,6,1,2,3,3,4,1,3,2,"(slice(297, 311, None), slice(392, 404, None), slice(164, 170, None))","[304, 398, 167]",1.3.6.1.4.1.14519.5.2.1.6279.6001.169128136262002764211589185953
534,6,1,1,3,2,4,1,2,1,"(slice(342, 350, None), slice(153, 165, None), slice(201, 207, None))","[346, 159, 204]",1.3.6.1.4.1.14519.5.2.1.6279.6001.169128136262002764211589185953
535,6,1,1,2,2,4,1,2,1,"(slice(224, 236, None), slice(327, 337, None), slice(206, 210, None))","[230, 332, 208]",1.3.6.1.4.1.14519.5.2.1.6279.6001.169128136262002764211589185953
536,6,1,1,3,2,3,2,1,1,"(slice(309, 322, None), slice(187, 196, None), slice(211, 214, None))","[315, 191, 212]",1.3.6.1.4.1.14519.5.2.1.6279.6001.169128136262002764211589185953
537,6,1,2,4,1,3,4,5,3,"(slice(274, 310, None), slice(269, 324, None), slice(230, 252, None))","[292, 296, 241]",1.3.6.1.4.1.14519.5.2.1.6279.6001.169128136262002764211589185953
538,4,1,1,1,5,5,1,5,5,"(slice(420, 439, None), slice(143, 159, None), slice(142, 157, None))","[429, 151, 149]",1.3.6.1.4.1.14519.5.2.1.6279.6001.952265563663939823135367733681
539,6,1,3,4,2,3,3,5,5,"(slice(332, 388, None), slice(297, 337, None), slice(104, 125, None))","[360, 317, 114]",1.3.6.1.4.1.14519.5.2.1.6279.6001.897684031374557757145405000951
540,6,1,1,3,3,4,1,3,4,"(slice(362, 376, None), slice(142, 154, None), slice(113, 121, None))","[369, 148, 117]",1.3.6.1.4.1.14519.5.2.1.6279.6001.897684031374557757145405000951
541,6,1,1,4,5,4,2,5,5,"(slice(282, 310, None), slice(115, 137, None), slice(128, 143, None))","[296, 126, 135]",1.3.6.1.4.1.14519.5.2.1.6279.6001.897684031374557757145405000951
542,6,1,1,3,4,3,2,5,5,"(slice(228, 261, None), slice(122, 147, None), slice(133, 148, None))","[244, 134, 140]",1.3.6.1.4.1.14519.5.2.1.6279.6001.897684031374557757145405000951
543,6,1,2,2,3,3,2,3,4,"(slice(290, 306, None), slice(166, 178, None), slice(64, 71, None))","[298, 172, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.693480911433291675609148051914
544,6,1,2,3,4,4,1,5,5,"(slice(191, 206, None), slice(122, 133, None), slice(105, 111, None))","[198, 127, 108]",1.3.6.1.4.1.14519.5.2.1.6279.6001.693480911433291675609148051914
545,6,1,2,3,2,4,1,2,2,"(slice(377, 394, None), slice(96, 110, None), slice(293, 299, None))","[385, 103, 296]",1.3.6.1.4.1.14519.5.2.1.6279.6001.338104567770715523699587505022
546,6,1,2,3,4,4,2,3,5,"(slice(358, 373, None), slice(309, 320, None), slice(440, 450, None))","[365, 314, 445]",1.3.6.1.4.1.14519.5.2.1.6279.6001.338104567770715523699587505022
547,6,1,4,3,5,4,4,3,5,"(slice(200, 211, None), slice(207, 217, None), slice(92, 97, None))","[205, 212, 94]",1.3.6.1.4.1.14519.5.2.1.6279.6001.269075535958871753309238331179
548,6,1,4,4,5,4,4,5,5,"(slice(174, 198, None), slice(218, 241, None), slice(92, 100, None))","[186, 229, 96]",1.3.6.1.4.1.14519.5.2.1.6279.6001.269075535958871753309238331179
549,4,1,4,2,5,5,4,5,5,"(slice(355, 367, None), slice(108, 119, None), slice(98, 102, None))","[361, 113, 100]",1.3.6.1.4.1.14519.5.2.1.6279.6001.269075535958871753309238331179
550,6,1,1,3,5,4,1,4,5,"(slice(353, 363, None), slice(389, 398, None), slice(101, 105, None))","[358, 393, 103]",1.3.6.1.4.1.14519.5.2.1.6279.6001.205523326998654833765855998037
551,6,1,1,3,4,4,1,4,5,"(slice(272, 282, None), slice(422, 432, None), slice(105, 108, None))","[277, 427, 106]",1.3.6.1.4.1.14519.5.2.1.6279.6001.205523326998654833765855998037
552,5,1,1,2,5,5,1,5,5,"(slice(368, 380, None), slice(177, 191, None), slice(40, 49, None))","[374, 184, 44]",1.3.6.1.4.1.14519.5.2.1.6279.6001.305858704835252413616501469037
553,6,1,1,3,4,3,1,4,5,"(slice(320, 340, None), slice(301, 315, None), slice(48, 61, None))","[330, 308, 54]",1.3.6.1.4.1.14519.5.2.1.6279.6001.305858704835252413616501469037
554,6,1,1,3,4,2,1,3,5,"(slice(147, 157, None), slice(157, 173, None), slice(146, 157, None))","[152, 165, 151]",1.3.6.1.4.1.14519.5.2.1.6279.6001.199282854229880908602362094937
555,5,1,1,2,4,4,1,4,5,"(slice(208, 218, None), slice(96, 107, None), slice(130, 134, None))","[213, 101, 132]",1.3.6.1.4.1.14519.5.2.1.6279.6001.217754016294471278921686508169
556,6,1,2,2,2,3,1,4,2,"(slice(326, 336, None), slice(152, 165, None), slice(70, 73, None))","[331, 158, 71]",1.3.6.1.4.1.14519.5.2.1.6279.6001.277445975068759205899107114231
557,6,1,2,2,2,4,1,3,2,"(slice(352, 362, None), slice(166, 177, None), slice(74, 76, None))","[357, 171, 75]",1.3.6.1.4.1.14519.5.2.1.6279.6001.277445975068759205899107114231
558,6,1,1,4,5,4,1,4,5,"(slice(310, 326, None), slice(85, 99, None), slice(53, 57, None))","[318, 92, 55]",1.3.6.1.4.1.14519.5.2.1.6279.6001.312127933722985204808706697221
559,6,1,1,4,4,5,1,4,4,"(slice(268, 284, None), slice(72, 87, None), slice(77, 83, None))","[276, 79, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.312127933722985204808706697221
560,6,1,1,3,4,4,2,4,4,"(slice(253, 265, None), slice(78, 89, None), slice(87, 90, None))","[259, 83, 88]",1.3.6.1.4.1.14519.5.2.1.6279.6001.312127933722985204808706697221
561,6,1,2,3,2,4,2,3,4,"(slice(289, 300, None), slice(111, 125, None), slice(118, 122, None))","[294, 118, 120]",1.3.6.1.4.1.14519.5.2.1.6279.6001.312127933722985204808706697221
562,6,1,3,5,4,3,2,5,4,"(slice(208, 234, None), slice(272, 300, None), slice(128, 135, None))","[221, 286, 131]",1.3.6.1.4.1.14519.5.2.1.6279.6001.312127933722985204808706697221
563,6,1,1,3,5,4,1,3,5,"(slice(391, 402, None), slice(377, 391, None), slice(109, 113, None))","[396, 384, 111]",1.3.6.1.4.1.14519.5.2.1.6279.6001.569096986145782511000054443951
564,6,1,2,3,4,4,2,4,5,"(slice(397, 410, None), slice(170, 185, None), slice(153, 156, None))","[403, 177, 154]",1.3.6.1.4.1.14519.5.2.1.6279.6001.569096986145782511000054443951
565,6,1,1,3,5,4,1,4,5,"(slice(121, 136, None), slice(147, 161, None), slice(140, 144, None))","[128, 154, 142]",1.3.6.1.4.1.14519.5.2.1.6279.6001.254957696184671649675053562027
566,4,1,1,2,5,5,1,4,5,"(slice(263, 274, None), slice(422, 433, None), slice(149, 153, None))","[268, 427, 151]",1.3.6.1.4.1.14519.5.2.1.6279.6001.254957696184671649675053562027
567,6,1,3,4,3,3,3,4,5,"(slice(67, 88, None), slice(137, 161, None), slice(82, 85, None))","[77, 149, 83]",1.3.6.1.4.1.14519.5.2.1.6279.6001.276556509002726404418399209377
568,6,1,2,3,4,3,1,4,5,"(slice(243, 257, None), slice(92, 110, None), slice(95, 98, None))","[250, 101, 96]",1.3.6.1.4.1.14519.5.2.1.6279.6001.276556509002726404418399209377
569,6,1,1,4,5,4,1,5,5,"(slice(149, 193, None), slice(347, 377, None), slice(165, 179, None))","[171, 362, 172]",1.3.6.1.4.1.14519.5.2.1.6279.6001.282512043257574309474415322775
570,6,1,2,3,4,4,1,3,4,"(slice(228, 244, None), slice(106, 122, None), slice(234, 238, None))","[236, 114, 236]",1.3.6.1.4.1.14519.5.2.1.6279.6001.346115813056769250958550383763
571,3,1,2,1,5,4,1,5,5,"(slice(153, 166, None), slice(132, 146, None), slice(131, 142, None))","[159, 139, 136]",1.3.6.1.4.1.14519.5.2.1.6279.6001.267519732763035023633235877753
572,4,1,1,2,5,4,1,4,5,"(slice(98, 107, None), slice(127, 138, None), slice(142, 149, None))","[102, 132, 145]",1.3.6.1.4.1.14519.5.2.1.6279.6001.267519732763035023633235877753
573,3,1,2,1,5,3,1,5,5,"(slice(172, 183, None), slice(283, 292, None), slice(348, 356, None))","[177, 287, 352]",1.3.6.1.4.1.14519.5.2.1.6279.6001.267519732763035023633235877753
574,6,1,2,3,2,4,1,2,2,"(slice(198, 216, None), slice(159, 176, None), slice(323, 333, None))","[207, 167, 328]",1.3.6.1.4.1.14519.5.2.1.6279.6001.392861216720727557882279374324
575,6,1,2,3,4,4,1,4,5,"(slice(370, 385, None), slice(379, 400, None), slice(153, 167, None))","[377, 389, 160]",1.3.6.1.4.1.14519.5.2.1.6279.6001.323899724653546164058849558431
576,5,1,1,3,5,4,1,4,5,"(slice(373, 384, None), slice(386, 397, None), slice(183, 192, None))","[378, 391, 187]",1.3.6.1.4.1.14519.5.2.1.6279.6001.323899724653546164058849558431
577,6,1,1,3,5,4,1,5,5,"(slice(413, 421, None), slice(184, 193, None), slice(111, 116, None))","[417, 188, 113]",1.3.6.1.4.1.14519.5.2.1.6279.6001.168985655485163461062675655739
578,4,1,2,2,4,3,2,5,5,"(slice(353, 369, None), slice(369, 379, None), slice(159, 164, None))","[361, 374, 161]",1.3.6.1.4.1.14519.5.2.1.6279.6001.168985655485163461062675655739
579,6,1,1,3,4,4,1,4,4,"(slice(259, 268, None), slice(119, 129, None), slice(167, 169, None))","[263, 124, 168]",1.3.6.1.4.1.14519.5.2.1.6279.6001.168985655485163461062675655739
580,6,1,1,3,5,4,1,5,5,"(slice(351, 360, None), slice(154, 164, None), slice(185, 192, None))","[355, 159, 188]",1.3.6.1.4.1.14519.5.2.1.6279.6001.168985655485163461062675655739
581,6,1,2,3,3,4,2,4,4,"(slice(135, 156, None), slice(149, 170, None), slice(227, 237, None))","[145, 159, 232]",1.3.6.1.4.1.14519.5.2.1.6279.6001.259543921154154401875872845498
582,6,1,2,3,4,4,1,4,5,"(slice(159, 178, None), slice(142, 156, None), slice(238, 246, None))","[168, 149, 242]",1.3.6.1.4.1.14519.5.2.1.6279.6001.259543921154154401875872845498
583,6,1,1,2,4,4,1,4,5,"(slice(189, 204, None), slice(149, 165, None), slice(253, 261, None))","[196, 157, 257]",1.3.6.1.4.1.14519.5.2.1.6279.6001.259543921154154401875872845498
584,4,1,2,2,4,4,1,4,5,"(slice(346, 361, None), slice(196, 212, None), slice(157, 171, None))","[353, 204, 164]",1.3.6.1.4.1.14519.5.2.1.6279.6001.557875302364105947813979213632
585,4,1,1,1,5,4,1,4,5,"(slice(334, 346, None), slice(178, 192, None), slice(209, 220, None))","[340, 185, 214]",1.3.6.1.4.1.14519.5.2.1.6279.6001.557875302364105947813979213632
586,4,1,1,1,5,4,2,4,5,"(slice(318, 332, None), slice(388, 402, None), slice(297, 309, None))","[325, 395, 303]",1.3.6.1.4.1.14519.5.2.1.6279.6001.557875302364105947813979213632
587,6,1,1,3,5,4,1,4,5,"(slice(258, 272, None), slice(359, 373, None), slice(519, 532, None))","[265, 366, 525]",1.3.6.1.4.1.14519.5.2.1.6279.6001.126264578931778258890371755354
588,6,1,2,3,3,4,2,4,4,"(slice(130, 159, None), slice(129, 153, None), slice(223, 241, None))","[144, 141, 232]",1.3.6.1.4.1.14519.5.2.1.6279.6001.297433269262659217151107535012
589,3,1,2,1,4,4,1,5,5,"(slice(145, 170, None), slice(294, 314, None), slice(334, 350, None))","[157, 304, 342]",1.3.6.1.4.1.14519.5.2.1.6279.6001.297433269262659217151107535012
590,4,1,2,2,4,4,1,5,5,"(slice(338, 352, None), slice(359, 371, None), slice(166, 176, None))","[345, 365, 171]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202464973819273687476049035824
591,6,1,2,3,5,4,1,4,5,"(slice(379, 401, None), slice(326, 344, None), slice(265, 270, None))","[390, 335, 267]",1.3.6.1.4.1.14519.5.2.1.6279.6001.394470743585708729682444806008
592,6,1,1,3,2,4,1,2,4,"(slice(331, 345, None), slice(401, 416, None), slice(56, 59, None))","[338, 408, 57]",1.3.6.1.4.1.14519.5.2.1.6279.6001.109002525524522225658609808059
593,6,1,2,5,4,3,2,5,5,"(slice(268, 305, None), slice(412, 443, None), slice(64, 73, None))","[286, 427, 68]",1.3.6.1.4.1.14519.5.2.1.6279.6001.109002525524522225658609808059
594,6,1,2,3,4,4,1,4,5,"(slice(257, 271, None), slice(63, 75, None), slice(211, 217, None))","[264, 69, 214]",1.3.6.1.4.1.14519.5.2.1.6279.6001.173101104804533997398137418032
595,6,1,2,3,4,4,2,4,5,"(slice(348, 362, None), slice(389, 404, None), slice(218, 228, None))","[355, 396, 223]",1.3.6.1.4.1.14519.5.2.1.6279.6001.173101104804533997398137418032
596,6,1,2,3,4,4,2,4,4,"(slice(290, 305, None), slice(409, 424, None), slice(266, 271, None))","[297, 416, 268]",1.3.6.1.4.1.14519.5.2.1.6279.6001.173101104804533997398137418032
597,6,1,1,2,4,2,2,3,5,"(slice(361, 376, None), slice(80, 94, None), slice(135, 139, None))","[368, 87, 137]",1.3.6.1.4.1.14519.5.2.1.6279.6001.215640837032688688030770057224
598,6,1,1,3,4,4,2,4,5,"(slice(120, 135, None), slice(360, 378, None), slice(136, 139, None))","[127, 369, 137]",1.3.6.1.4.1.14519.5.2.1.6279.6001.215640837032688688030770057224
599,6,1,1,3,4,4,2,4,5,"(slice(328, 345, None), slice(375, 392, None), slice(156, 162, None))","[336, 383, 159]",1.3.6.1.4.1.14519.5.2.1.6279.6001.215640837032688688030770057224
600,6,1,2,3,4,3,2,5,5,"(slice(221, 238, None), slice(418, 437, None), slice(163, 169, None))","[229, 427, 166]",1.3.6.1.4.1.14519.5.2.1.6279.6001.215640837032688688030770057224
601,6,1,4,4,3,3,4,5,5,"(slice(277, 305, None), slice(396, 425, None), slice(167, 175, None))","[291, 410, 171]",1.3.6.1.4.1.14519.5.2.1.6279.6001.215640837032688688030770057224
602,6,1,2,3,4,4,2,4,4,"(slice(345, 362, None), slice(385, 402, None), slice(180, 185, None))","[353, 393, 182]",1.3.6.1.4.1.14519.5.2.1.6279.6001.215640837032688688030770057224
603,6,1,2,3,5,4,1,4,5,"(slice(362, 381, None), slice(209, 227, None), slice(110, 116, None))","[371, 218, 113]",1.3.6.1.4.1.14519.5.2.1.6279.6001.321465552859463184018938648244
604,6,1,2,3,4,4,2,4,5,"(slice(238, 253, None), slice(59, 77, None), slice(142, 149, None))","[245, 68, 145]",1.3.6.1.4.1.14519.5.2.1.6279.6001.230416590143922549745658357505
605,6,1,2,3,3,4,1,2,3,"(slice(260, 273, None), slice(109, 121, None), slice(281, 286, None))","[266, 115, 283]",1.3.6.1.4.1.14519.5.2.1.6279.6001.230416590143922549745658357505
606,6,1,2,4,4,3,1,5,5,"(slice(310, 327, None), slice(326, 347, None), slice(54, 60, None))","[318, 336, 57]",1.3.6.1.4.1.14519.5.2.1.6279.6001.241083615484551649610616348856
607,5,1,1,2,5,5,1,4,5,"(slice(272, 282, None), slice(417, 427, None), slice(62, 67, None))","[277, 422, 64]",1.3.6.1.4.1.14519.5.2.1.6279.6001.259123825760999546551970425757
608,6,1,2,3,4,4,2,3,5,"(slice(246, 256, None), slice(196, 206, None), slice(150, 154, None))","[251, 201, 152]",1.3.6.1.4.1.14519.5.2.1.6279.6001.259123825760999546551970425757
609,6,1,2,4,4,4,2,4,5,"(slice(253, 266, None), slice(205, 220, None), slice(154, 160, None))","[259, 212, 157]",1.3.6.1.4.1.14519.5.2.1.6279.6001.259123825760999546551970425757
610,6,1,1,2,4,4,1,3,5,"(slice(287, 298, None), slice(190, 201, None), slice(195, 200, None))","[292, 195, 197]",1.3.6.1.4.1.14519.5.2.1.6279.6001.259123825760999546551970425757
611,5,1,4,4,3,3,4,5,4,"(slice(302, 363, None), slice(194, 245, None), slice(200, 220, None))","[332, 219, 210]",1.3.6.1.4.1.14519.5.2.1.6279.6001.259123825760999546551970425757
612,4,1,2,1,5,4,2,5,5,"(slice(174, 190, None), slice(450, 464, None), slice(126, 140, None))","[182, 457, 133]",1.3.6.1.4.1.14519.5.2.1.6279.6001.179162671133894061547290922949
613,6,1,1,3,4,4,2,5,4,"(slice(212, 226, None), slice(63, 74, None), slice(159, 167, None))","[219, 68, 163]",1.3.6.1.4.1.14519.5.2.1.6279.6001.179162671133894061547290922949
614,4,1,1,1,5,5,1,5,5,"(slice(182, 190, None), slice(179, 188, None), slice(244, 249, None))","[186, 183, 246]",1.3.6.1.4.1.14519.5.2.1.6279.6001.179162671133894061547290922949
615,6,1,2,4,3,3,2,3,4,"(slice(271, 283, None), slice(435, 448, None), slice(96, 100, None))","[277, 441, 98]",1.3.6.1.4.1.14519.5.2.1.6279.6001.319066480138812986026181758474
616,6,1,2,3,3,3,1,3,4,"(slice(364, 384, None), slice(157, 171, None), slice(116, 121, None))","[374, 164, 118]",1.3.6.1.4.1.14519.5.2.1.6279.6001.319066480138812986026181758474
617,6,1,1,3,4,4,2,4,5,"(slice(136, 150, None), slice(149, 161, None), slice(211, 217, None))","[143, 155, 214]",1.3.6.1.4.1.14519.5.2.1.6279.6001.362762275895885013176610377950
618,6,1,2,3,4,4,1,4,4,"(slice(114, 126, None), slice(158, 172, None), slice(98, 102, None))","[120, 165, 100]",1.3.6.1.4.1.14519.5.2.1.6279.6001.183184435049555024219115904825
619,4,1,2,2,5,4,1,4,5,"(slice(229, 240, None), slice(346, 357, None), slice(209, 214, None))","[234, 351, 211]",1.3.6.1.4.1.14519.5.2.1.6279.6001.183184435049555024219115904825
620,6,1,3,4,4,3,3,5,5,"(slice(297, 324, None), slice(94, 128, None), slice(282, 301, None))","[310, 111, 291]",1.3.6.1.4.1.14519.5.2.1.6279.6001.922852847124879997825997808179
621,6,1,2,2,4,3,1,4,5,"(slice(182, 203, None), slice(193, 210, None), slice(330, 348, None))","[192, 201, 339]",1.3.6.1.4.1.14519.5.2.1.6279.6001.922852847124879997825997808179
622,6,1,2,2,4,4,1,3,5,"(slice(273, 288, None), slice(484, 496, None), slice(135, 149, None))","[280, 490, 142]",1.3.6.1.4.1.14519.5.2.1.6279.6001.148229375703208214308676934766
623,3,1,2,1,5,4,2,4,5,"(slice(247, 261, None), slice(367, 381, None), slice(354, 363, None))","[254, 374, 358]",1.3.6.1.4.1.14519.5.2.1.6279.6001.148229375703208214308676934766
624,6,1,4,4,2,3,2,4,2,"(slice(236, 281, None), slice(129, 168, None), slice(199, 212, None))","[258, 148, 205]",1.3.6.1.4.1.14519.5.2.1.6279.6001.558286136379689377915919180358
625,4,1,1,1,5,4,1,4,5,"(slice(159, 170, None), slice(393, 405, None), slice(65, 70, None))","[164, 399, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.275007193025729362844652516689
626,6,1,3,5,5,4,1,5,5,"(slice(200, 251, None), slice(190, 225, None), slice(65, 84, None))","[225, 207, 74]",1.3.6.1.4.1.14519.5.2.1.6279.6001.275007193025729362844652516689
627,6,1,2,4,1,4,2,2,1,"(slice(201, 240, None), slice(77, 121, None), slice(74, 86, None))","[220, 99, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.275007193025729362844652516689
628,6,1,2,2,4,3,1,3,5,"(slice(315, 328, None), slice(398, 411, None), slice(75, 80, None))","[321, 404, 77]",1.3.6.1.4.1.14519.5.2.1.6279.6001.303421828981831854739626597495
629,5,1,3,3,4,4,2,5,5,"(slice(365, 403, None), slice(354, 386, None), slice(112, 133, None))","[384, 370, 122]",1.3.6.1.4.1.14519.5.2.1.6279.6001.258220324170977900491673635112
630,6,1,1,3,4,4,1,3,5,"(slice(254, 265, None), slice(419, 430, None), slice(277, 285, None))","[259, 424, 281]",1.3.6.1.4.1.14519.5.2.1.6279.6001.258220324170977900491673635112
631,6,1,2,4,4,3,3,5,5,"(slice(298, 341, None), slice(92, 143, None), slice(162, 180, None))","[319, 117, 171]",1.3.6.1.4.1.14519.5.2.1.6279.6001.625270601160880745954773142570
632,5,1,1,2,5,4,1,3,5,"(slice(217, 227, None), slice(37, 46, None), slice(85, 90, None))","[222, 41, 87]",1.3.6.1.4.1.14519.5.2.1.6279.6001.994459772950022352718462251777
633,6,1,1,2,5,4,1,3,5,"(slice(162, 170, None), slice(109, 117, None), slice(124, 129, None))","[166, 113, 126]",1.3.6.1.4.1.14519.5.2.1.6279.6001.994459772950022352718462251777
634,6,1,1,2,4,3,1,3,3,"(slice(329, 339, None), slice(187, 197, None), slice(199, 202, None))","[334, 192, 200]",1.3.6.1.4.1.14519.5.2.1.6279.6001.994459772950022352718462251777
635,6,1,2,3,2,4,2,3,2,"(slice(351, 374, None), slice(187, 211, None), slice(102, 112, None))","[362, 199, 107]",1.3.6.1.4.1.14519.5.2.1.6279.6001.250397690690072950000431855143
636,6,1,1,4,4,3,1,4,5,"(slice(168, 187, None), slice(356, 375, None), slice(105, 113, None))","[177, 365, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.250397690690072950000431855143
637,6,1,2,3,5,4,1,4,5,"(slice(235, 248, None), slice(393, 407, None), slice(117, 125, None))","[241, 400, 121]",1.3.6.1.4.1.14519.5.2.1.6279.6001.250397690690072950000431855143
638,6,1,1,2,5,3,2,4,5,"(slice(180, 194, None), slice(154, 170, None), slice(126, 131, None))","[187, 162, 128]",1.3.6.1.4.1.14519.5.2.1.6279.6001.250397690690072950000431855143
639,6,1,2,4,5,4,1,5,5,"(slice(202, 221, None), slice(357, 373, None), slice(131, 138, None))","[211, 365, 134]",1.3.6.1.4.1.14519.5.2.1.6279.6001.250397690690072950000431855143
640,6,1,2,3,4,4,1,4,5,"(slice(243, 254, None), slice(385, 396, None), slice(145, 149, None))","[248, 390, 147]",1.3.6.1.4.1.14519.5.2.1.6279.6001.250397690690072950000431855143
641,4,1,1,2,5,3,1,4,5,"(slice(391, 403, None), slice(149, 163, None), slice(109, 115, None))","[397, 156, 112]",1.3.6.1.4.1.14519.5.2.1.6279.6001.478062284228419671253422844986
642,6,1,1,3,4,4,1,4,4,"(slice(147, 157, None), slice(91, 104, None), slice(143, 150, None))","[152, 97, 146]",1.3.6.1.4.1.14519.5.2.1.6279.6001.188385286346390202873004762827
643,6,1,2,3,4,3,2,4,3,"(slice(296, 312, None), slice(93, 109, None), slice(56, 58, None))","[304, 101, 57]",1.3.6.1.4.1.14519.5.2.1.6279.6001.979083010707182900091062408058
644,6,1,1,2,4,4,1,4,5,"(slice(257, 274, None), slice(406, 426, None), slice(79, 84, None))","[265, 416, 81]",1.3.6.1.4.1.14519.5.2.1.6279.6001.107351566259572521472765997306
645,6,1,2,2,4,4,1,3,5,"(slice(445, 455, None), slice(164, 176, None), slice(94, 99, None))","[450, 170, 96]",1.3.6.1.4.1.14519.5.2.1.6279.6001.107351566259572521472765997306
646,5,1,2,2,4,3,1,4,5,"(slice(265, 276, None), slice(441, 457, None), slice(105, 113, None))","[270, 449, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.107351566259572521472765997306
647,6,1,2,4,3,3,2,4,4,"(slice(415, 443, None), slice(122, 145, None), slice(109, 118, None))","[429, 133, 113]",1.3.6.1.4.1.14519.5.2.1.6279.6001.107351566259572521472765997306
648,6,1,2,3,4,4,1,4,5,"(slice(309, 328, None), slice(61, 80, None), slice(114, 122, None))","[318, 70, 118]",1.3.6.1.4.1.14519.5.2.1.6279.6001.107351566259572521472765997306
649,6,1,1,3,4,3,1,4,5,"(slice(416, 432, None), slice(373, 392, None), slice(123, 129, None))","[424, 382, 126]",1.3.6.1.4.1.14519.5.2.1.6279.6001.107351566259572521472765997306
650,4,1,1,1,5,4,1,5,5,"(slice(215, 231, None), slice(153, 166, None), slice(107, 112, None))","[223, 159, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.114249388265341701207347458535
651,6,1,1,3,4,5,1,3,5,"(slice(323, 333, None), slice(375, 387, None), slice(166, 172, None))","[328, 381, 169]",1.3.6.1.4.1.14519.5.2.1.6279.6001.114249388265341701207347458535
652,6,1,2,3,4,4,1,3,5,"(slice(338, 348, None), slice(199, 214, None), slice(192, 196, None))","[343, 206, 194]",1.3.6.1.4.1.14519.5.2.1.6279.6001.114249388265341701207347458535
653,4,1,2,2,4,3,2,4,4,"(slice(390, 410, None), slice(143, 156, None), slice(106, 112, None))","[400, 149, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.274052674198758621258447180130
654,6,1,2,3,4,4,2,4,5,"(slice(291, 308, None), slice(134, 151, None), slice(155, 162, None))","[299, 142, 158]",1.3.6.1.4.1.14519.5.2.1.6279.6001.274052674198758621258447180130
655,6,1,1,3,4,4,1,4,5,"(slice(219, 239, None), slice(10, 28, None), slice(44, 61, None))","[229, 19, 52]",1.3.6.1.4.1.14519.5.2.1.6279.6001.300270516469599170290456821227
656,4,1,1,2,4,3,1,4,5,"(slice(139, 154, None), slice(115, 132, None), slice(198, 204, None))","[146, 123, 201]",1.3.6.1.4.1.14519.5.2.1.6279.6001.300270516469599170290456821227
657,6,1,1,2,4,4,1,3,5,"(slice(345, 356, None), slice(287, 298, None), slice(387, 394, None))","[350, 292, 390]",1.3.6.1.4.1.14519.5.2.1.6279.6001.193964947698259739624715468431
658,6,1,3,4,4,4,2,5,5,"(slice(352, 377, None), slice(149, 178, None), slice(39, 45, None))","[364, 163, 42]",1.3.6.1.4.1.14519.5.2.1.6279.6001.290135156874098366424871975734
659,6,1,4,5,3,4,2,5,5,"(slice(380, 411, None), slice(151, 192, None), slice(63, 72, None))","[395, 171, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.290135156874098366424871975734
660,6,1,2,3,3,4,2,4,5,"(slice(282, 296, None), slice(350, 360, None), slice(75, 78, None))","[289, 355, 76]",1.3.6.1.4.1.14519.5.2.1.6279.6001.290135156874098366424871975734
661,6,1,2,3,3,4,2,4,5,"(slice(284, 302, None), slice(378, 394, None), slice(75, 79, None))","[293, 386, 77]",1.3.6.1.4.1.14519.5.2.1.6279.6001.290135156874098366424871975734
662,6,1,2,4,4,3,2,5,5,"(slice(336, 368, None), slice(360, 379, None), slice(92, 98, None))","[352, 369, 95]",1.3.6.1.4.1.14519.5.2.1.6279.6001.290135156874098366424871975734
663,6,1,2,4,4,4,2,5,5,"(slice(219, 255, None), slice(346, 370, None), slice(346, 377, None))","[237, 358, 361]",1.3.6.1.4.1.14519.5.2.1.6279.6001.247816269490470394602288565775
664,6,1,1,3,4,4,1,3,4,"(slice(367, 378, None), slice(179, 190, None), slice(506, 519, None))","[372, 184, 512]",1.3.6.1.4.1.14519.5.2.1.6279.6001.247816269490470394602288565775
665,3,1,1,1,5,4,1,5,5,"(slice(355, 364, None), slice(152, 163, None), slice(89, 96, None))","[359, 157, 92]",1.3.6.1.4.1.14519.5.2.1.6279.6001.187966156856911682643615997798
666,3,1,1,1,5,5,1,5,5,"(slice(196, 207, None), slice(431, 442, None), slice(154, 167, None))","[201, 436, 160]",1.3.6.1.4.1.14519.5.2.1.6279.6001.187966156856911682643615997798
667,3,1,2,1,5,2,2,4,5,"(slice(227, 238, None), slice(371, 381, None), slice(313, 319, None))","[232, 376, 316]",1.3.6.1.4.1.14519.5.2.1.6279.6001.187966156856911682643615997798
668,5,1,2,3,5,4,2,5,5,"(slice(409, 429, None), slice(184, 203, None), slice(307, 327, None))","[419, 193, 317]",1.3.6.1.4.1.14519.5.2.1.6279.6001.187966156856911682643615997798
669,6,1,2,3,5,3,1,4,5,"(slice(394, 405, None), slice(345, 355, None), slice(124, 128, None))","[399, 350, 126]",1.3.6.1.4.1.14519.5.2.1.6279.6001.443400977949406454649939526179
670,3,1,2,1,5,4,1,5,5,"(slice(241, 252, None), slice(134, 146, None), slice(163, 167, None))","[246, 140, 165]",1.3.6.1.4.1.14519.5.2.1.6279.6001.443400977949406454649939526179
671,4,1,1,2,5,4,1,4,5,"(slice(365, 375, None), slice(334, 345, None), slice(91, 95, None))","[370, 339, 93]",1.3.6.1.4.1.14519.5.2.1.6279.6001.997611074084993415992563148335
672,6,1,2,4,4,4,2,5,5,"(slice(269, 325, None), slice(201, 251, None), slice(116, 140, None))","[297, 226, 128]",1.3.6.1.4.1.14519.5.2.1.6279.6001.997611074084993415992563148335
673,6,1,1,3,4,3,2,4,5,"(slice(315, 332, None), slice(446, 458, None), slice(78, 84, None))","[323, 452, 81]",1.3.6.1.4.1.14519.5.2.1.6279.6001.504845428620607044098514803031
674,6,1,1,2,4,3,1,4,5,"(slice(150, 161, None), slice(85, 96, None), slice(86, 90, None))","[155, 90, 88]",1.3.6.1.4.1.14519.5.2.1.6279.6001.504845428620607044098514803031
675,6,1,1,3,3,4,1,3,4,"(slice(199, 208, None), slice(66, 76, None), slice(140, 142, None))","[203, 71, 141]",1.3.6.1.4.1.14519.5.2.1.6279.6001.504845428620607044098514803031
676,6,1,2,4,2,4,2,4,1,"(slice(215, 242, None), slice(74, 107, None), slice(138, 145, None))","[228, 90, 141]",1.3.6.1.4.1.14519.5.2.1.6279.6001.265780642925621389994857727416
677,6,1,2,4,2,4,2,2,2,"(slice(249, 264, None), slice(152, 165, None), slice(375, 382, None))","[256, 158, 378]",1.3.6.1.4.1.14519.5.2.1.6279.6001.961063442349005937536597225349
678,6,1,2,3,4,4,2,4,5,"(slice(157, 173, None), slice(128, 148, None), slice(74, 78, None))","[165, 138, 76]",1.3.6.1.4.1.14519.5.2.1.6279.6001.149463915556499304732434215056
679,6,1,1,3,4,5,1,3,5,"(slice(301, 311, None), slice(75, 84, None), slice(86, 91, None))","[306, 79, 88]",1.3.6.1.4.1.14519.5.2.1.6279.6001.900182736599353600185270496549
680,6,1,1,3,5,5,1,3,5,"(slice(316, 326, None), slice(339, 350, None), slice(173, 178, None))","[321, 344, 175]",1.3.6.1.4.1.14519.5.2.1.6279.6001.900182736599353600185270496549
681,6,1,2,3,5,4,2,4,5,"(slice(209, 219, None), slice(118, 131, None), slice(93, 98, None))","[214, 124, 95]",1.3.6.1.4.1.14519.5.2.1.6279.6001.150684298696437181894923266019
682,6,1,2,3,2,3,2,2,3,"(slice(310, 327, None), slice(174, 190, None), slice(146, 152, None))","[318, 182, 149]",1.3.6.1.4.1.14519.5.2.1.6279.6001.314789075871001236641548593165
683,6,1,2,4,3,3,2,4,3,"(slice(308, 332, None), slice(373, 398, None), slice(183, 195, None))","[320, 385, 189]",1.3.6.1.4.1.14519.5.2.1.6279.6001.301462380687644451483231621986
684,6,1,2,4,3,3,2,3,4,"(slice(285, 316, None), slice(345, 372, None), slice(200, 212, None))","[300, 358, 206]",1.3.6.1.4.1.14519.5.2.1.6279.6001.301462380687644451483231621986
685,6,1,1,3,3,4,1,2,2,"(slice(351, 369, None), slice(334, 353, None), slice(233, 242, None))","[360, 343, 237]",1.3.6.1.4.1.14519.5.2.1.6279.6001.301462380687644451483231621986
686,6,1,4,4,3,4,2,5,4,"(slice(345, 380, None), slice(352, 391, None), slice(72, 89, None))","[362, 371, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.404768898286087278137462774930
687,6,1,1,3,2,4,1,4,4,"(slice(397, 416, None), slice(312, 339, None), slice(31, 41, None))","[406, 325, 36]",1.3.6.1.4.1.14519.5.2.1.6279.6001.161855583909753609742728521805
688,6,1,1,3,5,3,1,3,5,"(slice(265, 284, None), slice(426, 439, None), slice(38, 48, None))","[274, 432, 43]",1.3.6.1.4.1.14519.5.2.1.6279.6001.161855583909753609742728521805
689,6,1,1,3,5,4,1,5,5,"(slice(398, 411, None), slice(344, 358, None), slice(76, 85, None))","[404, 351, 80]",1.3.6.1.4.1.14519.5.2.1.6279.6001.161855583909753609742728521805
690,6,1,1,3,4,4,1,5,5,"(slice(359, 375, None), slice(200, 216, None), slice(86, 97, None))","[367, 208, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.161855583909753609742728521805
691,6,1,1,3,5,4,1,5,5,"(slice(373, 392, None), slice(133, 149, None), slice(99, 108, None))","[382, 141, 103]",1.3.6.1.4.1.14519.5.2.1.6279.6001.161855583909753609742728521805
692,6,1,2,4,4,4,4,5,4,"(slice(342, 372, None), slice(192, 223, None), slice(172, 187, None))","[357, 207, 179]",1.3.6.1.4.1.14519.5.2.1.6279.6001.168737928729363683423228050295
693,6,1,3,4,4,4,1,5,5,"(slice(167, 202, None), slice(399, 431, None), slice(141, 154, None))","[184, 415, 147]",1.3.6.1.4.1.14519.5.2.1.6279.6001.187694838527128312070807533473
694,6,1,2,3,4,4,1,2,5,"(slice(210, 222, None), slice(182, 194, None), slice(178, 183, None))","[216, 188, 180]",1.3.6.1.4.1.14519.5.2.1.6279.6001.663019255629770796363333877035
695,5,1,1,2,4,4,2,4,5,"(slice(164, 181, None), slice(151, 170, None), slice(50, 63, None))","[172, 160, 56]",1.3.6.1.4.1.14519.5.2.1.6279.6001.212608679077007918190529579976
696,3,1,1,1,5,4,2,5,5,"(slice(266, 277, None), slice(172, 180, None), slice(127, 131, None))","[271, 176, 129]",1.3.6.1.4.1.14519.5.2.1.6279.6001.212608679077007918190529579976
697,5,1,1,2,4,4,1,3,5,"(slice(368, 380, None), slice(71, 85, None), slice(144, 149, None))","[374, 78, 146]",1.3.6.1.4.1.14519.5.2.1.6279.6001.842317928015463083368074520378
698,6,1,2,4,3,3,3,4,4,"(slice(276, 300, None), slice(120, 144, None), slice(321, 336, None))","[288, 132, 328]",1.3.6.1.4.1.14519.5.2.1.6279.6001.716498695101447665580610403574
699,6,1,3,2,5,4,3,4,5,"(slice(150, 164, None), slice(405, 416, None), slice(329, 336, None))","[157, 410, 332]",1.3.6.1.4.1.14519.5.2.1.6279.6001.168605638657404145360275453085
700,4,1,2,2,5,4,1,5,5,"(slice(205, 225, None), slice(375, 396, None), slice(57, 64, None))","[215, 385, 60]",1.3.6.1.4.1.14519.5.2.1.6279.6001.561458563853929400124470098603
701,6,1,3,3,4,3,1,3,3,"(slice(187, 202, None), slice(83, 98, None), slice(64, 69, None))","[194, 90, 66]",1.3.6.1.4.1.14519.5.2.1.6279.6001.561458563853929400124470098603
702,6,1,2,3,4,4,2,2,4,"(slice(313, 323, None), slice(119, 129, None), slice(105, 111, None))","[318, 124, 108]",1.3.6.1.4.1.14519.5.2.1.6279.6001.275986221854423197884953496664
703,6,1,1,3,4,4,1,3,5,"(slice(262, 271, None), slice(156, 168, None), slice(118, 124, None))","[266, 162, 121]",1.3.6.1.4.1.14519.5.2.1.6279.6001.275986221854423197884953496664
704,5,1,1,2,4,3,1,4,5,"(slice(180, 194, None), slice(422, 434, None), slice(114, 124, None))","[187, 428, 119]",1.3.6.1.4.1.14519.5.2.1.6279.6001.291539125579672469833850180824
705,6,1,2,4,2,4,1,4,2,"(slice(385, 405, None), slice(347, 363, None), slice(135, 139, None))","[395, 355, 137]",1.3.6.1.4.1.14519.5.2.1.6279.6001.283733738239331719775105586296
706,3,1,1,1,5,5,1,5,5,"(slice(339, 349, None), slice(101, 112, None), slice(197, 204, None))","[344, 106, 200]",1.3.6.1.4.1.14519.5.2.1.6279.6001.970264865033574190975654369557
707,6,1,2,3,5,2,1,4,5,"(slice(296, 316, None), slice(422, 437, None), slice(390, 401, None))","[306, 429, 395]",1.3.6.1.4.1.14519.5.2.1.6279.6001.792381786708289670758399079830
708,6,1,2,4,4,4,2,5,5,"(slice(298, 327, None), slice(398, 426, None), slice(411, 440, None))","[312, 412, 425]",1.3.6.1.4.1.14519.5.2.1.6279.6001.792381786708289670758399079830
709,6,1,2,4,5,5,1,5,5,"(slice(253, 290, None), slice(400, 433, None), slice(194, 212, None))","[271, 416, 203]",1.3.6.1.4.1.14519.5.2.1.6279.6001.204802250386343794613980417281
710,6,2,1,3,5,4,1,5,5,"(slice(310, 321, None), slice(93, 108, None), slice(126, 133, None))","[315, 100, 129]",1.3.6.1.4.1.14519.5.2.1.6279.6001.725236073737175770730904408416
711,6,1,1,3,4,4,1,2,4,"(slice(340, 352, None), slice(428, 438, None), slice(70, 74, None))","[346, 433, 72]",1.3.6.1.4.1.14519.5.2.1.6279.6001.154837327827713479309898027966
712,6,1,1,3,4,3,2,4,5,"(slice(172, 186, None), slice(413, 427, None), slice(85, 92, None))","[179, 420, 88]",1.3.6.1.4.1.14519.5.2.1.6279.6001.154837327827713479309898027966
713,6,1,1,3,4,4,1,4,5,"(slice(351, 363, None), slice(410, 420, None), slice(100, 104, None))","[357, 415, 102]",1.3.6.1.4.1.14519.5.2.1.6279.6001.154837327827713479309898027966
714,6,1,1,3,4,3,2,4,4,"(slice(155, 171, None), slice(150, 164, None), slice(112, 116, None))","[163, 157, 114]",1.3.6.1.4.1.14519.5.2.1.6279.6001.154837327827713479309898027966
715,6,1,1,3,4,4,1,4,5,"(slice(372, 384, None), slice(99, 108, None), slice(121, 127, None))","[378, 103, 124]",1.3.6.1.4.1.14519.5.2.1.6279.6001.154837327827713479309898027966
716,6,1,1,3,5,2,2,4,5,"(slice(303, 323, None), slice(133, 154, None), slice(132, 138, None))","[313, 143, 135]",1.3.6.1.4.1.14519.5.2.1.6279.6001.154837327827713479309898027966
717,6,1,2,4,4,3,1,5,4,"(slice(357, 373, None), slice(74, 92, None), slice(54, 57, None))","[365, 83, 55]",1.3.6.1.4.1.14519.5.2.1.6279.6001.270152671889301412052226973069
718,6,1,1,3,4,3,1,3,5,"(slice(211, 225, None), slice(53, 63, None), slice(146, 150, None))","[218, 58, 148]",1.3.6.1.4.1.14519.5.2.1.6279.6001.163994693532965040247348251579
719,6,1,1,4,4,5,1,5,5,"(slice(381, 394, None), slice(126, 140, None), slice(90, 99, None))","[387, 133, 94]",1.3.6.1.4.1.14519.5.2.1.6279.6001.311476128731958142981941696518
720,6,1,3,4,2,3,2,3,2,"(slice(346, 364, None), slice(105, 126, None), slice(367, 375, None))","[355, 115, 371]",1.3.6.1.4.1.14519.5.2.1.6279.6001.129055977637338639741695800950
721,3,1,1,1,5,4,1,5,5,"(slice(393, 402, None), slice(339, 349, None), slice(40, 43, None))","[397, 344, 41]",1.3.6.1.4.1.14519.5.2.1.6279.6001.226889213794065160713547677129
722,6,1,1,3,5,4,1,3,5,"(slice(375, 386, None), slice(142, 155, None), slice(46, 51, None))","[380, 148, 48]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219087313261026510628926082729
723,6,1,2,4,3,3,4,4,4,"(slice(319, 354, None), slice(127, 155, None), slice(207, 223, None))","[336, 141, 215]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219087313261026510628926082729
724,6,1,1,2,3,3,2,4,2,"(slice(263, 276, None), slice(412, 424, None), slice(87, 90, None))","[269, 418, 88]",1.3.6.1.4.1.14519.5.2.1.6279.6001.292057261351416339496913597985
725,6,1,2,2,3,3,1,3,4,"(slice(386, 400, None), slice(117, 133, None), slice(94, 98, None))","[393, 125, 96]",1.3.6.1.4.1.14519.5.2.1.6279.6001.292057261351416339496913597985
726,6,1,1,2,4,4,1,4,4,"(slice(224, 233, None), slice(106, 116, None), slice(105, 108, None))","[228, 111, 106]",1.3.6.1.4.1.14519.5.2.1.6279.6001.292057261351416339496913597985
727,6,1,3,5,4,4,3,5,5,"(slice(279, 308, None), slice(343, 373, None), slice(133, 143, None))","[293, 358, 138]",1.3.6.1.4.1.14519.5.2.1.6279.6001.292057261351416339496913597985
728,6,1,2,2,4,4,1,4,5,"(slice(402, 417, None), slice(180, 193, None), slice(88, 91, None))","[409, 186, 89]",1.3.6.1.4.1.14519.5.2.1.6279.6001.137375498893536422914241295628
729,4,1,2,1,5,4,1,5,5,"(slice(213, 226, None), slice(82, 95, None), slice(158, 164, None))","[219, 88, 161]",1.3.6.1.4.1.14519.5.2.1.6279.6001.156016499715048493339281864474
730,4,1,1,1,5,4,1,5,5,"(slice(259, 269, None), slice(98, 110, None), slice(168, 172, None))","[264, 104, 170]",1.3.6.1.4.1.14519.5.2.1.6279.6001.156016499715048493339281864474
731,6,1,2,3,4,4,1,4,5,"(slice(335, 347, None), slice(141, 152, None), slice(233, 238, None))","[341, 146, 235]",1.3.6.1.4.1.14519.5.2.1.6279.6001.156016499715048493339281864474
732,6,1,1,3,4,4,1,4,5,"(slice(305, 321, None), slice(85, 95, None), slice(141, 144, None))","[313, 90, 142]",1.3.6.1.4.1.14519.5.2.1.6279.6001.121824995088859376862458155637
733,3,1,1,1,5,4,1,5,5,"(slice(350, 360, None), slice(352, 363, None), slice(152, 155, None))","[355, 357, 153]",1.3.6.1.4.1.14519.5.2.1.6279.6001.121824995088859376862458155637
734,6,1,2,4,4,4,1,4,5,"(slice(277, 299, None), slice(161, 180, None), slice(198, 211, None))","[288, 170, 204]",1.3.6.1.4.1.14519.5.2.1.6279.6001.877026508860018521147620598474
735,6,1,1,2,4,5,1,3,4,"(slice(222, 232, None), slice(128, 138, None), slice(72, 74, None))","[227, 133, 73]",1.3.6.1.4.1.14519.5.2.1.6279.6001.313605260055394498989743099991
736,5,1,1,3,5,4,1,4,5,"(slice(238, 259, None), slice(396, 417, None), slice(108, 116, None))","[248, 406, 112]",1.3.6.1.4.1.14519.5.2.1.6279.6001.404457313935200882843898832756
737,6,1,2,3,4,4,1,4,5,"(slice(179, 190, None), slice(173, 183, None), slice(156, 162, None))","[184, 178, 159]",1.3.6.1.4.1.14519.5.2.1.6279.6001.237215747217294006286437405216
738,5,1,2,2,4,3,2,4,5,"(slice(424, 440, None), slice(173, 185, None), slice(253, 260, None))","[432, 179, 256]",1.3.6.1.4.1.14519.5.2.1.6279.6001.124822907934319930841506266464
739,6,1,1,2,3,2,1,3,5,"(slice(420, 432, None), slice(373, 383, None), slice(257, 261, None))","[426, 378, 259]",1.3.6.1.4.1.14519.5.2.1.6279.6001.124822907934319930841506266464
740,5,1,2,2,4,3,2,4,5,"(slice(425, 439, None), slice(162, 176, None), slice(302, 308, None))","[432, 169, 305]",1.3.6.1.4.1.14519.5.2.1.6279.6001.124822907934319930841506266464
741,5,1,2,2,5,2,2,3,5,"(slice(175, 189, None), slice(380, 390, None), slice(416, 424, None))","[182, 385, 420]",1.3.6.1.4.1.14519.5.2.1.6279.6001.124822907934319930841506266464
742,6,1,2,3,3,2,2,3,5,"(slice(321, 332, None), slice(305, 317, None), slice(498, 501, None))","[326, 311, 499]",1.3.6.1.4.1.14519.5.2.1.6279.6001.124822907934319930841506266464
743,6,1,1,3,4,4,1,4,4,"(slice(329, 339, None), slice(430, 441, None), slice(80, 84, None))","[334, 435, 82]",1.3.6.1.4.1.14519.5.2.1.6279.6001.955688628308192728558382581802
744,6,1,2,3,4,3,1,4,5,"(slice(411, 425, None), slice(344, 358, None), slice(157, 163, None))","[418, 351, 160]",1.3.6.1.4.1.14519.5.2.1.6279.6001.955688628308192728558382581802
745,6,1,3,3,3,3,2,3,4,"(slice(391, 405, None), slice(194, 208, None), slice(159, 165, None))","[398, 201, 162]",1.3.6.1.4.1.14519.5.2.1.6279.6001.955688628308192728558382581802
746,6,1,1,2,4,4,1,4,5,"(slice(272, 284, None), slice(82, 93, None), slice(176, 182, None))","[278, 87, 179]",1.3.6.1.4.1.14519.5.2.1.6279.6001.955688628308192728558382581802
747,5,1,1,4,4,4,1,5,5,"(slice(361, 384, None), slice(388, 413, None), slice(205, 220, None))","[372, 400, 212]",1.3.6.1.4.1.14519.5.2.1.6279.6001.955688628308192728558382581802
748,6,1,1,2,4,3,1,4,4,"(slice(260, 273, None), slice(72, 84, None), slice(37, 39, None))","[266, 78, 38]",1.3.6.1.4.1.14519.5.2.1.6279.6001.130765375502800983459674173881
749,6,1,1,2,4,4,1,4,4,"(slice(270, 281, None), slice(422, 433, None), slice(42, 44, None))","[275, 427, 43]",1.3.6.1.4.1.14519.5.2.1.6279.6001.130765375502800983459674173881
750,6,1,1,3,2,3,1,3,2,"(slice(328, 343, None), slice(397, 417, None), slice(100, 109, None))","[335, 407, 104]",1.3.6.1.4.1.14519.5.2.1.6279.6001.213140617640021803112060161074
751,6,1,3,5,3,3,2,5,4,"(slice(169, 205, None), slice(294, 344, None), slice(191, 206, None))","[187, 319, 198]",1.3.6.1.4.1.14519.5.2.1.6279.6001.213140617640021803112060161074
752,6,1,1,2,3,4,1,3,3,"(slice(302, 315, None), slice(380, 392, None), slice(210, 215, None))","[308, 386, 212]",1.3.6.1.4.1.14519.5.2.1.6279.6001.213140617640021803112060161074
753,6,1,2,4,3,4,2,4,3,"(slice(246, 259, None), slice(410, 424, None), slice(252, 263, None))","[252, 417, 257]",1.3.6.1.4.1.14519.5.2.1.6279.6001.245248446973732759194067808002
754,6,1,2,3,3,3,1,4,4,"(slice(352, 363, None), slice(328, 342, None), slice(178, 182, None))","[357, 335, 180]",1.3.6.1.4.1.14519.5.2.1.6279.6001.173556680294801532247454313511
755,6,1,1,3,4,4,1,4,4,"(slice(323, 332, None), slice(389, 399, None), slice(93, 98, None))","[327, 394, 95]",1.3.6.1.4.1.14519.5.2.1.6279.6001.228934821089041845791238006047
756,6,1,1,3,4,3,2,4,5,"(slice(189, 202, None), slice(440, 458, None), slice(65, 72, None))","[195, 449, 68]",1.3.6.1.4.1.14519.5.2.1.6279.6001.797637294244261543517154417124
757,6,1,2,3,4,3,1,4,5,"(slice(151, 163, None), slice(92, 104, None), slice(182, 187, None))","[157, 98, 184]",1.3.6.1.4.1.14519.5.2.1.6279.6001.323859712968543712594665815359
758,6,1,2,3,4,3,2,4,5,"(slice(345, 367, None), slice(132, 145, None), slice(208, 216, None))","[356, 138, 212]",1.3.6.1.4.1.14519.5.2.1.6279.6001.323859712968543712594665815359
759,6,1,2,3,4,4,1,4,5,"(slice(358, 371, None), slice(418, 431, None), slice(66, 70, None))","[364, 424, 68]",1.3.6.1.4.1.14519.5.2.1.6279.6001.939152384493874708850321969356
760,6,1,2,2,4,3,3,4,4,"(slice(352, 371, None), slice(288, 301, None), slice(63, 70, None))","[361, 294, 66]",1.3.6.1.4.1.14519.5.2.1.6279.6001.224465398054769500989828256685
761,6,1,2,2,4,3,1,4,5,"(slice(302, 327, None), slice(53, 81, None), slice(61, 72, None))","[314, 67, 66]",1.3.6.1.4.1.14519.5.2.1.6279.6001.224465398054769500989828256685
762,6,1,2,4,5,4,2,5,5,"(slice(390, 417, None), slice(181, 205, None), slice(89, 106, None))","[403, 193, 97]",1.3.6.1.4.1.14519.5.2.1.6279.6001.224465398054769500989828256685
763,5,1,1,2,5,2,2,4,5,"(slice(374, 389, None), slice(96, 109, None), slice(106, 111, None))","[381, 102, 108]",1.3.6.1.4.1.14519.5.2.1.6279.6001.224465398054769500989828256685
764,5,1,1,2,5,4,1,4,5,"(slice(250, 260, None), slice(410, 420, None), slice(164, 169, None))","[255, 415, 166]",1.3.6.1.4.1.14519.5.2.1.6279.6001.246758220302211646532176593724
765,5,1,1,2,5,4,1,4,5,"(slice(218, 228, None), slice(377, 387, None), slice(227, 231, None))","[223, 382, 229]",1.3.6.1.4.1.14519.5.2.1.6279.6001.246758220302211646532176593724
766,3,1,1,1,5,4,1,4,5,"(slice(225, 236, None), slice(425, 435, None), slice(139, 147, None))","[230, 430, 143]",1.3.6.1.4.1.14519.5.2.1.6279.6001.217697417596902141600884006982
767,6,1,2,3,4,3,2,4,4,"(slice(274, 286, None), slice(91, 107, None), slice(197, 203, None))","[280, 99, 200]",1.3.6.1.4.1.14519.5.2.1.6279.6001.217697417596902141600884006982
768,6,1,1,2,4,4,1,4,5,"(slice(222, 232, None), slice(363, 376, None), slice(227, 233, None))","[227, 369, 230]",1.3.6.1.4.1.14519.5.2.1.6279.6001.217697417596902141600884006982
769,6,1,1,2,4,3,1,3,5,"(slice(159, 172, None), slice(394, 411, None), slice(54, 58, None))","[165, 402, 56]",1.3.6.1.4.1.14519.5.2.1.6279.6001.168833925301530155818375859047
770,6,1,1,2,4,4,1,3,5,"(slice(244, 256, None), slice(440, 447, None), slice(61, 64, None))","[250, 443, 62]",1.3.6.1.4.1.14519.5.2.1.6279.6001.168833925301530155818375859047
771,6,1,1,2,5,5,1,3,5,"(slice(270, 281, None), slice(86, 95, None), slice(76, 80, None))","[275, 90, 78]",1.3.6.1.4.1.14519.5.2.1.6279.6001.168833925301530155818375859047
772,6,1,1,2,4,4,1,2,5,"(slice(360, 369, None), slice(374, 383, None), slice(77, 80, None))","[364, 378, 78]",1.3.6.1.4.1.14519.5.2.1.6279.6001.168833925301530155818375859047
773,6,1,1,2,5,3,1,4,5,"(slice(168, 184, None), slice(85, 97, None), slice(82, 88, None))","[176, 91, 85]",1.3.6.1.4.1.14519.5.2.1.6279.6001.168833925301530155818375859047
774,6,1,4,5,2,3,4,5,5,"(slice(276, 325, None), slice(169, 207, None), slice(395, 427, None))","[300, 188, 411]",1.3.6.1.4.1.14519.5.2.1.6279.6001.110678335949765929063942738609
775,6,1,1,4,5,4,1,4,5,"(slice(230, 246, None), slice(89, 104, None), slice(136, 146, None))","[238, 96, 141]",1.3.6.1.4.1.14519.5.2.1.6279.6001.334517907433161353885866806005
776,4,1,1,1,4,4,1,5,5,"(slice(248, 262, None), slice(102, 116, None), slice(142, 150, None))","[255, 109, 146]",1.3.6.1.4.1.14519.5.2.1.6279.6001.334517907433161353885866806005
777,6,1,3,4,4,3,3,5,5,"(slice(337, 378, None), slice(108, 146, None), slice(191, 209, None))","[357, 127, 200]",1.3.6.1.4.1.14519.5.2.1.6279.6001.334517907433161353885866806005
778,6,1,2,4,3,4,2,5,5,"(slice(324, 349, None), slice(175, 196, None), slice(141, 147, None))","[336, 185, 144]",1.3.6.1.4.1.14519.5.2.1.6279.6001.174692377730646477496286081479
779,6,1,2,3,4,4,1,5,5,"(slice(288, 301, None), slice(54, 65, None), slice(90, 97, None))","[294, 59, 93]",1.3.6.1.4.1.14519.5.2.1.6279.6001.241570579760883349458693655367
780,6,1,2,4,4,3,2,4,5,"(slice(163, 189, None), slice(362, 387, None), slice(411, 423, None))","[176, 374, 417]",1.3.6.1.4.1.14519.5.2.1.6279.6001.301582691063019848479942618641
781,6,1,1,2,5,3,1,3,5,"(slice(222, 233, None), slice(80, 89, None), slice(105, 111, None))","[227, 84, 108]",1.3.6.1.4.1.14519.5.2.1.6279.6001.218476624578721885561483687176
782,3,1,1,1,5,4,1,4,5,"(slice(280, 289, None), slice(381, 393, None), slice(133, 138, None))","[284, 387, 135]",1.3.6.1.4.1.14519.5.2.1.6279.6001.218476624578721885561483687176
783,6,1,2,4,4,4,2,5,5,"(slice(240, 272, None), slice(102, 124, None), slice(172, 190, None))","[256, 113, 181]",1.3.6.1.4.1.14519.5.2.1.6279.6001.188619674701053082195613114069
784,6,1,2,4,5,4,2,5,5,"(slice(317, 334, None), slice(123, 139, None), slice(231, 239, None))","[325, 131, 235]",1.3.6.1.4.1.14519.5.2.1.6279.6001.188619674701053082195613114069
785,6,1,2,3,4,3,1,4,5,"(slice(299, 311, None), slice(170, 186, None), slice(72, 77, None))","[305, 178, 74]",1.3.6.1.4.1.14519.5.2.1.6279.6001.190298296009658115773239776160
786,6,1,2,3,4,4,2,4,5,"(slice(330, 358, None), slice(359, 383, None), slice(56, 64, None))","[344, 371, 60]",1.3.6.1.4.1.14519.5.2.1.6279.6001.167237290696350215427953159586
787,6,1,3,5,2,3,3,5,5,"(slice(218, 261, None), slice(89, 133, None), slice(120, 142, None))","[239, 111, 131]",1.3.6.1.4.1.14519.5.2.1.6279.6001.167237290696350215427953159586
788,6,1,3,4,5,2,4,3,5,"(slice(257, 283, None), slice(385, 411, None), slice(85, 94, None))","[270, 398, 89]",1.3.6.1.4.1.14519.5.2.1.6279.6001.199670099218798685977406484591
789,6,1,2,2,3,3,1,2,2,"(slice(131, 140, None), slice(113, 124, None), slice(88, 94, None))","[135, 118, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.199670099218798685977406484591
790,6,1,2,4,2,3,2,5,4,"(slice(239, 269, None), slice(71, 138, None), slice(133, 154, None))","[254, 104, 143]",1.3.6.1.4.1.14519.5.2.1.6279.6001.226383054119800793308721198594
791,6,1,1,4,5,4,2,5,5,"(slice(288, 306, None), slice(451, 470, None), slice(235, 243, None))","[297, 460, 239]",1.3.6.1.4.1.14519.5.2.1.6279.6001.627998298349675613581885874395
792,6,1,1,2,3,4,1,2,2,"(slice(239, 251, None), slice(109, 120, None), slice(74, 75, None))","[245, 114, 74]",1.3.6.1.4.1.14519.5.2.1.6279.6001.776800177074349870648765614630
793,6,1,1,4,5,4,1,4,5,"(slice(337, 347, None), slice(78, 87, None), slice(85, 89, None))","[342, 82, 87]",1.3.6.1.4.1.14519.5.2.1.6279.6001.299476369290630280560355838785
794,6,1,2,4,5,4,2,4,5,"(slice(193, 205, None), slice(109, 126, None), slice(160, 164, None))","[199, 117, 162]",1.3.6.1.4.1.14519.5.2.1.6279.6001.299476369290630280560355838785
795,6,1,2,3,4,4,2,4,5,"(slice(346, 357, None), slice(101, 114, None), slice(196, 200, None))","[351, 107, 198]",1.3.6.1.4.1.14519.5.2.1.6279.6001.191266041369462391833537519639
796,6,1,2,3,4,4,2,3,5,"(slice(188, 208, None), slice(446, 460, None), slice(49, 64, None))","[198, 453, 56]",1.3.6.1.4.1.14519.5.2.1.6279.6001.281489753704424911132261151767
797,6,1,2,3,3,3,2,3,2,"(slice(367, 388, None), slice(373, 398, None), slice(93, 97, None))","[377, 385, 95]",1.3.6.1.4.1.14519.5.2.1.6279.6001.162901839201654862079549658100
798,6,1,1,3,3,3,1,3,5,"(slice(346, 355, None), slice(415, 424, None), slice(149, 154, None))","[350, 419, 151]",1.3.6.1.4.1.14519.5.2.1.6279.6001.125124219978170516876304987559
799,6,1,1,2,4,3,1,3,5,"(slice(291, 299, None), slice(398, 409, None), slice(188, 192, None))","[295, 403, 190]",1.3.6.1.4.1.14519.5.2.1.6279.6001.125124219978170516876304987559
800,6,1,2,3,2,4,1,3,1,"(slice(289, 312, None), slice(147, 176, None), slice(266, 279, None))","[300, 161, 272]",1.3.6.1.4.1.14519.5.2.1.6279.6001.125124219978170516876304987559
801,6,1,1,2,4,4,1,2,5,"(slice(289, 299, None), slice(457, 469, None), slice(178, 189, None))","[294, 463, 183]",1.3.6.1.4.1.14519.5.2.1.6279.6001.227796349777753378641347819780
802,6,1,1,2,4,4,2,4,5,"(slice(259, 272, None), slice(36, 50, None), slice(196, 209, None))","[265, 43, 202]",1.3.6.1.4.1.14519.5.2.1.6279.6001.227796349777753378641347819780
803,6,1,1,2,4,4,1,4,5,"(slice(155, 168, None), slice(121, 135, None), slice(307, 314, None))","[161, 128, 310]",1.3.6.1.4.1.14519.5.2.1.6279.6001.227796349777753378641347819780
804,6,1,1,2,4,4,1,3,4,"(slice(146, 161, None), slice(140, 152, None), slice(60, 64, None))","[153, 146, 62]",1.3.6.1.4.1.14519.5.2.1.6279.6001.153646219551578201092527860224
805,6,3,2,4,4,4,3,5,3,"(slice(282, 311, None), slice(389, 423, None), slice(53, 61, None))","[296, 406, 57]",1.3.6.1.4.1.14519.5.2.1.6279.6001.272123398257168239653655006815
806,6,1,3,4,4,4,2,5,5,"(slice(234, 268, None), slice(355, 408, None), slice(69, 77, None))","[251, 381, 73]",1.3.6.1.4.1.14519.5.2.1.6279.6001.272123398257168239653655006815
807,6,1,2,4,3,4,2,4,5,"(slice(152, 182, None), slice(202, 228, None), slice(84, 89, None))","[167, 215, 86]",1.3.6.1.4.1.14519.5.2.1.6279.6001.272123398257168239653655006815
808,6,1,1,2,5,2,1,3,5,"(slice(363, 371, None), slice(189, 201, None), slice(126, 128, None))","[367, 195, 127]",1.3.6.1.4.1.14519.5.2.1.6279.6001.550599855064600241623943717588
809,6,1,1,2,4,4,1,3,5,"(slice(118, 130, None), slice(83, 95, None), slice(65, 68, None))","[124, 89, 66]",1.3.6.1.4.1.14519.5.2.1.6279.6001.675543413149938600000570588203
810,6,1,3,5,4,3,3,5,5,"(slice(283, 335, None), slice(379, 455, None), slice(72, 84, None))","[309, 417, 78]",1.3.6.1.4.1.14519.5.2.1.6279.6001.675543413149938600000570588203
811,6,1,1,3,4,4,1,3,5,"(slice(334, 346, None), slice(320, 332, None), slice(94, 98, None))","[340, 326, 96]",1.3.6.1.4.1.14519.5.2.1.6279.6001.142485715518010940961688015191
812,6,1,1,3,4,4,1,3,5,"(slice(187, 199, None), slice(81, 91, None), slice(193, 196, None))","[193, 86, 194]",1.3.6.1.4.1.14519.5.2.1.6279.6001.142485715518010940961688015191
813,6,1,1,2,4,4,1,4,5,"(slice(112, 134, None), slice(106, 124, None), slice(191, 198, None))","[123, 115, 194]",1.3.6.1.4.1.14519.5.2.1.6279.6001.142485715518010940961688015191
814,6,1,1,2,5,4,1,4,5,"(slice(387, 396, None), slice(302, 311, None), slice(295, 302, None))","[391, 306, 298]",1.3.6.1.4.1.14519.5.2.1.6279.6001.141511313712034597336182402384
815,6,1,3,5,3,4,4,5,4,"(slice(169, 215, None), slice(368, 418, None), slice(367, 395, None))","[192, 393, 381]",1.3.6.1.4.1.14519.5.2.1.6279.6001.141511313712034597336182402384
816,6,1,3,3,4,4,1,4,5,"(slice(144, 162, None), slice(154, 174, None), slice(402, 416, None))","[153, 164, 409]",1.3.6.1.4.1.14519.5.2.1.6279.6001.141511313712034597336182402384
817,6,1,2,2,4,4,2,3,4,"(slice(124, 137, None), slice(116, 127, None), slice(102, 104, None))","[130, 121, 103]",1.3.6.1.4.1.14519.5.2.1.6279.6001.229096941293122177107846044795
818,6,1,2,2,4,3,2,4,4,"(slice(145, 167, None), slice(121, 134, None), slice(103, 105, None))","[156, 127, 104]",1.3.6.1.4.1.14519.5.2.1.6279.6001.229096941293122177107846044795
819,6,1,2,3,4,4,2,4,5,"(slice(380, 397, None), slice(149, 167, None), slice(106, 110, None))","[388, 158, 108]",1.3.6.1.4.1.14519.5.2.1.6279.6001.229096941293122177107846044795
820,6,1,2,4,3,3,3,5,4,"(slice(388, 424, None), slice(131, 179, None), slice(108, 120, None))","[406, 155, 114]",1.3.6.1.4.1.14519.5.2.1.6279.6001.229096941293122177107846044795
821,6,1,2,2,4,3,3,3,5,"(slice(130, 143, None), slice(141, 158, None), slice(113, 117, None))","[136, 149, 115]",1.3.6.1.4.1.14519.5.2.1.6279.6001.229096941293122177107846044795
822,6,1,2,4,4,5,2,5,4,"(slice(321, 358, None), slice(360, 396, None), slice(132, 154, None))","[339, 378, 143]",1.3.6.1.4.1.14519.5.2.1.6279.6001.100621383016233746780170740405
823,6,1,2,4,5,4,1,5,5,"(slice(233, 273, None), slice(359, 400, None), slice(218, 244, None))","[253, 379, 231]",1.3.6.1.4.1.14519.5.2.1.6279.6001.100621383016233746780170740405
824,6,1,2,4,5,5,2,5,5,"(slice(275, 313, None), slice(200, 240, None), slice(241, 263, None))","[294, 220, 252]",1.3.6.1.4.1.14519.5.2.1.6279.6001.100621383016233746780170740405
825,6,1,2,4,5,4,2,4,5,"(slice(316, 338, None), slice(164, 189, None), slice(261, 272, None))","[327, 176, 266]",1.3.6.1.4.1.14519.5.2.1.6279.6001.100621383016233746780170740405
826,6,1,1,3,4,4,1,4,5,"(slice(206, 218, None), slice(39, 52, None), slice(76, 80, None))","[212, 45, 78]",1.3.6.1.4.1.14519.5.2.1.6279.6001.100225287222365663678666836860
827,6,1,1,2,4,5,1,4,5,"(slice(151, 160, None), slice(401, 411, None), slice(117, 119, None))","[155, 406, 118]",1.3.6.1.4.1.14519.5.2.1.6279.6001.100225287222365663678666836860
828,6,1,2,3,4,3,1,2,4,"(slice(236, 244, None), slice(99, 110, None), slice(176, 179, None))","[240, 104, 177]",1.3.6.1.4.1.14519.5.2.1.6279.6001.216882370221919561230873289517
829,3,1,1,1,5,3,1,4,5,"(slice(217, 226, None), slice(104, 114, None), slice(199, 202, None))","[221, 109, 200]",1.3.6.1.4.1.14519.5.2.1.6279.6001.216882370221919561230873289517
830,6,1,2,2,4,3,2,4,5,"(slice(124, 138, None), slice(146, 161, None), slice(289, 293, None))","[131, 153, 291]",1.3.6.1.4.1.14519.5.2.1.6279.6001.803987517543436570820681016103
831,4,1,2,1,4,5,1,4,5,"(slice(361, 375, None), slice(323, 337, None), slice(424, 430, None))","[368, 330, 427]",1.3.6.1.4.1.14519.5.2.1.6279.6001.803987517543436570820681016103
832,6,1,2,3,4,4,1,4,5,"(slice(257, 272, None), slice(447, 463, None), slice(197, 206, None))","[264, 455, 201]",1.3.6.1.4.1.14519.5.2.1.6279.6001.534006575256943390479252771547
833,6,1,2,3,1,4,1,3,1,"(slice(350, 376, None), slice(136, 166, None), slice(372, 391, None))","[363, 151, 381]",1.3.6.1.4.1.14519.5.2.1.6279.6001.534006575256943390479252771547
834,6,1,3,5,2,3,4,5,4,"(slice(218, 257, None), slice(342, 382, None), slice(407, 433, None))","[237, 362, 420]",1.3.6.1.4.1.14519.5.2.1.6279.6001.534006575256943390479252771547
835,6,1,1,3,4,3,1,3,5,"(slice(162, 169, None), slice(153, 164, None), slice(159, 162, None))","[165, 158, 160]",1.3.6.1.4.1.14519.5.2.1.6279.6001.337005960787660957389988207064
836,6,1,2,3,4,4,3,4,5,"(slice(148, 164, None), slice(194, 206, None), slice(158, 166, None))","[156, 200, 162]",1.3.6.1.4.1.14519.5.2.1.6279.6001.339882192295517122002429068974
837,6,1,2,2,3,4,1,3,4,"(slice(125, 137, None), slice(407, 419, None), slice(189, 195, None))","[131, 413, 192]",1.3.6.1.4.1.14519.5.2.1.6279.6001.282779922503707013097174625409
838,6,1,2,2,5,4,1,4,5,"(slice(306, 320, None), slice(421, 434, None), slice(285, 294, None))","[313, 427, 289]",1.3.6.1.4.1.14519.5.2.1.6279.6001.282779922503707013097174625409
839,6,1,1,2,5,4,1,4,5,"(slice(242, 256, None), slice(30, 42, None), slice(144, 154, None))","[249, 36, 149]",1.3.6.1.4.1.14519.5.2.1.6279.6001.198016798894102791158686961192
840,6,1,2,3,2,3,1,3,3,"(slice(248, 259, None), slice(428, 441, None), slice(110, 115, None))","[253, 434, 112]",1.3.6.1.4.1.14519.5.2.1.6279.6001.126704785377921920210612476953
841,6,1,2,2,4,3,1,4,5,"(slice(160, 174, None), slice(375, 388, None), slice(367, 380, None))","[167, 381, 373]",1.3.6.1.4.1.14519.5.2.1.6279.6001.196251645377731223510086726530
842,6,1,1,3,4,4,1,5,5,"(slice(260, 275, None), slice(369, 385, None), slice(149, 160, None))","[267, 377, 154]",1.3.6.1.4.1.14519.5.2.1.6279.6001.765459236550358748053283544075
843,6,1,2,4,4,3,2,5,5,"(slice(350, 367, None), slice(316, 337, None), slice(231, 248, None))","[358, 326, 239]",1.3.6.1.4.1.14519.5.2.1.6279.6001.765459236550358748053283544075
844,6,1,2,4,4,4,1,5,5,"(slice(143, 184, None), slice(123, 164, None), slice(44, 63, None))","[163, 143, 53]",1.3.6.1.4.1.14519.5.2.1.6279.6001.624425075947752229712087113746
845,6,1,2,4,4,4,1,5,5,"(slice(388, 407, None), slice(174, 191, None), slice(43, 50, None))","[397, 182, 46]",1.3.6.1.4.1.14519.5.2.1.6279.6001.118140393257625250121502185026
846,6,1,1,3,5,4,1,5,5,"(slice(274, 293, None), slice(31, 46, None), slice(60, 67, None))","[283, 38, 63]",1.3.6.1.4.1.14519.5.2.1.6279.6001.118140393257625250121502185026
847,6,1,1,3,4,3,2,4,5,"(slice(295, 306, None), slice(62, 78, None), slice(90, 93, None))","[300, 70, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.118140393257625250121502185026
848,6,1,2,3,3,2,2,2,3,"(slice(157, 170, None), slice(112, 124, None), slice(135, 137, None))","[163, 118, 136]",1.3.6.1.4.1.14519.5.2.1.6279.6001.230675342744370103160629638194
849,6,1,3,5,4,3,3,5,5,"(slice(306, 359, None), slice(346, 390, None), slice(207, 229, None))","[332, 368, 218]",1.3.6.1.4.1.14519.5.2.1.6279.6001.206097113343059612247503064658
850,6,1,1,2,4,4,1,3,4,"(slice(176, 186, None), slice(444, 453, None), slice(48, 51, None))","[181, 448, 49]",1.3.6.1.4.1.14519.5.2.1.6279.6001.413896555982844732694353377538
851,6,1,1,4,2,4,1,4,2,"(slice(306, 321, None), slice(195, 210, None), slice(62, 65, None))","[313, 202, 63]",1.3.6.1.4.1.14519.5.2.1.6279.6001.413896555982844732694353377538
852,6,1,2,4,3,3,2,5,4,"(slice(277, 308, None), slice(127, 158, None), slice(178, 194, None))","[292, 142, 186]",1.3.6.1.4.1.14519.5.2.1.6279.6001.106719103982792863757268101375
853,6,1,2,2,4,4,1,4,5,"(slice(124, 134, None), slice(177, 189, None), slice(212, 220, None))","[129, 183, 216]",1.3.6.1.4.1.14519.5.2.1.6279.6001.177785764461425908755977367558
854,6,1,2,3,3,4,2,2,1,"(slice(195, 206, None), slice(416, 428, None), slice(129, 131, None))","[200, 422, 130]",1.3.6.1.4.1.14519.5.2.1.6279.6001.534083630500464995109143618896
855,3,1,2,1,5,4,2,4,5,"(slice(245, 258, None), slice(176, 188, None), slice(147, 151, None))","[251, 182, 149]",1.3.6.1.4.1.14519.5.2.1.6279.6001.534083630500464995109143618896
856,3,1,1,1,5,4,2,4,5,"(slice(336, 349, None), slice(109, 120, None), slice(149, 154, None))","[342, 114, 151]",1.3.6.1.4.1.14519.5.2.1.6279.6001.534083630500464995109143618896
857,6,1,2,3,3,4,1,3,3,"(slice(294, 312, None), slice(97, 109, None), slice(225, 233, None))","[303, 103, 229]",1.3.6.1.4.1.14519.5.2.1.6279.6001.238855414831158993232534884296
858,4,1,1,2,5,4,1,5,5,"(slice(250, 283, None), slice(20, 47, None), slice(44, 61, None))","[266, 33, 52]",1.3.6.1.4.1.14519.5.2.1.6279.6001.119304665257760307862874140576
859,6,1,2,3,2,2,2,4,2,"(slice(261, 280, None), slice(92, 109, None), slice(230, 243, None))","[270, 100, 236]",1.3.6.1.4.1.14519.5.2.1.6279.6001.252358625003143649770119512644
860,6,1,3,4,4,4,3,5,4,"(slice(317, 374, None), slice(91, 143, None), slice(80, 101, None))","[345, 117, 90]",1.3.6.1.4.1.14519.5.2.1.6279.6001.137763212752154081977261297097
861,6,1,2,4,4,4,2,5,5,"(slice(297, 321, None), slice(72, 92, None), slice(86, 97, None))","[309, 82, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.137763212752154081977261297097
862,6,1,2,3,4,4,2,4,5,"(slice(298, 314, None), slice(50, 73, None), slice(235, 244, None))","[306, 61, 239]",1.3.6.1.4.1.14519.5.2.1.6279.6001.278010349511857248000260557753
863,6,1,4,4,2,4,2,4,3,"(slice(163, 190, None), slice(114, 141, None), slice(206, 223, None))","[176, 127, 214]",1.3.6.1.4.1.14519.5.2.1.6279.6001.317087518531899043292346860596
864,6,1,2,4,5,4,2,5,5,"(slice(274, 305, None), slice(110, 131, None), slice(367, 390, None))","[289, 120, 378]",1.3.6.1.4.1.14519.5.2.1.6279.6001.313835996725364342034830119490
865,6,1,3,4,2,4,1,4,2,"(slice(150, 172, None), slice(337, 357, None), slice(98, 106, None))","[161, 347, 102]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219349715895470349269596532320
866,6,2,3,4,2,3,2,4,2,"(slice(182, 209, None), slice(352, 385, None), slice(130, 137, None))","[195, 368, 133]",1.3.6.1.4.1.14519.5.2.1.6279.6001.296738183013079390785739615169
867,6,1,2,4,2,3,2,4,2,"(slice(275, 306, None), slice(118, 152, None), slice(146, 158, None))","[290, 135, 152]",1.3.6.1.4.1.14519.5.2.1.6279.6001.296738183013079390785739615169
868,6,1,1,3,4,4,1,4,4,"(slice(117, 127, None), slice(411, 422, None), slice(95, 98, None))","[122, 416, 96]",1.3.6.1.4.1.14519.5.2.1.6279.6001.138904664700896606480369521124
869,3,1,1,1,5,3,1,5,5,"(slice(215, 230, None), slice(108, 118, None), slice(96, 100, None))","[222, 113, 98]",1.3.6.1.4.1.14519.5.2.1.6279.6001.138904664700896606480369521124
870,3,1,2,1,5,3,1,5,5,"(slice(312, 335, None), slice(137, 170, None), slice(109, 119, None))","[323, 153, 114]",1.3.6.1.4.1.14519.5.2.1.6279.6001.138904664700896606480369521124
871,3,1,1,1,5,4,1,5,5,"(slice(332, 342, None), slice(109, 119, None), slice(76, 82, None))","[337, 114, 79]",1.3.6.1.4.1.14519.5.2.1.6279.6001.179730018513720561213088132029
872,6,1,2,3,4,4,1,3,5,"(slice(378, 389, None), slice(329, 340, None), slice(90, 94, None))","[383, 334, 92]",1.3.6.1.4.1.14519.5.2.1.6279.6001.315770913282450940389971401304
873,6,1,2,4,4,4,3,5,5,"(slice(353, 383, None), slice(412, 445, None), slice(167, 180, None))","[368, 428, 173]",1.3.6.1.4.1.14519.5.2.1.6279.6001.140239815496047437552471323962
874,6,1,3,3,3,3,2,4,4,"(slice(210, 226, None), slice(403, 420, None), slice(179, 187, None))","[218, 411, 183]",1.3.6.1.4.1.14519.5.2.1.6279.6001.246225645401227472829175288633
875,6,1,1,3,5,4,1,4,5,"(slice(165, 181, None), slice(75, 93, None), slice(218, 226, None))","[173, 84, 222]",1.3.6.1.4.1.14519.5.2.1.6279.6001.640729228179368154416184318668
876,5,1,2,3,4,4,2,4,5,"(slice(184, 216, None), slice(302, 326, None), slice(243, 253, None))","[200, 314, 248]",1.3.6.1.4.1.14519.5.2.1.6279.6001.211956804948320236390242845468
877,5,1,4,4,4,4,3,5,5,"(slice(199, 246, None), slice(286, 332, None), slice(258, 289, None))","[222, 309, 273]",1.3.6.1.4.1.14519.5.2.1.6279.6001.211956804948320236390242845468
878,6,1,3,4,4,3,3,5,5,"(slice(288, 318, None), slice(128, 153, None), slice(277, 290, None))","[303, 140, 283]",1.3.6.1.4.1.14519.5.2.1.6279.6001.211956804948320236390242845468
879,6,1,3,4,3,3,2,4,5,"(slice(295, 334, None), slice(358, 381, None), slice(66, 83, None))","[314, 369, 74]",1.3.6.1.4.1.14519.5.2.1.6279.6001.235364978775280910367690540811
880,6,1,1,3,5,4,1,3,5,"(slice(266, 277, None), slice(440, 452, None), slice(56, 60, None))","[271, 446, 58]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219254430927834326484477690403
881,6,1,1,3,4,4,1,4,4,"(slice(284, 295, None), slice(86, 96, None), slice(72, 74, None))","[289, 91, 73]",1.3.6.1.4.1.14519.5.2.1.6279.6001.219254430927834326484477690403
882,6,1,2,3,4,4,2,4,4,"(slice(314, 327, None), slice(91, 103, None), slice(33, 36, None))","[320, 97, 34]",1.3.6.1.4.1.14519.5.2.1.6279.6001.338875090785618956575597613546
883,6,1,1,2,4,4,1,3,5,"(slice(320, 329, None), slice(66, 76, None), slice(41, 43, None))","[324, 71, 42]",1.3.6.1.4.1.14519.5.2.1.6279.6001.338875090785618956575597613546
884,6,1,1,2,4,4,1,3,4,"(slice(275, 286, None), slice(69, 78, None), slice(45, 47, None))","[280, 73, 46]",1.3.6.1.4.1.14519.5.2.1.6279.6001.338875090785618956575597613546
885,6,1,1,3,4,4,1,5,4,"(slice(317, 330, None), slice(423, 435, None), slice(48, 51, None))","[323, 429, 49]",1.3.6.1.4.1.14519.5.2.1.6279.6001.338875090785618956575597613546
886,6,1,2,3,4,4,2,3,4,"(slice(360, 369, None), slice(398, 406, None), slice(54, 57, None))","[364, 402, 55]",1.3.6.1.4.1.14519.5.2.1.6279.6001.338875090785618956575597613546
887,6,1,1,2,4,3,1,3,5,"(slice(175, 187, None), slice(112, 127, None), slice(66, 69, None))","[181, 119, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.338875090785618956575597613546
888,6,1,5,3,4,2,5,4,5,"(slice(251, 266, None), slice(56, 70, None), slice(114, 119, None))","[258, 63, 116]",1.3.6.1.4.1.14519.5.2.1.6279.6001.305887072264491016857673607285
889,4,1,1,1,5,4,1,4,5,"(slice(291, 301, None), slice(76, 86, None), slice(101, 104, None))","[296, 81, 102]",1.3.6.1.4.1.14519.5.2.1.6279.6001.334105754605642100456249422350
890,6,1,2,2,4,3,1,4,5,"(slice(356, 368, None), slice(308, 326, None), slice(197, 206, None))","[362, 317, 201]",1.3.6.1.4.1.14519.5.2.1.6279.6001.750792629100457382099842515038
891,6,2,1,5,4,5,1,5,4,"(slice(310, 338, None), slice(109, 141, None), slice(311, 330, None))","[324, 125, 320]",1.3.6.1.4.1.14519.5.2.1.6279.6001.750792629100457382099842515038
892,4,1,1,2,5,4,1,4,5,"(slice(346, 355, None), slice(231, 240, None), slice(56, 61, None))","[350, 235, 58]",1.3.6.1.4.1.14519.5.2.1.6279.6001.117040183261056772902616195387
893,6,1,1,3,5,4,1,4,5,"(slice(380, 388, None), slice(124, 133, None), slice(61, 66, None))","[384, 128, 63]",1.3.6.1.4.1.14519.5.2.1.6279.6001.117040183261056772902616195387
894,6,1,1,4,5,4,1,4,5,"(slice(393, 408, None), slice(373, 389, None), slice(71, 80, None))","[400, 381, 75]",1.3.6.1.4.1.14519.5.2.1.6279.6001.117040183261056772902616195387
895,6,1,1,4,5,5,1,4,5,"(slice(345, 362, None), slice(333, 350, None), slice(113, 124, None))","[353, 341, 118]",1.3.6.1.4.1.14519.5.2.1.6279.6001.117040183261056772902616195387
896,6,1,1,2,5,5,1,4,5,"(slice(393, 402, None), slice(130, 139, None), slice(130, 136, None))","[397, 134, 133]",1.3.6.1.4.1.14519.5.2.1.6279.6001.117040183261056772902616195387
897,6,1,1,2,5,5,2,5,5,"(slice(257, 285, None), slice(90, 118, None), slice(116, 125, None))","[271, 104, 120]",1.3.6.1.4.1.14519.5.2.1.6279.6001.135657246677982059395844827629
898,6,1,2,2,3,4,1,2,2,"(slice(246, 256, None), slice(430, 446, None), slice(405, 410, None))","[251, 438, 407]",1.3.6.1.4.1.14519.5.2.1.6279.6001.265960756233787099041040311282
899,6,1,2,3,4,4,1,3,5,"(slice(211, 221, None), slice(138, 152, None), slice(145, 148, None))","[216, 145, 146]",1.3.6.1.4.1.14519.5.2.1.6279.6001.147325126373007278009743173696
900,6,1,1,3,5,4,1,3,5,"(slice(342, 353, None), slice(218, 229, None), slice(208, 212, None))","[347, 223, 210]",1.3.6.1.4.1.14519.5.2.1.6279.6001.147325126373007278009743173696
901,6,1,1,2,5,4,1,3,5,"(slice(151, 160, None), slice(130, 138, None), slice(49, 52, None))","[155, 134, 50]",1.3.6.1.4.1.14519.5.2.1.6279.6001.300246184547502297539521283806
902,6,1,1,2,4,4,2,3,4,"(slice(246, 255, None), slice(401, 415, None), slice(107, 110, None))","[250, 408, 108]",1.3.6.1.4.1.14519.5.2.1.6279.6001.300246184547502297539521283806
903,6,1,2,4,4,4,3,4,5,"(slice(309, 339, None), slice(222, 244, None), slice(157, 169, None))","[324, 233, 163]",1.3.6.1.4.1.14519.5.2.1.6279.6001.259227883564429312164962953756
904,4,1,1,2,4,4,1,4,5,"(slice(339, 360, None), slice(295, 312, None), slice(86, 101, None))","[349, 303, 93]",1.3.6.1.4.1.14519.5.2.1.6279.6001.275766318636944297772360944907
905,6,1,2,3,4,4,1,4,5,"(slice(137, 167, None), slice(140, 155, None), slice(260, 271, None))","[152, 147, 265]",1.3.6.1.4.1.14519.5.2.1.6279.6001.275766318636944297772360944907
906,6,1,1,4,4,4,2,4,5,"(slice(340, 355, None), slice(114, 128, None), slice(207, 212, None))","[347, 121, 209]",1.3.6.1.4.1.14519.5.2.1.6279.6001.244447966386688625240438849169
907,6,1,4,4,4,4,1,4,5,"(slice(281, 306, None), slice(444, 467, None), slice(79, 86, None))","[293, 455, 82]",1.3.6.1.4.1.14519.5.2.1.6279.6001.592821488053137951302246128864
908,6,1,3,3,3,4,2,3,2,"(slice(285, 301, None), slice(372, 386, None), slice(135, 138, None))","[293, 379, 136]",1.3.6.1.4.1.14519.5.2.1.6279.6001.592821488053137951302246128864
909,6,1,3,4,4,4,3,4,4,"(slice(276, 292, None), slice(189, 210, None), slice(449, 459, None))","[284, 199, 454]",1.3.6.1.4.1.14519.5.2.1.6279.6001.458525794434429386945463560826
910,6,1,1,3,4,4,1,2,4,"(slice(365, 374, None), slice(92, 102, None), slice(108, 111, None))","[369, 97, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.254473943359963613733707320244
911,6,1,2,3,4,4,1,4,5,"(slice(362, 376, None), slice(351, 364, None), slice(183, 187, None))","[369, 357, 185]",1.3.6.1.4.1.14519.5.2.1.6279.6001.254473943359963613733707320244
912,6,1,2,4,4,4,2,5,5,"(slice(320, 353, None), slice(145, 187, None), slice(93, 111, None))","[336, 166, 102]",1.3.6.1.4.1.14519.5.2.1.6279.6001.765930210026773090100532964804
913,6,1,1,2,5,5,1,4,5,"(slice(315, 325, None), slice(355, 367, None), slice(245, 250, None))","[320, 361, 247]",1.3.6.1.4.1.14519.5.2.1.6279.6001.765930210026773090100532964804
914,6,1,2,5,4,4,2,5,5,"(slice(138, 176, None), slice(104, 148, None), slice(49, 70, None))","[157, 126, 59]",1.3.6.1.4.1.14519.5.2.1.6279.6001.107109359065300889765026303943
915,6,1,2,4,4,3,2,5,4,"(slice(335, 363, None), slice(169, 194, None), slice(150, 160, None))","[349, 181, 155]",1.3.6.1.4.1.14519.5.2.1.6279.6001.226152078193253087875725735761
916,6,1,4,5,4,4,4,5,5,"(slice(232, 265, None), slice(357, 395, None), slice(180, 199, None))","[248, 376, 189]",1.3.6.1.4.1.14519.5.2.1.6279.6001.892375496445736188832556446335
917,6,1,1,3,4,5,1,3,5,"(slice(360, 373, None), slice(117, 131, None), slice(134, 140, None))","[366, 124, 137]",1.3.6.1.4.1.14519.5.2.1.6279.6001.329624439086643515259182406526
918,6,1,1,2,4,3,1,4,5,"(slice(354, 374, None), slice(163, 177, None), slice(122, 127, None))","[364, 170, 124]",1.3.6.1.4.1.14519.5.2.1.6279.6001.141345499716190654505508410197
919,4,1,2,2,5,4,2,5,5,"(slice(183, 203, None), slice(97, 125, None), slice(106, 113, None))","[193, 111, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.134519406153127654901640638633
920,5,1,1,2,4,3,1,4,5,"(slice(300, 320, None), slice(211, 222, None), slice(229, 235, None))","[310, 216, 232]",1.3.6.1.4.1.14519.5.2.1.6279.6001.134519406153127654901640638633
921,6,1,2,4,3,4,2,5,5,"(slice(318, 346, None), slice(142, 169, None), slice(212, 234, None))","[332, 155, 223]",1.3.6.1.4.1.14519.5.2.1.6279.6001.227968442353440630355230778531
922,6,1,2,3,4,4,2,4,5,"(slice(318, 349, None), slice(379, 407, None), slice(175, 206, None))","[333, 393, 190]",1.3.6.1.4.1.14519.5.2.1.6279.6001.115386642382564804180764325545
923,6,1,1,3,4,3,1,4,5,"(slice(292, 303, None), slice(399, 412, None), slice(135, 138, None))","[297, 405, 136]",1.3.6.1.4.1.14519.5.2.1.6279.6001.299806338046301317870803017534
924,6,1,1,2,4,3,1,2,5,"(slice(211, 220, None), slice(387, 398, None), slice(162, 165, None))","[215, 392, 163]",1.3.6.1.4.1.14519.5.2.1.6279.6001.299806338046301317870803017534
925,6,1,1,2,4,3,1,4,5,"(slice(364, 378, None), slice(184, 201, None), slice(173, 179, None))","[371, 192, 176]",1.3.6.1.4.1.14519.5.2.1.6279.6001.299806338046301317870803017534
926,6,1,1,3,4,4,1,4,5,"(slice(364, 377, None), slice(163, 175, None), slice(188, 192, None))","[370, 169, 190]",1.3.6.1.4.1.14519.5.2.1.6279.6001.299806338046301317870803017534
927,6,1,1,2,4,2,1,4,4,"(slice(369, 378, None), slice(319, 338, None), slice(190, 194, None))","[373, 328, 192]",1.3.6.1.4.1.14519.5.2.1.6279.6001.299806338046301317870803017534
928,6,1,2,3,4,4,1,3,5,"(slice(385, 400, None), slice(385, 399, None), slice(54, 58, None))","[392, 392, 56]",1.3.6.1.4.1.14519.5.2.1.6279.6001.214800939017429618305208626314
929,6,1,2,3,3,4,2,3,4,"(slice(263, 276, None), slice(460, 472, None), slice(60, 63, None))","[269, 466, 61]",1.3.6.1.4.1.14519.5.2.1.6279.6001.214800939017429618305208626314
930,6,1,1,3,5,5,1,3,5,"(slice(368, 381, None), slice(91, 102, None), slice(75, 79, None))","[374, 96, 77]",1.3.6.1.4.1.14519.5.2.1.6279.6001.214800939017429618305208626314
931,6,1,2,3,4,4,1,4,5,"(slice(398, 413, None), slice(199, 215, None), slice(90, 94, None))","[405, 207, 92]",1.3.6.1.4.1.14519.5.2.1.6279.6001.214800939017429618305208626314
932,6,1,1,2,4,5,1,2,5,"(slice(376, 385, None), slice(122, 132, None), slice(94, 97, None))","[380, 127, 95]",1.3.6.1.4.1.14519.5.2.1.6279.6001.214800939017429618305208626314
933,6,1,2,3,3,3,2,4,4,"(slice(397, 415, None), slice(163, 178, None), slice(108, 112, None))","[406, 170, 110]",1.3.6.1.4.1.14519.5.2.1.6279.6001.214800939017429618305208626314
934,6,1,2,4,4,4,2,5,5,"(slice(193, 236, None), slice(148, 185, None), slice(78, 97, None))","[214, 166, 87]",1.3.6.1.4.1.14519.5.2.1.6279.6001.163901773171373940247829492387
935,6,1,1,3,4,3,1,5,4,"(slice(152, 160, None), slice(90, 99, None), slice(47, 50, None))","[156, 94, 48]",1.3.6.1.4.1.14519.5.2.1.6279.6001.123654356399290048011621921476
936,6,1,1,3,4,4,1,4,4,"(slice(295, 304, None), slice(343, 353, None), slice(72, 75, None))","[299, 348, 73]",1.3.6.1.4.1.14519.5.2.1.6279.6001.123654356399290048011621921476
937,4,1,2,2,5,3,1,5,5,"(slice(197, 210, None), slice(155, 168, None), slice(239, 247, None))","[203, 161, 243]",1.3.6.1.4.1.14519.5.2.1.6279.6001.293757615532132808762625441831
938,6,1,2,3,4,3,2,4,4,"(slice(298, 325, None), slice(128, 154, None), slice(273, 283, None))","[311, 141, 278]",1.3.6.1.4.1.14519.5.2.1.6279.6001.293757615532132808762625441831
939,5,1,1,2,4,4,1,4,5,"(slice(262, 275, None), slice(449, 464, None), slice(50, 55, None))","[268, 456, 52]",1.3.6.1.4.1.14519.5.2.1.6279.6001.217589936421986638139451480826
940,6,1,1,2,4,3,1,4,5,"(slice(317, 333, None), slice(456, 469, None), slice(52, 56, None))","[325, 462, 54]",1.3.6.1.4.1.14519.5.2.1.6279.6001.217589936421986638139451480826
941,5,1,1,3,5,4,1,4,5,"(slice(392, 406, None), slice(373, 386, None), slice(104, 108, None))","[399, 379, 106]",1.3.6.1.4.1.14519.5.2.1.6279.6001.217589936421986638139451480826
942,6,1,3,3,4,3,3,4,5,"(slice(260, 280, None), slice(186, 201, None), slice(215, 221, None))","[270, 193, 218]",1.3.6.1.4.1.14519.5.2.1.6279.6001.217589936421986638139451480826
943,6,2,3,5,3,3,4,5,5,"(slice(250, 291, None), slice(165, 216, None), slice(38, 48, None))","[270, 190, 43]",1.3.6.1.4.1.14519.5.2.1.6279.6001.655242448149322898770987310561
944,6,1,1,3,4,3,1,5,5,"(slice(200, 218, None), slice(66, 86, None), slice(187, 199, None))","[209, 76, 193]",1.3.6.1.4.1.14519.5.2.1.6279.6001.242624386080831911167122628616
945,3,1,1,1,5,3,1,5,5,"(slice(230, 252, None), slice(128, 149, None), slice(238, 250, None))","[241, 138, 244]",1.3.6.1.4.1.14519.5.2.1.6279.6001.242624386080831911167122628616
946,4,1,1,2,5,4,1,5,5,"(slice(350, 364, None), slice(352, 365, None), slice(81, 86, None))","[357, 358, 83]",1.3.6.1.4.1.14519.5.2.1.6279.6001.200725988589959521302320481687
947,6,1,2,3,4,4,1,4,5,"(slice(297, 314, None), slice(320, 337, None), slice(151, 156, None))","[305, 328, 153]",1.3.6.1.4.1.14519.5.2.1.6279.6001.200725988589959521302320481687
948,5,1,1,3,3,4,1,3,3,"(slice(233, 250, None), slice(431, 445, None), slice(187, 192, None))","[241, 438, 189]",1.3.6.1.4.1.14519.5.2.1.6279.6001.466284753932369813717081722101
949,6,1,1,3,4,3,2,5,5,"(slice(290, 307, None), slice(421, 435, None), slice(164, 175, None))","[298, 428, 169]",1.3.6.1.4.1.14519.5.2.1.6279.6001.309955999522338651429118207446
950,4,1,1,1,5,4,1,5,5,"(slice(319, 334, None), slice(129, 145, None), slice(171, 189, None))","[326, 137, 180]",1.3.6.1.4.1.14519.5.2.1.6279.6001.309955999522338651429118207446
951,6,1,1,3,3,4,1,4,4,"(slice(179, 191, None), slice(384, 396, None), slice(233, 239, None))","[185, 390, 236]",1.3.6.1.4.1.14519.5.2.1.6279.6001.309955999522338651429118207446
952,6,1,2,3,4,4,2,4,5,"(slice(281, 295, None), slice(357, 374, None), slice(240, 248, None))","[288, 365, 244]",1.3.6.1.4.1.14519.5.2.1.6279.6001.309955999522338651429118207446
953,6,1,1,3,4,4,1,4,5,"(slice(254, 264, None), slice(394, 404, None), slice(279, 287, None))","[259, 399, 283]",1.3.6.1.4.1.14519.5.2.1.6279.6001.309955999522338651429118207446
954,6,1,2,3,5,4,2,4,5,"(slice(389, 404, None), slice(177, 197, None), slice(285, 298, None))","[396, 187, 291]",1.3.6.1.4.1.14519.5.2.1.6279.6001.309955999522338651429118207446
955,6,1,1,2,3,3,1,4,5,"(slice(274, 286, None), slice(142, 152, None), slice(340, 346, None))","[280, 147, 343]",1.3.6.1.4.1.14519.5.2.1.6279.6001.309955999522338651429118207446
956,6,1,1,2,4,4,1,4,5,"(slice(173, 184, None), slice(111, 128, None), slice(106, 115, None))","[178, 119, 110]",1.3.6.1.4.1.14519.5.2.1.6279.6001.401389720232123950202941034290
957,6,1,1,2,4,3,1,4,5,"(slice(252, 265, None), slice(448, 458, None), slice(125, 136, None))","[258, 453, 130]",1.3.6.1.4.1.14519.5.2.1.6279.6001.247769845138587733933485039556
958,6,1,2,3,4,4,2,4,5,"(slice(169, 183, None), slice(94, 107, None), slice(151, 162, None))","[176, 100, 156]",1.3.6.1.4.1.14519.5.2.1.6279.6001.247769845138587733933485039556
959,6,1,1,2,5,3,1,4,5,"(slice(173, 183, None), slice(77, 87, None), slice(166, 172, None))","[178, 82, 169]",1.3.6.1.4.1.14519.5.2.1.6279.6001.247769845138587733933485039556
960,6,1,2,2,3,3,1,2,4,"(slice(217, 233, None), slice(98, 110, None), slice(92, 95, None))","[225, 104, 93]",1.3.6.1.4.1.14519.5.2.1.6279.6001.300146276266881736689307479986
961,6,1,3,3,3,4,2,4,4,"(slice(332, 354, None), slice(120, 136, None), slice(130, 135, None))","[343, 128, 132]",1.3.6.1.4.1.14519.5.2.1.6279.6001.300146276266881736689307479986
962,6,1,1,2,5,4,1,4,5,"(slice(297, 307, None), slice(348, 358, None), slice(136, 141, None))","[302, 353, 138]",1.3.6.1.4.1.14519.5.2.1.6279.6001.199171741859530285887752432478
963,6,1,1,3,4,4,1,5,5,"(slice(327, 338, None), slice(134, 144, None), slice(137, 143, None))","[332, 139, 140]",1.3.6.1.4.1.14519.5.2.1.6279.6001.199171741859530285887752432478
964,6,1,2,4,2,3,3,5,3,"(slice(161, 191, None), slice(72, 112, None), slice(42, 56, None))","[176, 92, 49]",1.3.6.1.4.1.14519.5.2.1.6279.6001.144883090372691745980459537053
965,6,1,1,2,3,4,1,2,4,"(slice(281, 294, None), slice(119, 137, None), slice(139, 142, None))","[287, 128, 140]",1.3.6.1.4.1.14519.5.2.1.6279.6001.144883090372691745980459537053
966,6,1,1,2,4,4,1,4,5,"(slice(348, 360, None), slice(432, 443, None), slice(121, 130, None))","[354, 437, 125]",1.3.6.1.4.1.14519.5.2.1.6279.6001.194488534645348916700259325236
967,4,1,2,2,5,4,1,4,5,"(slice(287, 300, None), slice(391, 404, None), slice(294, 309, None))","[293, 397, 301]",1.3.6.1.4.1.14519.5.2.1.6279.6001.194488534645348916700259325236
968,6,1,2,3,3,4,1,4,4,"(slice(362, 373, None), slice(107, 119, None), slice(121, 126, None))","[367, 113, 123]",1.3.6.1.4.1.14519.5.2.1.6279.6001.252697338970999211181671881792
969,6,1,1,2,2,4,1,3,1,"(slice(205, 215, None), slice(357, 367, None), slice(109, 111, None))","[210, 362, 110]",1.3.6.1.4.1.14519.5.2.1.6279.6001.674809958213117379592437424616
970,6,1,1,3,3,4,1,4,4,"(slice(289, 298, None), slice(399, 407, None), slice(114, 115, None))","[293, 403, 114]",1.3.6.1.4.1.14519.5.2.1.6279.6001.674809958213117379592437424616
971,6,1,3,3,5,4,2,4,5,"(slice(145, 160, None), slice(109, 126, None), slice(259, 275, None))","[152, 117, 267]",1.3.6.1.4.1.14519.5.2.1.6279.6001.164988920331211858091402361989
972,6,1,4,5,4,3,3,4,4,"(slice(258, 278, None), slice(155, 176, None), slice(289, 309, None))","[268, 165, 299]",1.3.6.1.4.1.14519.5.2.1.6279.6001.222052723822248889877676736332
973,6,1,1,3,3,4,2,3,4,"(slice(268, 279, None), slice(318, 328, None), slice(345, 356, None))","[273, 323, 350]",1.3.6.1.4.1.14519.5.2.1.6279.6001.222052723822248889877676736332
974,3,1,2,1,5,4,1,5,5,"(slice(394, 405, None), slice(104, 115, None), slice(192, 201, None))","[399, 109, 196]",1.3.6.1.4.1.14519.5.2.1.6279.6001.275755514659958628040305922764
975,5,1,2,3,4,3,1,4,5,"(slice(359, 370, None), slice(411, 426, None), slice(199, 208, None))","[364, 418, 203]",1.3.6.1.4.1.14519.5.2.1.6279.6001.275755514659958628040305922764
976,4,1,1,1,5,4,2,4,5,"(slice(172, 188, None), slice(188, 205, None), slice(211, 222, None))","[180, 196, 216]",1.3.6.1.4.1.14519.5.2.1.6279.6001.275755514659958628040305922764
977,6,1,1,3,3,4,2,4,5,"(slice(175, 186, None), slice(125, 137, None), slice(224, 232, None))","[180, 131, 228]",1.3.6.1.4.1.14519.5.2.1.6279.6001.275755514659958628040305922764
978,3,1,3,1,4,4,2,5,5,"(slice(204, 228, None), slice(133, 149, None), slice(258, 269, None))","[216, 141, 263]",1.3.6.1.4.1.14519.5.2.1.6279.6001.275755514659958628040305922764
979,6,1,2,4,5,4,1,5,5,"(slice(387, 417, None), slice(322, 351, None), slice(107, 128, None))","[402, 336, 117]",1.3.6.1.4.1.14519.5.2.1.6279.6001.225227615446398900698431118292
980,6,1,1,2,4,4,1,3,5,"(slice(310, 324, None), slice(456, 468, None), slice(117, 124, None))","[317, 462, 120]",1.3.6.1.4.1.14519.5.2.1.6279.6001.225227615446398900698431118292
981,6,1,1,2,5,4,1,4,5,"(slice(368, 381, None), slice(433, 443, None), slice(171, 179, None))","[374, 438, 175]",1.3.6.1.4.1.14519.5.2.1.6279.6001.225227615446398900698431118292
982,6,1,1,2,4,4,1,5,5,"(slice(328, 342, None), slice(413, 431, None), slice(256, 264, None))","[335, 422, 260]",1.3.6.1.4.1.14519.5.2.1.6279.6001.225227615446398900698431118292
983,6,1,2,2,4,4,2,4,5,"(slice(394, 407, None), slice(140, 153, None), slice(299, 310, None))","[400, 146, 304]",1.3.6.1.4.1.14519.5.2.1.6279.6001.325164338773720548739146851679
984,6,1,2,3,3,4,2,4,2,"(slice(339, 375, None), slice(81, 113, None), slice(365, 392, None))","[357, 97, 378]",1.3.6.1.4.1.14519.5.2.1.6279.6001.325164338773720548739146851679
985,6,1,1,3,4,4,2,4,4,"(slice(253, 264, None), slice(73, 88, None), slice(85, 88, None))","[258, 80, 86]",1.3.6.1.4.1.14519.5.2.1.6279.6001.340158437895922179455019686521
986,6,1,1,3,5,4,1,4,5,"(slice(163, 177, None), slice(118, 133, None), slice(107, 113, None))","[170, 125, 110]",1.3.6.1.4.1.14519.5.2.1.6279.6001.216252660192313507027754194207
987,4,1,2,2,4,4,1,4,4,"(slice(302, 322, None), slice(131, 151, None), slice(133, 142, None))","[312, 141, 137]",1.3.6.1.4.1.14519.5.2.1.6279.6001.216252660192313507027754194207
988,6,1,1,3,4,3,1,3,5,"(slice(333, 342, None), slice(382, 392, None), slice(131, 135, None))","[337, 387, 133]",1.3.6.1.4.1.14519.5.2.1.6279.6001.273525289046256012743471155680
989,6,1,2,3,4,4,2,4,4,"(slice(279, 292, None), slice(52, 63, None), slice(59, 61, None))","[285, 57, 60]",1.3.6.1.4.1.14519.5.2.1.6279.6001.215104063467523905369326175410
990,6,1,2,4,2,3,3,5,4,"(slice(143, 207, None), slice(284, 352, None), slice(130, 169, None))","[175, 318, 149]",1.3.6.1.4.1.14519.5.2.1.6279.6001.121391737347333465796214915391
991,6,1,2,4,3,3,2,4,3,"(slice(332, 346, None), slice(414, 427, None), slice(131, 134, None))","[339, 420, 132]",1.3.6.1.4.1.14519.5.2.1.6279.6001.114195693932194925962391697338
992,6,1,1,2,5,5,1,4,5,"(slice(162, 174, None), slice(149, 160, None), slice(160, 171, None))","[168, 154, 165]",1.3.6.1.4.1.14519.5.2.1.6279.6001.253322967203074795232627653819
993,6,1,2,3,4,4,1,3,5,"(slice(359, 372, None), slice(103, 113, None), slice(272, 279, None))","[365, 108, 275]",1.3.6.1.4.1.14519.5.2.1.6279.6001.253322967203074795232627653819
994,6,1,3,3,4,4,1,4,4,"(slice(291, 308, None), slice(394, 412, None), slice(376, 392, None))","[299, 403, 384]",1.3.6.1.4.1.14519.5.2.1.6279.6001.253322967203074795232627653819
995,6,1,3,3,3,3,3,3,4,"(slice(320, 337, None), slice(150, 173, None), slice(361, 375, None))","[328, 161, 368]",1.3.6.1.4.1.14519.5.2.1.6279.6001.658611160253017715059194304729
996,6,1,3,4,3,3,3,4,3,"(slice(289, 309, None), slice(141, 155, None), slice(384, 390, None))","[299, 148, 387]",1.3.6.1.4.1.14519.5.2.1.6279.6001.658611160253017715059194304729
997,4,1,3,1,4,4,1,5,5,"(slice(399, 427, None), slice(342, 369, None), slice(140, 149, None))","[413, 355, 144]",1.3.6.1.4.1.14519.5.2.1.6279.6001.309672797925724868457151381131
998,6,1,1,3,5,4,2,4,5,"(slice(295, 304, None), slice(455, 466, None), slice(40, 46, None))","[299, 460, 43]",1.3.6.1.4.1.14519.5.2.1.6279.6001.151764021165118974848436095034
999,6,1,2,2,5,5,1,5,5,"(slice(147, 155, None), slice(117, 125, None), slice(113, 118, None))","[151, 121, 115]",1.3.6.1.4.1.14519.5.2.1.6279.6001.151764021165118974848436095034
1000,4,1,1,1,5,4,2,5,5,"(slice(282, 293, None), slice(385, 396, None), slice(149, 154, None))","[287, 390, 151]",1.3.6.1.4.1.14519.5.2.1.6279.6001.151764021165118974848436095034
1001,4,1,1,2,5,4,1,5,5,"(slice(176, 189, None), slice(135, 148, None), slice(181, 189, None))","[182, 141, 185]",1.3.6.1.4.1.14519.5.2.1.6279.6001.151764021165118974848436095034
1002,6,1,2,3,4,4,2,5,4,"(slice(204, 222, None), slice(323, 342, None), slice(368, 382, None))","[213, 332, 375]",1.3.6.1.4.1.14519.5.2.1.6279.6001.142154819868944114554521645782
1003,6,1,2,4,2,4,1,2,3,"(slice(255, 273, None), slice(221, 239, None), slice(207, 214, None))","[264, 230, 210]",1.3.6.1.4.1.14519.5.2.1.6279.6001.390513733720659266816639651938
1004,4,1,1,1,5,5,1,4,5,"(slice(318, 328, None), slice(153, 163, None), slice(275, 281, None))","[323, 158, 278]",1.3.6.1.4.1.14519.5.2.1.6279.6001.149893110752986700464921264055
1005,6,1,3,4,4,4,2,5,5,"(slice(270, 311, None), slice(111, 149, None), slice(293, 322, None))","[290, 130, 307]",1.3.6.1.4.1.14519.5.2.1.6279.6001.149893110752986700464921264055
1006,6,1,2,3,4,5,1,3,5,"(slice(247, 256, None), slice(84, 91, None), slice(187, 192, None))","[251, 87, 189]",1.3.6.1.4.1.14519.5.2.1.6279.6001.170052181746004939527661217512
1007,6,1,2,3,3,3,3,5,5,"(slice(300, 320, None), slice(148, 170, None), slice(243, 248, None))","[310, 159, 245]",1.3.6.1.4.1.14519.5.2.1.6279.6001.170052181746004939527661217512
1008,6,1,2,3,3,3,1,4,3,"(slice(329, 358, None), slice(125, 150, None), slice(142, 163, None))","[343, 137, 152]",1.3.6.1.4.1.14519.5.2.1.6279.6001.232071262560365924176679652948
1009,6,1,1,2,4,4,1,4,5,"(slice(125, 138, None), slice(169, 185, None), slice(63, 69, None))","[131, 177, 66]",1.3.6.1.4.1.14519.5.2.1.6279.6001.898642529028521482602829374444
1010,6,1,1,3,4,4,1,4,4,"(slice(161, 176, None), slice(394, 409, None), slice(95, 101, None))","[168, 401, 98]",1.3.6.1.4.1.14519.5.2.1.6279.6001.898642529028521482602829374444
1011,6,1,1,3,4,3,1,3,5,"(slice(264, 277, None), slice(377, 394, None), slice(88, 91, None))","[270, 385, 89]",1.3.6.1.4.1.14519.5.2.1.6279.6001.100398138793540579077826395208
1012,6,1,3,4,3,3,2,5,4,"(slice(327, 378, None), slice(122, 185, None), slice(233, 257, None))","[352, 153, 245]",1.3.6.1.4.1.14519.5.2.1.6279.6001.220596530836092324070084384692
1013,6,1,3,3,4,4,2,4,4,"(slice(292, 306, None), slice(135, 149, None), slice(375, 387, None))","[299, 142, 381]",1.3.6.1.4.1.14519.5.2.1.6279.6001.114914167428485563471327801935
1014,6,1,2,3,4,4,2,4,5,"(slice(232, 252, None), slice(198, 215, None), slice(396, 412, None))","[242, 206, 404]",1.3.6.1.4.1.14519.5.2.1.6279.6001.114914167428485563471327801935
1015,6,1,1,3,4,4,1,4,5,"(slice(287, 304, None), slice(51, 62, None), slice(52, 58, None))","[295, 56, 55]",1.3.6.1.4.1.14519.5.2.1.6279.6001.304700823314998198591652152637
1016,4,1,1,2,5,4,1,5,5,"(slice(252, 278, None), slice(415, 438, None), slice(242, 257, None))","[265, 426, 249]",1.3.6.1.4.1.14519.5.2.1.6279.6001.213854687290736562463866711534
1017,6,1,2,2,4,3,1,2,4,"(slice(123, 134, None), slice(339, 349, None), slice(253, 258, None))","[128, 344, 255]",1.3.6.1.4.1.14519.5.2.1.6279.6001.182192086929819295877506541021
1018,6,1,2,2,3,3,3,3,3,"(slice(380, 396, None), slice(311, 327, None), slice(260, 267, None))","[388, 319, 263]",1.3.6.1.4.1.14519.5.2.1.6279.6001.182192086929819295877506541021
1019,6,1,2,3,1,3,1,2,1,"(slice(194, 212, None), slice(132, 149, None), slice(195, 201, None))","[203, 140, 198]",1.3.6.1.4.1.14519.5.2.1.6279.6001.199975006921901879512837687266
1020,6,1,2,3,1,3,1,1,1,"(slice(230, 244, None), slice(312, 328, None), slice(213, 217, None))","[237, 320, 215]",1.3.6.1.4.1.14519.5.2.1.6279.6001.199975006921901879512837687266
1021,6,1,1,3,4,4,1,4,4,"(slice(275, 285, None), slice(435, 444, None), slice(120, 124, None))","[280, 439, 122]",1.3.6.1.4.1.14519.5.2.1.6279.6001.419601611032172899567156073142
1022,3,1,1,1,5,4,1,5,5,"(slice(355, 371, None), slice(393, 412, None), slice(258, 270, None))","[363, 402, 264]",1.3.6.1.4.1.14519.5.2.1.6279.6001.768276876111112560631432843476
1023,5,1,1,2,5,5,1,3,5,"(slice(199, 209, None), slice(403, 412, None), slice(68, 70, None))","[204, 407, 69]",1.3.6.1.4.1.14519.5.2.1.6279.6001.450501966058662668272378865145
1024,6,1,2,2,4,3,2,4,4,"(slice(280, 301, None), slice(328, 349, None), slice(105, 114, None))","[290, 338, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.450501966058662668272378865145
1025,6,1,3,4,2,2,3,4,4,"(slice(269, 283, None), slice(193, 216, None), slice(201, 213, None))","[276, 204, 207]",1.3.6.1.4.1.14519.5.2.1.6279.6001.323535944958374186208096541480
1026,6,1,2,2,4,2,1,3,5,"(slice(389, 398, None), slice(142, 154, None), slice(127, 131, None))","[393, 148, 129]",1.3.6.1.4.1.14519.5.2.1.6279.6001.631047517458234322522264161877
1027,6,1,2,5,3,3,3,5,4,"(slice(186, 231, None), slice(333, 377, None), slice(174, 186, None))","[208, 355, 180]",1.3.6.1.4.1.14519.5.2.1.6279.6001.801945620899034889998809817499
1028,6,1,4,2,4,4,4,4,5,"(slice(286, 299, None), slice(129, 144, None), slice(83, 87, None))","[292, 136, 85]",1.3.6.1.4.1.14519.5.2.1.6279.6001.323302986710576400812869264321
1029,6,1,1,3,4,3,1,4,5,"(slice(274, 291, None), slice(151, 167, None), slice(91, 102, None))","[282, 159, 96]",1.3.6.1.4.1.14519.5.2.1.6279.6001.724251104254976962355686318345
1030,6,1,1,3,4,4,1,4,5,"(slice(332, 343, None), slice(394, 406, None), slice(264, 273, None))","[337, 400, 268]",1.3.6.1.4.1.14519.5.2.1.6279.6001.724251104254976962355686318345
1031,3,1,1,1,5,4,1,5,5,"(slice(307, 335, None), slice(107, 133, None), slice(155, 173, None))","[321, 120, 164]",1.3.6.1.4.1.14519.5.2.1.6279.6001.780558315515979171413904604168
1032,6,1,2,3,3,3,1,4,4,"(slice(136, 150, None), slice(143, 155, None), slice(212, 218, None))","[143, 149, 215]",1.3.6.1.4.1.14519.5.2.1.6279.6001.259124675432205040899951626253
1033,6,1,1,2,3,4,2,4,4,"(slice(376, 389, None), slice(168, 179, None), slice(76, 79, None))","[382, 173, 77]",1.3.6.1.4.1.14519.5.2.1.6279.6001.134370886216012873213579659366
1034,6,1,2,4,2,4,2,4,4,"(slice(294, 322, None), slice(153, 182, None), slice(168, 174, None))","[308, 167, 171]",1.3.6.1.4.1.14519.5.2.1.6279.6001.134370886216012873213579659366
1035,5,1,2,2,3,3,1,4,5,"(slice(252, 263, None), slice(380, 393, None), slice(75, 78, None))","[257, 386, 76]",1.3.6.1.4.1.14519.5.2.1.6279.6001.148447286464082095534651426689
1036,6,1,1,3,4,4,2,4,5,"(slice(161, 172, None), slice(160, 171, None), slice(88, 92, None))","[166, 165, 90]",1.3.6.1.4.1.14519.5.2.1.6279.6001.148447286464082095534651426689
1037,6,1,1,3,4,5,1,3,4,"(slice(202, 210, None), slice(310, 319, None), slice(97, 101, None))","[206, 314, 99]",1.3.6.1.4.1.14519.5.2.1.6279.6001.148447286464082095534651426689
1038,6,1,3,5,2,4,3,5,3,"(slice(184, 232, None), slice(314, 355, None), slice(135, 154, None))","[208, 334, 144]",1.3.6.1.4.1.14519.5.2.1.6279.6001.148447286464082095534651426689
1039,6,1,1,3,3,4,1,4,4,"(slice(280, 299, None), slice(365, 389, None), slice(155, 161, None))","[289, 377, 158]",1.3.6.1.4.1.14519.5.2.1.6279.6001.148447286464082095534651426689
1040,6,1,1,3,4,4,1,4,5,"(slice(209, 220, None), slice(345, 355, None), slice(167, 172, None))","[214, 350, 169]",1.3.6.1.4.1.14519.5.2.1.6279.6001.148447286464082095534651426689
1041,6,1,1,3,3,4,1,2,2,"(slice(313, 323, None), slice(402, 412, None), slice(132, 135, None))","[318, 407, 133]",1.3.6.1.4.1.14519.5.2.1.6279.6001.771741891125176943862272696845
1042,5,1,1,2,4,4,1,3,4,"(slice(291, 301, None), slice(432, 444, None), slice(75, 79, None))","[296, 438, 77]",1.3.6.1.4.1.14519.5.2.1.6279.6001.192256506776434538421891524301
1043,6,1,1,3,2,4,1,1,1,"(slice(194, 208, None), slice(160, 175, None), slice(113, 116, None))","[201, 167, 114]",1.3.6.1.4.1.14519.5.2.1.6279.6001.192256506776434538421891524301
1044,6,1,1,2,2,4,1,2,2,"(slice(180, 194, None), slice(399, 416, None), slice(125, 127, None))","[187, 407, 126]",1.3.6.1.4.1.14519.5.2.1.6279.6001.192256506776434538421891524301
1045,6,1,1,3,2,4,1,2,2,"(slice(277, 289, None), slice(205, 221, None), slice(155, 158, None))","[283, 213, 156]",1.3.6.1.4.1.14519.5.2.1.6279.6001.192256506776434538421891524301
1046,6,1,1,3,2,4,1,1,1,"(slice(290, 300, None), slice(288, 302, None), slice(156, 159, None))","[295, 295, 157]",1.3.6.1.4.1.14519.5.2.1.6279.6001.192256506776434538421891524301
1047,6,1,1,3,5,4,1,3,5,"(slice(278, 298, None), slice(293, 311, None), slice(123, 131, None))","[288, 302, 127]",1.3.6.1.4.1.14519.5.2.1.6279.6001.167661207884826429102690781600
1048,6,1,2,4,5,4,2,5,5,"(slice(276, 306, None), slice(380, 409, None), slice(164, 175, None))","[291, 394, 169]",1.3.6.1.4.1.14519.5.2.1.6279.6001.167661207884826429102690781600
1049,6,1,1,4,5,5,1,4,5,"(slice(415, 426, None), slice(108, 118, None), slice(82, 87, None))","[420, 113, 84]",1.3.6.1.4.1.14519.5.2.1.6279.6001.280072876841890439628529365478
1050,6,1,3,4,4,4,3,5,5,"(slice(272, 312, None), slice(395, 447, None), slice(82, 112, None))","[292, 421, 97]",1.3.6.1.4.1.14519.5.2.1.6279.6001.280072876841890439628529365478
1051,6,1,1,4,5,4,1,4,5,"(slice(339, 358, None), slice(102, 122, None), slice(101, 109, None))","[348, 112, 105]",1.3.6.1.4.1.14519.5.2.1.6279.6001.280072876841890439628529365478
1052,6,1,2,4,4,4,2,4,5,"(slice(164, 187, None), slice(138, 161, None), slice(160, 167, None))","[175, 149, 163]",1.3.6.1.4.1.14519.5.2.1.6279.6001.280072876841890439628529365478
1053,6,1,1,3,3,4,1,3,4,"(slice(324, 338, None), slice(105, 123, None), slice(174, 180, None))","[331, 114, 177]",1.3.6.1.4.1.14519.5.2.1.6279.6001.280072876841890439628529365478
1054,6,1,2,3,3,4,2,4,3,"(slice(209, 230, None), slice(84, 102, None), slice(211, 219, None))","[219, 93, 215]",1.3.6.1.4.1.14519.5.2.1.6279.6001.280072876841890439628529365478
1055,6,1,1,3,4,4,1,4,5,"(slice(320, 339, None), slice(287, 301, None), slice(252, 258, None))","[329, 294, 255]",1.3.6.1.4.1.14519.5.2.1.6279.6001.280072876841890439628529365478
1056,6,1,4,5,4,4,3,5,5,"(slice(298, 351, None), slice(305, 369, None), slice(106, 120, None))","[324, 337, 113]",1.3.6.1.4.1.14519.5.2.1.6279.6001.112740418331256326754121315800
1057,6,1,3,5,4,4,4,5,4,"(slice(173, 219, None), slice(167, 209, None), slice(111, 117, None))","[196, 188, 114]",1.3.6.1.4.1.14519.5.2.1.6279.6001.112740418331256326754121315800
1058,6,1,2,3,4,4,1,4,5,"(slice(357, 372, None), slice(104, 120, None), slice(181, 189, None))","[364, 112, 185]",1.3.6.1.4.1.14519.5.2.1.6279.6001.141069661700670042960678408762
1059,6,1,2,3,4,4,2,4,5,"(slice(442, 460, None), slice(378, 392, None), slice(159, 165, None))","[451, 385, 162]",1.3.6.1.4.1.14519.5.2.1.6279.6001.265453131727473342790950829556
1060,6,1,2,3,5,4,2,4,5,"(slice(415, 428, None), slice(106, 122, None), slice(177, 184, None))","[421, 114, 180]",1.3.6.1.4.1.14519.5.2.1.6279.6001.265453131727473342790950829556
1061,6,1,2,4,4,3,2,4,5,"(slice(417, 445, None), slice(324, 346, None), slice(196, 204, None))","[431, 335, 200]",1.3.6.1.4.1.14519.5.2.1.6279.6001.265453131727473342790950829556
1062,6,1,2,3,4,4,2,4,5,"(slice(427, 448, None), slice(369, 387, None), slice(216, 225, None))","[437, 378, 220]",1.3.6.1.4.1.14519.5.2.1.6279.6001.265453131727473342790950829556
1063,6,1,1,3,5,3,1,4,5,"(slice(368, 383, None), slice(365, 380, None), slice(231, 237, None))","[375, 372, 234]",1.3.6.1.4.1.14519.5.2.1.6279.6001.265453131727473342790950829556
1064,6,1,2,4,4,4,1,4,5,"(slice(316, 333, None), slice(347, 365, None), slice(46, 54, None))","[324, 356, 50]",1.3.6.1.4.1.14519.5.2.1.6279.6001.194440094986948071643661798326
1065,4,1,2,3,4,4,1,5,5,"(slice(250, 267, None), slice(107, 135, None), slice(99, 108, None))","[258, 121, 103]",1.3.6.1.4.1.14519.5.2.1.6279.6001.104562737760173137525888934217
1066,6,1,1,2,5,5,1,3,5,"(slice(311, 322, None), slice(199, 209, None), slice(221, 226, None))","[316, 204, 223]",1.3.6.1.4.1.14519.5.2.1.6279.6001.215785045378334625097907422785
1067,6,1,1,3,4,4,1,4,5,"(slice(379, 392, None), slice(318, 332, None), slice(87, 91, None))","[385, 325, 89]",1.3.6.1.4.1.14519.5.2.1.6279.6001.292194861362266467652267941663
1068,6,1,3,3,4,4,1,3,4,"(slice(374, 384, None), slice(156, 169, None), slice(103, 107, None))","[379, 162, 105]",1.3.6.1.4.1.14519.5.2.1.6279.6001.162351539386551708034407968929
1069,5,1,2,3,4,4,1,4,5,"(slice(209, 225, None), slice(439, 456, None), slice(121, 129, None))","[217, 447, 125]",1.3.6.1.4.1.14519.5.2.1.6279.6001.162351539386551708034407968929
1070,6,1,2,4,5,3,3,4,5,"(slice(200, 214, None), slice(430, 448, None), slice(48, 52, None))","[207, 439, 50]",1.3.6.1.4.1.14519.5.2.1.6279.6001.692598144815688523679745963696
1071,6,1,2,3,3,4,2,4,2,"(slice(242, 254, None), slice(195, 208, None), slice(116, 119, None))","[248, 201, 117]",1.3.6.1.4.1.14519.5.2.1.6279.6001.692598144815688523679745963696
1072,6,1,2,4,3,2,2,5,4,"(slice(315, 363, None), slice(399, 426, None), slice(325, 344, None))","[339, 412, 334]",1.3.6.1.4.1.14519.5.2.1.6279.6001.525937963993475482158828421281
1073,6,1,1,3,4,4,1,4,5,"(slice(305, 318, None), slice(399, 412, None), slice(127, 132, None))","[311, 405, 129]",1.3.6.1.4.1.14519.5.2.1.6279.6001.297988578825170426663869669862
1074,6,1,2,4,2,3,1,2,1,"(slice(192, 222, None), slice(316, 336, None), slice(171, 181, None))","[207, 326, 176]",1.3.6.1.4.1.14519.5.2.1.6279.6001.297988578825170426663869669862
1075,6,1,1,2,5,4,1,3,5,"(slice(301, 312, None), slice(93, 104, None), slice(59, 62, None))","[306, 98, 60]",1.3.6.1.4.1.14519.5.2.1.6279.6001.229860476925100292554329427970
1076,6,1,1,2,5,5,1,4,5,"(slice(150, 161, None), slice(102, 111, None), slice(73, 76, None))","[155, 106, 74]",1.3.6.1.4.1.14519.5.2.1.6279.6001.229860476925100292554329427970
1077,6,1,1,2,4,5,1,3,5,"(slice(166, 174, None), slice(114, 122, None), slice(74, 77, None))","[170, 118, 75]",1.3.6.1.4.1.14519.5.2.1.6279.6001.229860476925100292554329427970
1078,6,1,2,3,4,3,2,4,5,"(slice(344, 361, None), slice(96, 115, None), slice(300, 308, None))","[352, 105, 304]",1.3.6.1.4.1.14519.5.2.1.6279.6001.168037818448885856452592057286
1079,6,1,2,3,4,3,1,2,5,"(slice(297, 309, None), slice(409, 420, None), slice(176, 179, None))","[303, 414, 177]",1.3.6.1.4.1.14519.5.2.1.6279.6001.119515474430718803379832249911
1080,6,1,1,3,5,4,1,4,5,"(slice(424, 433, None), slice(350, 361, None), slice(162, 166, None))","[428, 355, 164]",1.3.6.1.4.1.14519.5.2.1.6279.6001.265775376735520890308424143898
1081,6,1,2,3,4,3,2,4,5,"(slice(378, 391, None), slice(221, 237, None), slice(186, 191, None))","[384, 229, 188]",1.3.6.1.4.1.14519.5.2.1.6279.6001.265775376735520890308424143898
1082,6,1,1,3,4,4,1,5,5,"(slice(386, 397, None), slice(114, 125, None), slice(194, 200, None))","[391, 119, 197]",1.3.6.1.4.1.14519.5.2.1.6279.6001.265775376735520890308424143898
1083,6,1,2,4,3,4,4,4,4,"(slice(347, 369, None), slice(117, 139, None), slice(106, 111, None))","[358, 128, 108]",1.3.6.1.4.1.14519.5.2.1.6279.6001.233001470265230594739708503198
1084,6,1,1,3,5,4,1,4,5,"(slice(355, 365, None), slice(334, 343, None), slice(264, 270, None))","[360, 338, 267]",1.3.6.1.4.1.14519.5.2.1.6279.6001.250481236093201801255751845296
1085,6,1,1,3,4,4,2,3,5,"(slice(308, 318, None), slice(64, 74, None), slice(119, 123, None))","[313, 69, 121]",1.3.6.1.4.1.14519.5.2.1.6279.6001.756684168227383088294595834066
1086,6,1,1,2,4,4,1,4,5,"(slice(327, 339, None), slice(108, 120, None), slice(227, 232, None))","[333, 114, 229]",1.3.6.1.4.1.14519.5.2.1.6279.6001.417815314896088956784723476543
1087,6,1,2,4,5,4,2,5,5,"(slice(139, 164, None), slice(420, 441, None), slice(144, 168, None))","[151, 430, 156]",1.3.6.1.4.1.14519.5.2.1.6279.6001.946129570505893110165820050204
1088,6,1,1,3,5,5,2,4,5,"(slice(303, 319, None), slice(466, 479, None), slice(188, 195, None))","[311, 472, 191]",1.3.6.1.4.1.14519.5.2.1.6279.6001.172573195301625265149778785969
1089,6,1,2,3,5,4,2,4,5,"(slice(400, 414, None), slice(205, 221, None), slice(292, 303, None))","[407, 213, 297]",1.3.6.1.4.1.14519.5.2.1.6279.6001.172573195301625265149778785969
1090,6,1,2,4,4,4,1,5,5,"(slice(299, 321, None), slice(151, 175, None), slice(126, 133, None))","[310, 163, 129]",1.3.6.1.4.1.14519.5.2.1.6279.6001.244681063194071446501270815660
1091,6,1,1,4,2,4,1,3,2,"(slice(353, 365, None), slice(374, 387, None), slice(175, 180, None))","[359, 380, 177]",1.3.6.1.4.1.14519.5.2.1.6279.6001.584871944187559733312703328980
1092,6,1,1,2,4,4,1,2,5,"(slice(273, 282, None), slice(381, 390, None), slice(177, 181, None))","[277, 385, 179]",1.3.6.1.4.1.14519.5.2.1.6279.6001.323426705628838942177546503237
1093,6,1,2,3,4,3,1,3,5,"(slice(199, 212, None), slice(440, 453, None), slice(178, 184, None))","[205, 446, 181]",1.3.6.1.4.1.14519.5.2.1.6279.6001.179943248049071805421192715219
1094,5,1,2,2,4,4,1,3,5,"(slice(142, 152, None), slice(394, 403, None), slice(287, 294, None))","[147, 398, 290]",1.3.6.1.4.1.14519.5.2.1.6279.6001.336894364358709782463716339027
1095,3,1,1,1,5,4,1,3,5,"(slice(290, 303, None), slice(160, 174, None), slice(196, 205, None))","[296, 167, 200]",1.3.6.1.4.1.14519.5.2.1.6279.6001.244590453955380448651329424024
1096,6,1,1,3,4,4,1,4,5,"(slice(366, 379, None), slice(437, 448, None), slice(89, 94, None))","[372, 442, 91]",1.3.6.1.4.1.14519.5.2.1.6279.6001.247060297988514823071467295949
1097,3,1,3,1,5,3,1,5,5,"(slice(206, 223, None), slice(100, 121, None), slice(126, 143, None))","[214, 110, 134]",1.3.6.1.4.1.14519.5.2.1.6279.6001.114218724025049818743426522343
1098,6,1,3,3,4,4,2,4,5,"(slice(242, 255, None), slice(419, 434, None), slice(194, 204, None))","[248, 426, 199]",1.3.6.1.4.1.14519.5.2.1.6279.6001.114218724025049818743426522343
1099,6,1,2,3,4,3,2,4,5,"(slice(259, 278, None), slice(392, 410, None), slice(222, 238, None))","[268, 401, 230]",1.3.6.1.4.1.14519.5.2.1.6279.6001.114218724025049818743426522343
1100,6,1,2,4,3,4,3,5,4,"(slice(321, 341, None), slice(106, 138, None), slice(131, 141, None))","[331, 122, 136]",1.3.6.1.4.1.14519.5.2.1.6279.6001.297964221542942838344351735414
1101,6,1,2,3,4,4,2,4,4,"(slice(240, 252, None), slice(163, 176, None), slice(147, 151, None))","[246, 169, 149]",1.3.6.1.4.1.14519.5.2.1.6279.6001.297964221542942838344351735414
1102,3,1,1,1,5,4,1,5,5,"(slice(284, 309, None), slice(400, 416, None), slice(215, 224, None))","[296, 408, 219]",1.3.6.1.4.1.14519.5.2.1.6279.6001.244442540088515471945035689377
1103,6,1,2,5,3,3,4,5,5,"(slice(305, 360, None), slice(284, 336, None), slice(434, 482, None))","[332, 310, 458]",1.3.6.1.4.1.14519.5.2.1.6279.6001.771831598853841017505646275338
1104,6,1,2,3,5,4,1,4,5,"(slice(265, 281, None), slice(447, 461, None), slice(42, 48, None))","[273, 454, 45]",1.3.6.1.4.1.14519.5.2.1.6279.6001.206539885154775002929031534291
1105,6,1,2,3,5,4,1,4,5,"(slice(241, 251, None), slice(458, 468, None), slice(70, 73, None))","[246, 463, 71]",1.3.6.1.4.1.14519.5.2.1.6279.6001.206539885154775002929031534291
1106,6,1,4,5,2,4,5,5,5,"(slice(209, 243, None), slice(369, 419, None), slice(83, 91, None))","[226, 394, 87]",1.3.6.1.4.1.14519.5.2.1.6279.6001.741709061958490690246385302477
1107,3,1,1,1,5,4,2,5,5,"(slice(295, 304, None), slice(120, 130, None), slice(98, 101, None))","[299, 125, 99]",1.3.6.1.4.1.14519.5.2.1.6279.6001.741709061958490690246385302477
1108,6,1,2,4,4,4,2,5,5,"(slice(298, 323, None), slice(430, 454, None), slice(88, 105, None))","[310, 442, 96]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249530219848512542668813996730
1109,6,1,2,3,4,4,2,4,4,"(slice(248, 267, None), slice(466, 485, None), slice(126, 135, None))","[257, 475, 130]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249530219848512542668813996730
1110,6,1,2,3,4,3,1,4,5,"(slice(259, 271, None), slice(65, 83, None), slice(244, 250, None))","[265, 74, 247]",1.3.6.1.4.1.14519.5.2.1.6279.6001.249530219848512542668813996730
1111,3,1,2,1,5,4,1,5,5,"(slice(321, 330, None), slice(354, 365, None), slice(238, 247, None))","[325, 359, 242]",1.3.6.1.4.1.14519.5.2.1.6279.6001.603166427542096384265514998412
1112,6,1,1,3,3,3,1,5,5,"(slice(311, 327, None), slice(52, 66, None), slice(119, 130, None))","[319, 59, 124]",1.3.6.1.4.1.14519.5.2.1.6279.6001.138080888843357047811238713686
1113,6,1,4,5,3,3,3,5,3,"(slice(278, 339, None), slice(113, 174, None), slice(339, 387, None))","[308, 143, 363]",1.3.6.1.4.1.14519.5.2.1.6279.6001.286422846896797433168187085942
1114,6,1,2,4,2,2,2,5,4,"(slice(223, 321, None), slice(326, 375, None), slice(228, 248, None))","[272, 350, 238]",1.3.6.1.4.1.14519.5.2.1.6279.6001.205993750485568250373835565680
1115,6,1,4,3,3,3,4,4,4,"(slice(325, 342, None), slice(337, 367, None), slice(248, 256, None))","[333, 352, 252]",1.3.6.1.4.1.14519.5.2.1.6279.6001.205993750485568250373835565680
1116,6,1,4,3,4,4,3,3,4,"(slice(318, 333, None), slice(330, 344, None), slice(250, 255, None))","[325, 337, 252]",1.3.6.1.4.1.14519.5.2.1.6279.6001.205993750485568250373835565680
1117,6,1,2,2,5,4,1,5,5,"(slice(379, 393, None), slice(144, 158, None), slice(301, 310, None))","[386, 151, 305]",1.3.6.1.4.1.14519.5.2.1.6279.6001.227885601428639043345478571594
1118,6,1,2,3,4,3,2,3,4,"(slice(143, 159, None), slice(361, 372, None), slice(133, 137, None))","[151, 366, 135]",1.3.6.1.4.1.14519.5.2.1.6279.6001.192419869605596446455526220766
1119,6,1,3,4,3,3,3,5,5,"(slice(264, 288, None), slice(235, 268, None), slice(332, 357, None))","[276, 251, 344]",1.3.6.1.4.1.14519.5.2.1.6279.6001.208511362832825683639135205368
1120,6,1,3,5,2,4,4,5,5,"(slice(285, 329, None), slice(122, 174, None), slice(338, 373, None))","[307, 148, 355]",1.3.6.1.4.1.14519.5.2.1.6279.6001.208511362832825683639135205368
1121,6,1,4,5,3,4,2,5,5,"(slice(170, 217, None), slice(308, 363, None), slice(163, 184, None))","[193, 335, 173]",1.3.6.1.4.1.14519.5.2.1.6279.6001.943403138251347598519939390311
1122,6,1,1,2,4,3,1,3,5,"(slice(204, 215, None), slice(366, 376, None), slice(285, 291, None))","[209, 371, 288]",1.3.6.1.4.1.14519.5.2.1.6279.6001.129567032250534530765928856531
1123,6,1,1,3,4,4,1,4,5,"(slice(252, 268, None), slice(143, 159, None), slice(36, 39, None))","[260, 151, 37]",1.3.6.1.4.1.14519.5.2.1.6279.6001.145759169833745025756371695397
1124,6,1,2,3,4,4,1,3,5,"(slice(263, 274, None), slice(207, 219, None), slice(96, 100, None))","[268, 213, 98]",1.3.6.1.4.1.14519.5.2.1.6279.6001.194246472548954252250399902051
1125,6,1,1,3,5,5,1,4,5,"(slice(341, 353, None), slice(432, 443, None), slice(68, 71, None))","[347, 437, 69]",1.3.6.1.4.1.14519.5.2.1.6279.6001.404364125369979066736354549484
1126,6,1,2,3,4,4,2,4,5,"(slice(172, 215, None), slice(156, 192, None), slice(154, 177, None))","[193, 174, 165]",1.3.6.1.4.1.14519.5.2.1.6279.6001.850739282072340578344345230132
1127,6,1,2,4,1,4,2,3,1,"(slice(198, 211, None), slice(340, 354, None), slice(119, 122, None))","[204, 347, 120]",1.3.6.1.4.1.14519.5.2.1.6279.6001.195557219224169985110295082004
1128,6,1,2,3,2,4,1,3,2,"(slice(184, 197, None), slice(377, 395, None), slice(123, 128, None))","[190, 386, 125]",1.3.6.1.4.1.14519.5.2.1.6279.6001.195557219224169985110295082004
1129,6,1,2,3,4,3,2,4,4,"(slice(198, 215, None), slice(139, 151, None), slice(131, 135, None))","[206, 145, 133]",1.3.6.1.4.1.14519.5.2.1.6279.6001.195557219224169985110295082004
1130,6,1,2,3,1,4,1,3,1,"(slice(262, 277, None), slice(372, 387, None), slice(140, 146, None))","[269, 379, 143]",1.3.6.1.4.1.14519.5.2.1.6279.6001.195557219224169985110295082004
1131,6,1,1,3,1,3,1,3,1,"(slice(236, 251, None), slice(374, 389, None), slice(147, 152, None))","[243, 381, 149]",1.3.6.1.4.1.14519.5.2.1.6279.6001.195557219224169985110295082004
1132,6,1,2,3,2,4,1,4,2,"(slice(270, 293, None), slice(361, 386, None), slice(175, 183, None))","[281, 373, 179]",1.3.6.1.4.1.14519.5.2.1.6279.6001.195557219224169985110295082004
1133,6,1,2,3,2,4,2,3,2,"(slice(196, 212, None), slice(167, 182, None), slice(190, 196, None))","[204, 174, 193]",1.3.6.1.4.1.14519.5.2.1.6279.6001.195557219224169985110295082004
1134,6,1,2,3,2,4,2,3,2,"(slice(244, 261, None), slice(177, 195, None), slice(191, 198, None))","[252, 186, 194]",1.3.6.1.4.1.14519.5.2.1.6279.6001.195557219224169985110295082004
1135,6,1,2,4,2,4,1,3,2,"(slice(256, 272, None), slice(392, 407, None), slice(192, 198, None))","[264, 399, 195]",1.3.6.1.4.1.14519.5.2.1.6279.6001.195557219224169985110295082004
1136,6,1,1,3,3,4,1,3,5,"(slice(346, 358, None), slice(422, 433, None), slice(183, 192, None))","[352, 427, 187]",1.3.6.1.4.1.14519.5.2.1.6279.6001.111172165674661221381920536987
1137,6,1,2,3,3,3,2,4,4,"(slice(358, 375, None), slice(349, 367, None), slice(157, 167, None))","[366, 358, 162]",1.3.6.1.4.1.14519.5.2.1.6279.6001.270951128717816232360812849541
1138,6,2,2,3,3,4,2,4,2,"(slice(245, 275, None), slice(382, 413, None), slice(305, 328, None))","[260, 397, 316]",1.3.6.1.4.1.14519.5.2.1.6279.6001.277662902666135640561346462196
1139,6,1,3,5,4,3,3,5,4,"(slice(260, 286, None), slice(114, 139, None), slice(105, 109, None))","[273, 126, 107]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202643836890896697853521610450
1140,6,1,1,3,4,4,1,4,4,"(slice(311, 324, None), slice(421, 435, None), slice(167, 177, None))","[317, 428, 172]",1.3.6.1.4.1.14519.5.2.1.6279.6001.143412474064515942785157561636
1141,6,1,2,3,4,4,2,4,5,"(slice(375, 393, None), slice(174, 190, None), slice(180, 185, None))","[384, 182, 182]",1.3.6.1.4.1.14519.5.2.1.6279.6001.308655308958459380153492314021
1142,3,1,1,1,5,4,1,4,5,"(slice(193, 207, None), slice(110, 123, None), slice(276, 281, None))","[200, 116, 278]",1.3.6.1.4.1.14519.5.2.1.6279.6001.308655308958459380153492314021
1143,6,1,1,3,5,3,1,5,5,"(slice(146, 162, None), slice(193, 211, None), slice(194, 205, None))","[154, 202, 199]",1.3.6.1.4.1.14519.5.2.1.6279.6001.119209873306155771318545953948
1144,6,1,1,3,4,4,2,5,5,"(slice(143, 160, None), slice(133, 147, None), slice(269, 276, None))","[151, 140, 272]",1.3.6.1.4.1.14519.5.2.1.6279.6001.119209873306155771318545953948
1145,6,1,2,4,4,4,2,4,5,"(slice(300, 317, None), slice(212, 234, None), slice(192, 197, None))","[308, 223, 194]",1.3.6.1.4.1.14519.5.2.1.6279.6001.964952370561266624992539111877
1146,6,1,3,4,4,4,3,5,5,"(slice(178, 212, None), slice(176, 204, None), slice(194, 205, None))","[195, 190, 199]",1.3.6.1.4.1.14519.5.2.1.6279.6001.964952370561266624992539111877
1147,6,1,1,3,4,3,2,3,5,"(slice(244, 252, None), slice(378, 388, None), slice(200, 206, None))","[248, 383, 203]",1.3.6.1.4.1.14519.5.2.1.6279.6001.964952370561266624992539111877
1148,6,1,1,3,4,4,1,4,5,"(slice(198, 210, None), slice(370, 383, None), slice(202, 208, None))","[204, 376, 205]",1.3.6.1.4.1.14519.5.2.1.6279.6001.964952370561266624992539111877
1149,6,1,2,3,3,3,1,4,3,"(slice(345, 360, None), slice(208, 223, None), slice(291, 296, None))","[352, 215, 293]",1.3.6.1.4.1.14519.5.2.1.6279.6001.147250707071097813243473865421
1150,6,1,2,4,3,4,2,5,3,"(slice(261, 277, None), slice(353, 369, None), slice(410, 421, None))","[269, 361, 415]",1.3.6.1.4.1.14519.5.2.1.6279.6001.147250707071097813243473865421
1151,6,1,2,3,4,3,1,2,5,"(slice(263, 275, None), slice(422, 436, None), slice(78, 81, None))","[269, 429, 79]",1.3.6.1.4.1.14519.5.2.1.6279.6001.185226274332527104841463955058
1152,6,1,1,2,4,4,2,2,5,"(slice(201, 209, None), slice(131, 140, None), slice(113, 115, None))","[205, 135, 114]",1.3.6.1.4.1.14519.5.2.1.6279.6001.185226274332527104841463955058
1153,6,1,2,3,2,3,1,3,1,"(slice(298, 328, None), slice(412, 439, None), slice(119, 126, None))","[313, 425, 122]",1.3.6.1.4.1.14519.5.2.1.6279.6001.226456162308124493341905600418
1154,6,1,3,4,2,4,2,4,4,"(slice(350, 373, None), slice(397, 416, None), slice(134, 140, None))","[361, 406, 137]",1.3.6.1.4.1.14519.5.2.1.6279.6001.226456162308124493341905600418
1155,6,1,2,3,4,4,2,4,5,"(slice(164, 178, None), slice(107, 120, None), slice(117, 121, None))","[171, 113, 119]",1.3.6.1.4.1.14519.5.2.1.6279.6001.130036599816889919308975074972
1156,5,1,2,3,4,4,2,4,5,"(slice(329, 345, None), slice(192, 208, None), slice(245, 249, None))","[337, 200, 247]",1.3.6.1.4.1.14519.5.2.1.6279.6001.167919147233131417984739058859
1157,6,1,2,2,4,2,1,3,5,"(slice(355, 365, None), slice(316, 329, None), slice(102, 106, None))","[360, 322, 104]",1.3.6.1.4.1.14519.5.2.1.6279.6001.252814707117018427472206147014
1158,6,1,2,3,4,3,2,4,5,"(slice(207, 220, None), slice(75, 89, None), slice(134, 139, None))","[213, 82, 136]",1.3.6.1.4.1.14519.5.2.1.6279.6001.252814707117018427472206147014
1159,6,1,1,3,4,5,1,4,5,"(slice(263, 275, None), slice(37, 49, None), slice(205, 214, None))","[269, 43, 209]",1.3.6.1.4.1.14519.5.2.1.6279.6001.204303454658845815034433453512
1160,6,1,1,3,4,3,1,4,5,"(slice(91, 109, None), slice(144, 158, None), slice(295, 303, None))","[100, 151, 299]",1.3.6.1.4.1.14519.5.2.1.6279.6001.204303454658845815034433453512
1161,6,1,4,4,3,3,3,5,4,"(slice(271, 315, None), slice(43, 82, None), slice(287, 313, None))","[293, 62, 300]",1.3.6.1.4.1.14519.5.2.1.6279.6001.204303454658845815034433453512
1162,6,1,2,2,4,3,2,4,5,"(slice(339, 356, None), slice(188, 205, None), slice(489, 500, None))","[347, 196, 494]",1.3.6.1.4.1.14519.5.2.1.6279.6001.204303454658845815034433453512
1163,5,1,1,2,4,3,1,4,5,"(slice(330, 345, None), slice(179, 192, None), slice(500, 510, None))","[337, 185, 505]",1.3.6.1.4.1.14519.5.2.1.6279.6001.204303454658845815034433453512
1164,6,1,2,3,4,4,2,4,5,"(slice(344, 365, None), slice(103, 129, None), slice(110, 130, None))","[354, 116, 120]",1.3.6.1.4.1.14519.5.2.1.6279.6001.397522780537301776672854630421
1165,5,1,1,3,5,4,1,4,5,"(slice(322, 348, None), slice(274, 299, None), slice(480, 510, None))","[335, 286, 495]",1.3.6.1.4.1.14519.5.2.1.6279.6001.397522780537301776672854630421
1166,3,1,1,1,5,4,1,4,5,"(slice(326, 336, None), slice(60, 69, None), slice(66, 70, None))","[331, 64, 68]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202187810895588720702176009630
1167,4,1,1,1,5,3,2,3,5,"(slice(205, 214, None), slice(66, 74, None), slice(67, 71, None))","[209, 70, 69]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202187810895588720702176009630
1168,3,1,1,1,5,3,1,2,5,"(slice(346, 354, None), slice(342, 350, None), slice(68, 71, None))","[350, 346, 69]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202187810895588720702176009630
1169,3,1,1,1,5,4,1,3,5,"(slice(211, 220, None), slice(446, 454, None), slice(80, 84, None))","[215, 450, 82]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202187810895588720702176009630
1170,4,1,1,1,5,3,1,3,5,"(slice(267, 275, None), slice(451, 460, None), slice(101, 103, None))","[271, 455, 102]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202187810895588720702176009630
1171,3,1,1,1,5,4,1,4,5,"(slice(180, 190, None), slice(96, 106, None), slice(121, 124, None))","[185, 101, 122]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202187810895588720702176009630
1172,4,1,2,1,4,3,1,4,5,"(slice(238, 247, None), slice(74, 82, None), slice(136, 138, None))","[242, 78, 137]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202187810895588720702176009630
1173,3,1,1,1,5,2,1,2,5,"(slice(230, 241, None), slice(409, 417, None), slice(199, 204, None))","[235, 413, 201]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202187810895588720702176009630
1174,3,1,2,1,5,2,2,3,5,"(slice(241, 249, None), slice(288, 297, None), slice(207, 210, None))","[245, 292, 208]",1.3.6.1.4.1.14519.5.2.1.6279.6001.202187810895588720702176009630
1175,3,1,1,1,5,5,1,5,5,"(slice(385, 404, None), slice(285, 305, None), slice(492, 511, None))","[394, 295, 501]",1.3.6.1.4.1.14519.5.2.1.6279.6001.162845309248822193437735868939
1176,6,1,1,4,4,5,1,4,5,"(slice(399, 418, None), slice(182, 202, None), slice(107, 111, None))","[408, 192, 109]",1.3.6.1.4.1.14519.5.2.1.6279.6001.670107649586205629860363487713
1177,6,1,1,2,4,4,1,4,5,"(slice(279, 289, None), slice(320, 328, None), slice(156, 162, None))","[284, 324, 159]",1.3.6.1.4.1.14519.5.2.1.6279.6001.910607280658963002048724648683
1178,6,1,2,2,4,4,1,4,4,"(slice(144, 160, None), slice(420, 434, None), slice(66, 69, None))","[152, 427, 67]",1.3.6.1.4.1.14519.5.2.1.6279.6001.245391706475696258069508046497
1179,6,1,2,2,4,3,2,4,5,"(slice(131, 141, None), slice(153, 161, None), slice(45, 47, None))","[136, 157, 46]",1.3.6.1.4.1.14519.5.2.1.6279.6001.299767339686526858593516834230
1180,4,1,2,2,4,4,2,5,5,"(slice(358, 384, None), slice(166, 195, None), slice(49, 56, None))","[371, 180, 52]",1.3.6.1.4.1.14519.5.2.1.6279.6001.299767339686526858593516834230
1181,3,1,1,1,5,4,2,4,5,"(slice(255, 268, None), slice(150, 165, None), slice(61, 66, None))","[261, 157, 63]",1.3.6.1.4.1.14519.5.2.1.6279.6001.299767339686526858593516834230
1182,6,1,1,3,4,4,2,4,5,"(slice(194, 204, None), slice(81, 90, None), slice(70, 72, None))","[199, 85, 71]",1.3.6.1.4.1.14519.5.2.1.6279.6001.299767339686526858593516834230
1183,6,1,1,2,5,3,1,5,5,"(slice(175, 192, None), slice(414, 426, None), slice(80, 84, None))","[183, 420, 82]",1.3.6.1.4.1.14519.5.2.1.6279.6001.299767339686526858593516834230
1184,6,1,2,4,2,4,3,4,3,"(slice(343, 358, None), slice(132, 150, None), slice(300, 310, None))","[350, 141, 305]",1.3.6.1.4.1.14519.5.2.1.6279.6001.293593766328917170359373773080
