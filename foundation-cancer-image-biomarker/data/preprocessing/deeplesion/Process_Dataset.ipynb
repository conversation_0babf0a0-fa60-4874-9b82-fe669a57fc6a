{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "\n", "from fmcib.visualization import visualize_seed_point\n", "\n", "# Replace this with the path to you DeepLesion download. Make sure it is the root folder of the dataset.\n", "DEEPLESION_PATH = \"\""]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Load the annotations\n", "df = pd.read_csv(\"./annotations/deeplesion_annotations_training.csv\")\n", "\n", "# Filter annotations to have z-spacing less than or equal to 3mm\n", "df = df[df[\"spacing_z\"] <= 5]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["1    22836\n", "3     4907\n", "2     4877\n", "Name: Train_Val_Test, dtype: int64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get annotations categories\n", "df[\"Train_Val_Test\"].value_counts()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Split into pre-training dataset and dataset for task1 (lesion anatomical site classification)\n", "pre_train_df = df[df[\"Train_Val_Test\"] == 1]\n", "task1_train_df = df[df[\"Train_Val_Test\"] == 2]\n", "task1_val_test_df = df[df[\"Train_Val_Test\"] == 3]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_4147405/584942068.py:2: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  task1_train_df[\"Coarse_lesion_type\"] = task1_train_df[\"Coarse_lesion_type\"] - 1\n", "/tmp/ipykernel_4147405/584942068.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  task1_val_test_df[\"Coarse_lesion_type\"] = task1_val_test_df[\"Coarse_lesion_type\"] - 1\n"]}], "source": ["# Make range between 0 and 7\n", "task1_train_df[\"Coarse_lesion_type\"] = task1_train_df[\"Coarse_lesion_type\"] - 1\n", "task1_val_test_df[\"Coarse_lesion_type\"] = task1_val_test_df[\"Coarse_lesion_type\"] - 1"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["4    1141\n", "1     374\n", "2     351\n", "3     230\n", "7     183\n", "0     116\n", "6     115\n", "5     100\n", "Name: Coarse_lesion_type, dtype: int64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["task1_train_df[\"Coarse_lesion_type\"].value_counts()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mT<PERSON> crashed while executing code in the the current cell or a previous cell. Please review the code in the cell(s) to identify a possible cause of the failure. Click <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. View Jupyter <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["# Save pre-training and task1 datasets\n", "pre_train_df.to_csv(\"./annotations/pretrain.csv\", index=False)\n", "task1_train_df.to_csv(\"./annotations/task1_train.csv\", index=False)\n", "\n", "# Split task1_val_test_df into validation and test set\n", "task1_val_df, task1_test_df = train_test_split(task1_val_test_df, test_size=0.5, random_state=21)\n", "\n", "task1_val_df.to_csv(\"./annotations/task1_val.csv\", index=False)\n", "task1_test_df.to_csv(\"./annotations/task1_test.csv\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}