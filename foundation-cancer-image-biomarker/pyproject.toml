# Poetry pyproject.toml: https://python-poetry.org/docs/pyproject/
[build-system]
requires = ["poetry_core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "foundation-cancer-image-biomarker"
version = "1.0.1a8"
description = "Official repo for Foundation Models for Quantitative Biomarker Discovery in Cancer Imaging [INSERT DOI]"
readme = "README.md"
authors = ["Suraj Pai <<EMAIL>>"]
license = "MIT"
repository = "https://github.com/AIM-Harvard/foundation-cancer-image-biomarker"
homepage = "https://aim.hms.harvard.edu/foundation-cancer-image-biomarker"

# Keywords description https://python-poetry.org/docs/pyproject/#keywords
keywords = []  #! Update me
packages = [
    { include = "fmcib" },
]

# Pypi classifiers: https://pypi.org/classifiers/
classifiers = [  #! Update me
  "Development Status :: 3 - Alpha",
  "Intended Audience :: Developers",
  "Operating System :: OS Independent",
  "Topic :: Software Development :: Libraries :: Python Modules",
  "License :: OSI Approved :: MIT License",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
]

[tool.poetry.dependencies]
python = ">=3.9,<3.12"
project-lighter = {version = "0.0.2a19", allow-prereleases = true}
wget = "^3.2"
google-cloud-storage = "^2.9.0"
thedicomsort = "^1.0.1"
dcmrtstruct2nii = "^5"
nibabel = "^5.1.0"
matplotlib = "^3.7.1"
platipy = "0.4.1"
pynrrd = "^1.0.0"
pydicom-seg = "^0.4.1"
pydicom = "^2.4.0"
itk = "^5.3.0"
scipy = "1.10.0"
fonttools = "^4.44.0"
pydantic = "1.10.13"
lightly = "1.4.19"
werkzeug = "^3.0.1"
urllib3 = ">=2.2.2"
aiohttp = "^3.11.12"
pip = "^25.0.1"
tornado = "^6.4.2"
wandb = "^0.16.3"
mpmath = "1.3.0"
zipp = ">=3.19.1"
virtualenv = ">=20.26.6"
setuptools = ">=70.0.0"
sentry-sdk = ">=2.8.0"
scikit-learn = ">=1.5.0"
requests = ">=2.32.2"
mkdocs-material = ">=9.5.32"
certifi = ">=2024.07.04"
sqlalchemy = "^2.0.37"
gunicorn = "^23.0.0"
jinja2 = "^3.1.5"

[tool.poetry.group.dev.dependencies]
black = "^24.4.2"
bandit = "^1.7.7"
isort = {extras = ["colors"], version = "^5.10.1"}
mypy = "^0.910"
mypy-extensions = "^0.4.3"
pre-commit = "^2.15.0"
pydocstyle = "^6.1.1"
pylint = "^2.11.1"
pytest = "^6.2.5"
pyupgrade = "^2.29.1"
safety = "^2.3.5"
coverage = "^6.1.2"
coverage-badge = "^1.1.0"
pytest-html = "^3.1.1"
pytest-cov = "^3.0.0"
aiohttp = "^3.9.3"

[tool.black]
# https://github.com/psf/black
target-version = ["py38"]
line-length = 127
color = true

exclude = '''
/(
    \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
    | env
    | venv
)/
'''

[tool.isort]
# https://github.com/timothycrosley/isort/
py_version = 38
line_length = 127

known_typing = ["typing", "types", "typing_extensions", "mypy", "mypy_extensions"]
sections = ["FUTURE", "TYPING", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
include_trailing_comma = true
profile = "black"
multi_line_output = 3
indent = 4
color_output = true

[tool.mypy]
# https://mypy.readthedocs.io/en/latest/config_file.html#using-a-pyproject-toml-file
python_version = 3.9
pretty = true
show_traceback = true
color_output = true

allow_redefinition = false
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
ignore_missing_imports = true
implicit_reexport = false
no_implicit_optional = true
show_column_numbers = true
show_error_codes = true
show_error_context = true
strict_equality = true
strict_optional = true
warn_no_return = true
warn_redundant_casts = true
warn_return_any = true
warn_unreachable = true
warn_unused_configs = true
warn_unused_ignores = true


[tool.pytest.ini_options]
# https://docs.pytest.org/en/6.2.x/customize.html#pyproject-toml
# Directories that are not visited by pytest collector:
norecursedirs =["hooks", "*.egg", ".eggs", "dist", "build", "docs", ".tox", ".git", "__pycache__"]
doctest_optionflags = ["NUMBER", "NORMALIZE_WHITESPACE", "IGNORE_EXCEPTION_DETAIL"]

# Extra options:
addopts = [
  "--strict-markers",
  "--tb=short",
  "--doctest-modules",
  "--doctest-continue-on-failure",
]
# Markers to categorize tests
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "serial",
]


[tool.coverage.run]
source = ["tests"]

[coverage.paths]
source = "foundation-cancer-image-biomarker"

[coverage.run]
branch = true

[coverage.report]
fail_under = 50
show_missing = true


[tool.pylint.'MESSAGES CONTROL']
max-line-length = 127
disable = """
    too-many-arguments,
    no-else-return,
    missing-module-docstring,
    missing-function-docstring,
    invalid-name
"""
generated-members = "torch.*"

[tool.pylint.master]
fail-under=8

[tool.poetry_bumpversion.file."version.py"]
[tool.poetry_bumpversion.file."fmcib/__init__.py"]

[tool.bandit]
skips = ["B108", "B301", "B614"]