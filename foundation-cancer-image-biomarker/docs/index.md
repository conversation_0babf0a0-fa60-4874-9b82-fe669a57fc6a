---
hide:
  - title
---
# 
<div style="display: flex; flex-direction: column; align-items: center;">
    <img src="assets/Header.png" style="width: 100%;"/>
    <div style="display: flex; justify-content: space-between; width: 100%;">
        <img src="assets/Mhub_image.png" style="width: 50%;"/>
        <img src="assets/Mhub_image2.png" style="width: 50%;"/>
    </div>
</div>


## Documentation Walkthrough

[Getting Started](./getting-started/quick-start.md)<br> This section of the documentation explains how you can install our package and get started with a working example. 

!!! note
    [We also provide quickstart examples that run in a free-cloud based environment](./getting-started/cloud-quick-start.md) (through Google Colab) so you can get familiar with our workflows, without having to download anything on your local machine!!

[Replication Guide](./replication-guide/data.md)<br> If you would like to pre-train a foundation model on your own unannotated data or would like to replicate the training and evaluation from our study, see here. 

[Tutorials](https://github.com/AIM-Harvard/foundation-cancer-image-biomarker/tree/master/tutorials)<br> We provide comprehensive tutorials that use the foundation model for cancer imaging biomarkers and compare against other popularly used methods. If you would like to build your own study using our foundation model, these set of tutorials are highly recommended as the starting point. 

[API Docs](./reference/run) <br> This is for the more advanced user who would like to deep-dive into different methods and classes provided by our package. 

 
## License
This project is licensed under the terms of the `MIT` license.
 See [LICENSE](https://github.com/AIM-Harvard/foundation-cancer-image-biomarker/blob/master/LICENSE) for more details.

## Acknowledgements
Code development, testing, and documentation: [Suraj Pai](), [Ibrahim Hadzic]() <br>
Framework used for building the code: [project-lighter](https://github.com/project-lighter/lighter)

project-lighter was developed internally within our lab by Ibrahim Hadzic and Suraj Pai. 

## Disclaimer
The code and data of this repository are provided to promote reproducible research. They are not intended for clinical care or
commercial use. The software is provided "as is", without warranty of any kind, express or implied, including but not limited to the warranties of merchantability, fitness for a particular purpose and noninfringement. In no event shall the authors or copyright holders be liable for any claim, damages or other liability, whether in an action of contract, tort or otherwise, arising from, out of or in connection with the software or the use or other dealings in the software.

## Code Citation
Please cite the  DOIs if you use our code in your project. 

```bibtex
@software{suraj_pai_2024_10535536,
  author       = {Suraj Pai and
                  Ibrahim Hadzic},
  title        = {{AIM-Harvard/foundation-cancer-image-biomarker: 
                   v0.0.1}},
  month        = jan,
  year         = 2024,
  publisher    = {Zenodo},
  version      = {v0.0.1},
  doi          = {10.5281/zenodo.10535536},
  url          = {https://doi.org/10.5281/zenodo.10535536}
}


```
