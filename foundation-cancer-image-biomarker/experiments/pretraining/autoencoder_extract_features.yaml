trainer:
    _target_: pytorch_lightning.Trainer
    benchmark: True
    max_epochs: 100
    check_val_every_n_epoch: 4
    accelerator: gpu
    devices: 1
    log_every_n_steps: 50
    logger: False
    callbacks:
        - _target_: fmcib.callbacks.SavePredictions
          path: "out.csv" # Note: Change path to the CSV file to save predictions to.
          save_preview_samples: 5
system:
    _target_: lighter.LighterSystem
    batch_size: 32
    pin_memory: True
    num_workers: 6
    model:
      _target_: lighter.utils.model.adjust_prefix_and_load_state_dict
      ckpt_path: /mnt/data1/RadiomicsFoundationModel/checkpoints/autoencoder_pretrain/last.ckpt
      ckpt_to_model_prefix: {"model." : ""} 
      model:
        _target_: fmcib.models.AutoEncoder
        spatial_dims: 3
        in_channels: 1
        out_channels: 1
        num_res_units: 0
        decoder: False
        channels: [128, 512, 128, 256, 1024, 256, 1024]
        strides: [2, 2, 2, 1, 2, 2, 2]
        padding: 2 
        inter_channels: [2048,]

    criterion: null
    optimizers: null
    schedulers: null
    train_dataset: null
    val_dataset: null
    test_dataset: null

    predict_dataset:
        _target_: fmcib.datasets.SSLRadiomicsDataset
        path: "./data/preprocessing/deeplesion/annotations/task1_test.csv" # Note: Change path to the CSV file to extract features from.
        label: "Coarse_lesion_type" # Note: Change the label to "malignancy" for task 2 and "survival" for task 3
        radius: 25
        orient: True # Note: Set orient to False for task 2 and task 3
        resample_spacing: [1, 1, 1]
        enable_negatives: False
        transform:
            _target_: monai.transforms.Compose
            transforms:               
                - _target_: monai.transforms.ToTensor
                - _target_: monai.transforms.AddChannel
                - _target_: monai.transforms.NormalizeIntensity
                  subtrahend: -1024
                  divisor: 3072
                - _target_: monai.transforms.SpatialPad
                  spatial_size: [50, 50, 50]                  