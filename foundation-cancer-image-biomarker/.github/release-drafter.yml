# Release drafter configuration https://github.com/release-drafter/release-drafter#configuration
# Emojis were chosen to match the https://gitmoji.carloscuesta.me/

name-template: "v$NEXT_PATCH_VERSION"
tag-template: "v$NEXT_PATCH_VERSION"

categories:
  - title: ":rocket: Features"
    labels: [enhancement, feature]
  - title: ":wrench: Fixes & Refactoring"
    labels: [bug, refactoring, bugfix, fix]
  - title: ":package: Build System & CI/CD"
    labels: [build, ci, testing]
  - title: ":boom: Breaking Changes"
    labels: [breaking]
  - title: ":pencil: Documentation"
    labels: [documentation]
  - title: ":arrow_up: Dependencies updates"
    labels: [dependencies]

template: |
  ## What’s Changed

  $CHANGES

  ## :busts_in_silhouette: List of contributors

  $CONTRIBUTORS
