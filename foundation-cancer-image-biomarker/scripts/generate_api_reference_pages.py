"""Generate the code reference pages and navigation automatically.
Modified from https://mkdocstrings.github.io/recipes/#automatic-code-reference-pages.
"""

from pathlib import Path

import mkdocs_gen_files

PACKAGE = "fmcib"

# Modules to exclude
EXCLUDE = [
    "fmcib.__init__",
]

nav = mkdocs_gen_files.Nav()

root = Path(__file__).parent.parent
src = root / PACKAGE

# Sort files by depth
paths = sorted(src.rglob("*.py"), key=lambda path: len(path.parts))

for path in paths:
    print(f"Processing {path}")
    module_path = path.relative_to(src).with_suffix("")

    module_py_notation = PACKAGE + "." + ".".join(module_path.parts)
    if module_py_notation in EXCLUDE:
        print(f"Excluding '{module_py_notation}' from the API reference.")
        continue

    doc_path = path.relative_to(src).with_suffix(".md")
    full_doc_path = Path("reference", doc_path)

    parts = (PACKAGE, *module_path.parts)

    if parts[-1] == "__init__":
        parts = parts[:-1]
        doc_path = doc_path.with_name("index.md")
        full_doc_path = full_doc_path.with_name("index.md")
    elif parts[-1] == "__main__":
        continue

    nav[parts] = doc_path.as_posix()

    with mkdocs_gen_files.open(full_doc_path, "w") as fd:
        ident = ".".join(parts)
        fd.write(f"::: {ident}")

    mkdocs_gen_files.set_edit_path(full_doc_path, path.relative_to(root))

with mkdocs_gen_files.open("reference/SUMMARY.md", "w") as nav_file:
    nav_file.writelines(nav.build_literate_nav())
