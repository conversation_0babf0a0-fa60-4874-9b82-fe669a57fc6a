[tool.poetry]
name = "clarion"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "clarion", from = "src"}]

[tool.poetry.dependencies]
python = ">=3.10,<3.12"
foundation-cancer-image-biomarker = {path = "foundation-cancer-image-biomarker", develop = true}

[tool.poetry.group.dev.dependencies]
ruff = "^0.8.0"
nbqa = "^1.7.0"

[tool.ruff]
line-length = 88
target-version = "py310"
exclude = [
    "ai-lung-health-benchmarking/",
    "foundation-cancer-image-biomarker/",
]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # Line too long
    "E402",  # Module level import not at top of file
    "B007",  # Loop control variable not used
    "E741",  # Ambiguous variable name
    "C408",  # Unnecessary dict call
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.ruff.lint.isort]
known-first-party = ["clarion"]

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
